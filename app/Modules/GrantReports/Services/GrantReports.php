<?php

namespace AwardForce\Modules\GrantReports\Services;

use AwardForce\Modules\Entries\Contracts\AttachmentRepository;
use AwardForce\Modules\Forms\Collaboration\Repositories\CollaboratorsRepository;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Services\SubmittableService;
use AwardForce\Modules\GrantReports\Models\GrantReportRepository;

class GrantReports extends SubmittableService
{
    public function __construct(
        AttachmentRepository $attachments,
        protected CollaboratorsRepository $collaborators,
        ValuesService $values,
        GrantReportRepository $grantReports,
    ) {
        parent::__construct($attachments, $collaborators, $values);
        $this->submittable = $grantReports;
    }

    public function formEditRoute(): string
    {
        return 'grant-report.form.edit';
    }
}
