<?php

namespace AwardForce\Modules\GrantReports\Search;

use AwardForce\Library\Search\Columns\ReactiveMarker;
use AwardForce\Library\Search\Filters\ReportFormFilter;
use AwardForce\Library\Search\Filters\SeasonalFilter;
use AwardForce\Library\Search\Filters\TagSearchFilter;
use AwardForce\Library\Search\SeasonalColumnator;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Search\Columns\Links;
use AwardForce\Modules\Entries\Search\Filters\EntryKeywordFilter;
use AwardForce\Modules\Entries\Search\Filters\FieldColumnOrderFilter;
use AwardForce\Modules\Forms\Collaboration\Search\Columns\CollaboratorsCount;
use AwardForce\Modules\Forms\Collaboration\Search\Filters\CollaboratorsCountFilter;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Search\Columns\EntryFields;
use AwardForce\Modules\Forms\Fields\Search\Columns\SearchField;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\GrantReports\Models\GrantReportRepository;
use AwardForce\Modules\GrantReports\Search\Columns\CategoryShortcode;
use AwardForce\Modules\GrantReports\Search\Columns\DueDate;
use AwardForce\Modules\GrantReports\Search\Columns\EntryCategory;
use AwardForce\Modules\GrantReports\Search\Columns\EntryId;
use AwardForce\Modules\GrantReports\Search\Columns\EntrySlug;
use AwardForce\Modules\GrantReports\Search\Columns\EntryStatus;
use AwardForce\Modules\GrantReports\Search\Columns\GrantReportEntrantName;
use AwardForce\Modules\GrantReports\Search\Columns\GrantReportUsersJoinFilter;
use AwardForce\Modules\GrantReports\Search\Columns\ManagerEntryTitle;
use AwardForce\Modules\GrantReports\Search\Columns\ManagerGrantReportActionOverflow;
use AwardForce\Modules\GrantReports\Search\Columns\ManagerReportName;
use AwardForce\Modules\GrantReports\Search\Columns\Status;
use AwardForce\Modules\GrantReports\Search\Columns\SubmittedAt;
use AwardForce\Modules\GrantReports\Search\Enhancers\GrantReportFieldValuesEnhancer;
use AwardForce\Modules\GrantReports\Search\Filters\CategorySearchFilter;
use AwardForce\Modules\GrantReports\Search\Filters\EntryJoinFilter;
use AwardForce\Modules\GrantReports\Search\Filters\EntryStatusSearchFilter;
use AwardForce\Modules\GrantReports\Search\Filters\GrantReportsChapterFilter;
use AwardForce\Modules\GrantReports\Search\Filters\GrantReportStatusFilter;
use AwardForce\Modules\GrantReports\Search\Filters\ManageGrantReportsFieldSearchFilter;
use AwardForce\Modules\GrantReports\Search\Filters\ReportFormFieldsJoinFilter;
use AwardForce\Modules\GrantReports\Search\Filters\ReportFormJoinFilter;
use AwardForce\Modules\Search\FormRelated;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Columns\Updated;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\TranslatedColumnSearchFilter;
use Platform\Search\Filters\TrashedFilter;

class ManageGrantReportsColumnator extends Columnator implements FormRelated
{
    use SeasonalColumnator;

    private ?Collection $fieldColumns = null;
    private ?int $selectedFormId = 0;
    private ?array $entryFormIds = null;

    /**
     * Base columns are those that are static and essentially not columns based off any custom
     * field configuration. For example, a user's first and last name are base columns.
     *
     * @return mixed
     */
    protected function baseColumns()
    {
        return new Columns([
            new ReactiveMarker,
            new ManagerGrantReportActionOverflow(self::key()),
            new ManagerReportName,
            new GrantReportEntrantName,
            new ManagerEntryTitle,
            new EntryCategory,
            new EntryId,
            new EntrySlug,
            new CategoryShortcode,
            new DueDate,
            new Status,
            new EntryStatus,
            new Links,
            new CollaboratorsCount,
            new SubmittedAt,
            Updated::forResource('grant_reports', consumer()->dateLocale()),
            app(EntryFields::class)->setFieldColumns($this->fieldColumns())->setRepository($this->repository()),
        ]);
    }

    /**
     * Return the resource that this columnator represents (such as entries, or users).
     *
     * @return string|null
     */
    public function resource()
    {
        return 'Grants';
    }

    /**
     * All available dependencies for the area.
     */
    public function availableDependencies(Defaults $view): Dependencies
    {
        $dependencies = new Dependencies;
        $columns = $this->columns($view);

        //Filters
        $dependencies->add((new ColumnFilter(...$columns))->with(
            'grant_reports.id',
            'grant_reports.slug',
            'grant_reports.form_id',
            'grant_reports.entry_id',
            'grant_reports.submitted_at',
            'grant_reports.deleted_at',
            'grant_reports.values',
            'grant_reports.values as grant_report_values',
            'grant_reports.due_date',
            'entries.category_id',
            'entries.chapter_id',
            'entries.account_id',
            'users.first_name',
            'users.last_name',
        ));

        $dependencies->add(SeasonalFilter::withInput($this->input, 'grant_reports.season_id'));
        $dependencies->add(new EntryJoinFilter);
        $dependencies->add(new GrantReportUsersJoinFilter);
        $dependencies->add(new ReportFormFilter($this->input));
        $dependencies->add(new ReportFormJoinFilter);
        $dependencies->add(new CollaboratorsCountFilter);

        $dependencies->add(new GroupingFilter('grant_reports.id'));

        $dependencies->add(new GrantReportsChapterFilter($this->input));
        $dependencies->add(new CategorySearchFilter($this->input));
        $dependencies->add(new GrantReportStatusFilter($this->input));
        $dependencies->add(new ReportFormFieldsJoinFilter($this->input));
        $dependencies->add(new EntryStatusSearchFilter($this->input));
        $dependencies->add(new EntryKeywordFilter($this->input['keywords'] ?? '', ['entries.title', 'entries.slug', 'entries.local_id', 'users.email', 'grant_report_users.first_name', 'grant_report_users.last_name']));

        $dependencies->add(new TagSearchFilter($this->input, app(GrantReportRepository::class), [Entry::class, GrantReport::class], [GrantReport::class => 'entry_id']));
        $dependencies->add(FieldColumnOrderFilter::fromColumns($columns, $this->input, 'grant_reports.updated')->uniqueColumn('grant_reports.id'));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns(['my_grant_reports.entry_category', 'grant-reports.category_shortcode']), 'Category', current_account_id()))->setJoinTable('categories')->restrictLanguage($language = consumer()->languageCode()));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns(['report_name']), 'Form', current_account_id()))
            ->setJoinTable('forms')->restrictLanguage(consumer()->languageCode()));

        $dependencies->add(new IncludeFilter(['form', 'entry.category']));

        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(new TrashedFilter($this->input));

        $dependencies->add(ManageGrantReportsFieldSearchFilter::fromInput($this->input));
        $dependencies->add(GrantReportFieldValuesEnhancer::fromColumns($columns));

        return $dependencies;
    }

    /**
     * Each columnator must have a unique key that can be used to identify it when loading from settings.etc.
     */
    public static function key(): string
    {
        return 'manage_grant_reports.search';
    }

    /**
     * All columnators have an export view as well. The export key represents a value that can be used to search
     * for any saved exports for this particular columnator.
     */
    public static function exportKey(): string
    {
        return 'manage_grant_reports.export';
    }

    /**
     * Return the repository to be used for future queries.
     */
    public function repository(): Repository
    {
        return app(GrantReportRepository::class);
    }

    protected function fieldColumns()
    {
        if ($this->fieldColumns) {
            return $this->fieldColumns;
        }

        $grantReportFieldColumns = $this->grantReportFieldColumns();
        $entryFieldColumns = $this->entryFieldColumns();
        $userFieldColumns = $this->userFields('grant_reports.id');

        return $this->fieldColumns = collect(array_merge($grantReportFieldColumns, $entryFieldColumns, $userFieldColumns));
    }

    private function grantReportFieldColumns(): array
    {
        if ($this->formId()) {
            return $this->mapFields(app(FieldRepository::class)->requestCache()->getByFormIdAndResourceWhereNot(
                $this->formId(),
                Field::RESOURCE_FORMS,
                'content'
            ), 'grant_reports.id');
        }

        return $this->mapFields(app(FieldRepository::class)->requestCache()->getForGrantReportsByResourceWhereNot(
            Field::RESOURCE_FORMS,
            ['content'],
            $this->seasonId()), 'grant_reports.id'
        );
    }

    protected function entryFieldColumns(): array
    {
        if (! $this->formId()) {
            return $this->mapFields(
                app(FieldRepository::class)->requestCache()->getForEntriesByResourceWhereNot(
                    Field::RESOURCE_FORMS,
                    ['content'],
                    $this->seasonId()
                ), 'entries.id');
        }

        $fields = collect();
        foreach ($this->entryFormIds() as $formId) {
            $fields = $fields->merge(
                app(FieldRepository::class)->requestCache()->getByFormIdAndResourceWhereNot(
                    $formId,
                    Field::RESOURCE_FORMS,
                    ['content']
                )
            );
        }

        return $fields->count() ? $this->mapFields($fields, 'entries.id') : $fields->all();
    }

    protected function userFields(): array
    {
        return $this->mapFields(
            app(FieldRepository::class)->getUserFieldsByResourceWhereNot(['content'], $this->seasonId()),
            'entries.user_id'
        );
    }

    protected function mapFields(Collection $fields, string $foreignId)
    {
        return $fields->map(fn(Field $field) => SearchField::fromField($field, $foreignId))->all();
    }

    private function formId(): ?int
    {
        if ($this->selectedFormId !== 0) {
            return $this->selectedFormId;
        }

        $formId = Arr::get($this->input, 'form', FormSelector::getId());

        return $this->selectedFormId = ($formId && $formId !== FormSelector::FILTER_ALL ? $formId : null);
    }

    private function entryFormIds(): array
    {
        if (! is_null($this->entryFormIds)) {
            return $this->entryFormIds;
        }

        return $this->entryFormIds = app(FormRepository::class)->getAllForEntriesInExistingGrantReports($this->formId())
            ->just('id');
    }
}
