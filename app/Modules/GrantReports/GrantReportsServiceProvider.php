<?php

namespace AwardForce\Modules\GrantReports;

use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\AIAgents\Domain\Services\ContextResolver;
use AwardForce\Modules\GrantReports\Listeners\DueDateUpdater;
use AwardForce\Modules\GrantReports\Models\EloquentGrantRepository;
use AwardForce\Modules\GrantReports\Models\GrantReportRepository;
use AwardForce\Modules\GrantReports\Services\AIAgent\GrantReportContext;
use AwardForce\Modules\Notifications\Events\SendTimeSettingsUpdated;

class GrantReportsServiceProvider extends ServiceProvider
{
    protected $defer = true;
    protected $repositories = [
        GrantReportRepository::class => EloquentGrantRepository::class,
    ];
    protected $listeners = [
        SendTimeSettingsUpdated::class => [
            DueDateUpdater::class.'@whenSendTimeSettingsUpdated',
        ],
    ];

    public function register(): void
    {
        parent::register();

        $this->app->tag(GrantReportContext::class, ContextResolver::CONTEXT);
    }
}
