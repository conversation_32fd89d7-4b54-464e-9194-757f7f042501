<?php

namespace AwardForce\Modules\GrantReports\Models;

use AwardForce\Modules\Exports\Models\ExportRepository;
use AwardForce\Modules\Forms\Forms\Database\Repositories\SubmittableRepository;
use Platform\Database\Repository;

interface GrantReportRepository extends ExportRepository, Repository, SubmittableRepository
{
    public function getAllByEntry(int $entryId);

    public function getAllByEntries(array $entryIds);

    public function getIdsByEntries(array $entryIds): array;

    public function getForUser(int $userId);

    public function getTrashedForUser(?int $userId = null);

    public function entry(int|array $entryId): self;
}
