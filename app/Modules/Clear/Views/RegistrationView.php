<?php

namespace AwardForce\Modules\Clear\Views;

use AwardForce\Library\Country\Countries;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Agreements\Services\Consent;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Fields\View\VueFields;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Facades\Platform\Strings\Output;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Platform\Tokens\TokenManager;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Translator\Engine;

class RegistrationView extends View
{
    use VueFields;

    public function __construct(
        protected Request $request,
        protected UserRepository $users,
        protected TokenManager $tokens,
        protected Engine $translator,
        private ContentBlockRepository $contentBlocks,
        private RoleRepository $roles,
        private FieldRepository $fieldRepository,
        private SeasonRepository $seasons,
        private SettingRepository $settings,
        private Consent $consent,
        protected ValuesService $values,
        private Countries $countries
    ) {
        VueData::registerTranslations([
            'home.register',
            'home.quick-register',
            'buttons.complete-registration',
            'miscellaneous.optional',
            'miscellaneous.search',
            'miscellaneous.datepicker',
            'miscellaneous.password.hint',
            'files',
            'validation',
            'fields.form',
        ]);
    }

    public function tipsContentBlock()
    {
        if (! $this->role() || ! ($block = $this->role()->formContentBlock)) {
            return [];
        }

        $this->translator->translate($block);

        return $block->toArray() + [
            'title' => $block->title(),
            'content' => Output::html($block->content()),
        ];
    }

    public function defaultRole()
    {
        return $this->roles->getDefault();
    }

    public function defaultRoleSlug(): ?string
    {
        return (string) $this->defaultRole()?->slug ?: null;
    }

    protected function role()
    {
        if ($slug = session()->get('role.requested')) {
            return $this->roles->getBySlug($slug);
        }

        return null;
    }

    public function roleSlug(): ?string
    {
        return (string) $this->role()?->slug ?: null;
    }

    public function isRoleRegistration()
    {
        return session()->has('role.requested');
    }

    public function thirdPartyAuthentication()
    {
        $providers = explode(',', setting('social-authentication', ''));

        return (bool) $this->settings->getValueByKey('enable-3rd-party-authentication') &&
            ! empty(array_intersect($providers, config('awardforce.social-auth.registration')));
    }

    public function agreements(): array
    {
        $agreements = [];
        if ($this->consent->agreementToTermsMissing(\Consumer::user())) {
            $agreements[] = [
                'name' => 'acceptAgreementToTerms',
                'setting' => 'require-agreement-to-terms',
                'label' => Output::html(current_account()->agreementToTerms()),
                'checked' => old('acceptAgreementToTerms'),
            ];
        }

        if ($this->consentToNotificationsAndBroadcastsMissing()) {
            $agreements[] = [
                'name' => 'acceptConsentToNotificationsAndBroadcasts',
                'setting' => 'require-consent-to-notifications-and-broadcasts',
                'label' => Output::html(current_account()->consentToNotificationsAndBroadcasts()),
                'checked' => old('acceptConsentToNotificationsAndBroadcasts'),
            ];
        }

        return $agreements;
    }

    private function consentToNotificationsAndBroadcastsMissing(): bool
    {
        if (\Consumer::isGuest()) {
            return $this->consent->consentToNotificationMissingForGuestUser();
        }

        return $this->consent->consentToNotificationsMissing(\Consumer::user());
    }

    public function user(): ?User
    {
        return \Consumer::user();
    }

    public function fields()
    {
        if ($this->user()?->exists) {
            $fields = $this->values->getMissingUserFieldsForSeason(
                $this->user(),
                current_account()->activeSeason()->id,
                $this->user()->roles->just('id')
            );
        } else {
            $roleId = $this->role()?->id ?: $this->defaultRole()?->id;
            if (! $roleId) {
                return new Collection;
            }

            $fields = $this->fieldRepository->getAllForRole($roleId, $this->seasons->getActiveId());
        }

        return $this->mapForVue(
            $this->translator->shallow($fields),
            $this->user()
        );
    }

    public function invitationToken(): ?string
    {
        return null;
    }

    public function registrationIsOpen(): bool
    {
        return \Consumer::isUser() || $this->settings->getValueByKey('app-site-registration-open');
    }

    public function formAction(): string
    {
        return match (true) {
            \Consumer::isUser() && $this->role() => route('registration.complete.role', $this->roleSlug()),
            \Consumer::isUser() => route('registration.complete'),
            (bool) $this->invitationToken() => route('invitation.process'),
            (bool) (\Consumer::user()?->globalId || \Consumer::user()?->createdByThirdParty()) => route('quick-register'),
            default => route('registration.process')
        };
    }

    public function countries(): array
    {
        return $this->countries->forVue();
    }
}
