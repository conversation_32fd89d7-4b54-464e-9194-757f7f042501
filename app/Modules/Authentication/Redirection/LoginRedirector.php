<?php

namespace AwardForce\Modules\Authentication\Redirection;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Modules\Authentication\Exceptions\NoRedirectorFoundException;
use AwardForce\Modules\Authentication\Redirection\Redirectors\Redirector;
use AwardForce\Modules\Identity\Roles\Models\Role;

class LoginRedirector
{
    /**
     * @var Manager
     */
    private $consumer;

    /**
     * @var Redirector[]
     */
    private $redirectors = [];

    public function __construct(Manager $consumer)
    {
        $this->consumer = $consumer;
    }

    public function registerRedirector(Redirector $redirector)
    {
        $this->redirectors[] = $redirector;
    }

    public function route()
    {
        foreach ($this->redirectors as $redirector) {
            if ($redirector->canRedirect($this->consumer)) {
                return $redirector->route();
            }
        }

        throw new NoRedirectorFoundException;
    }

    public function afterRoleRegistration(Role $role)
    {
        if ($role->hasCompletedContentBlock()) {
            return route('register.role.complete', [$role->slug]);
        }

        if ($role->hasCompletedScoreSet()) {
            return score_set_url($role->completedScoreSet);
        }

        return $this->route();
    }
}
