<?php

namespace AwardForce\Modules\Forms\Forms\Bus;

use AwardForce\Modules\Entries\Contracts\AttachmentRepository;
use AwardForce\Modules\Entries\Contracts\ContributorRepository;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Referees\Contracts\RefereeRepository;
use Platform\Search\HasValues;

class UpdateFieldValue
{
    public function __construct(
        public readonly HasValues $hasValues,
        public readonly string $fieldSlug,
        public $value,
    ) {
    }

    public static function forSubmittable(
        FormRepository $forms,
        string $formSlug,
        string $submittableSlug,
        string $fieldSlug,
        $value
    ): self {
        return new self($forms->requireBySlug($formSlug)->findSubmittable($submittableSlug), $fieldSlug, $value);
    }

    public static function forAttachment(
        AttachmentRepository $attachments,
        int $attachmentId,
        string $fieldSlug,
        $value
    ): self {
        return new self($attachments->requireById($attachmentId), $fieldSlug, $value);
    }

    public static function forAttachmentToken(
        AttachmentRepository $attachments,
        string $fileToken,
        string $fieldSlug,
        $value
    ): self {
        return new self($attachments->getByFileToken($fileToken), $fieldSlug, $value);
    }

    public static function forContributor(
        ContributorRepository $contributors,
        int $contributorId,
        string $fieldSlug,
        $value
    ): self {
        return new self($contributors->requireById($contributorId), $fieldSlug, $value);
    }

    public static function forReferee(
        RefereeRepository $referees,
        int $refereeId,
        string $fieldSlug,
        $value
    ): self {
        return new self($referees->requireById($refereeId), $fieldSlug, $value);
    }
}
