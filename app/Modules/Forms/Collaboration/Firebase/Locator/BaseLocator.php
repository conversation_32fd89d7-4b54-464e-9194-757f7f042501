<?php

namespace AwardForce\Modules\Forms\Collaboration\Firebase\Locator;

use AwardForce\Library\Database\Firebase\Locator;
use AwardForce\Library\Database\Firebase\Path;
use Illuminate\Support\Arr;

abstract readonly class BaseLocator implements Locator
{
    public const COLLECTION = 'lockables';

    public const SEPARATOR = '-';

    abstract protected function documentParts(): array;

    final public function path(string $fieldPath): Path
    {
        return new Path(
            static::COLLECTION,
            Arr::join($this->documentParts(), self::SEPARATOR),
            $fieldPath
        );
    }
}
