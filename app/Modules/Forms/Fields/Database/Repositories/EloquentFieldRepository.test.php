<?php

namespace AwardForce\Modules\Forms\Fields\Database\Repositories;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Entries\Enums\AIFieldTrigger;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\FieldsResource;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\Seasons\Models\Season;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Database\Eloquent\Model;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\IntegratedTestCase;

use function sort;

final class EloquentFieldRepositoryTest extends IntegratedTestCase
{
    private EloquentFieldRepository $repository;

    public function init()
    {
        $this->repository = app(EloquentFieldRepository::class);

        Model::unguard();
    }

    public function testValuesCounter(): void
    {
        $field1 = $this->muffin(Field::class);
        $field2 = $this->muffin(Field::class);

        $this->assertFalse($field1->hasValues);
        $this->assertFalse($field2->hasValues);

        $this->repository->setFieldsHaveValues([$field1->id]);

        $this->assertTrue($field1->fresh()->hasValues);
        $this->assertFalse($field2->fresh()->hasValues);
    }

    public function testUpdateHaveValuesDoesntChangeUpdatedAt(): void
    {
        $field = $this->muffin(Field::class);
        $updatedAtField = $field->updatedAt;

        $this->travel(10)->minutes();
        $this->repository->setFieldsHaveValues([$field->id]);

        $this->assertTrue($updatedAtField->eq($field->fresh()->updatedAt));

        $this->travelBack();
    }

    public function testRetrievalByResource(): void
    {
        $this->setupField(Field::RESOURCE_FORMS, 1, true);
        $this->setupField(Field::RESOURCE_USERS, 2, false);

        $this->assertCount(1, $this->repository->getByResource(Field::RESOURCE_FORMS));
    }

    public function testRequiredRetrieval(): void
    {
        $this->setupField(Field::RESOURCE_ATTACHMENTS, 3, true);
        $this->setupField(Field::RESOURCE_FORMS, 1, true);
        $this->setupField(Field::RESOURCE_ATTACHMENTS, 3, false);

        $this->assertCount(1, $this->repository->getResourceRequired(Field::RESOURCE_ATTACHMENTS));
    }

    public function testGetContactable(): void
    {
        // Damn you FactoryMuffin!! I WILL PURGE YOU FROM OUR CODEBASE ONE DAY!
        $form = $this->muffin(Form::class);
        $this->muffin(Field::class, ['form_id' => $form->id, 'type' => function () {
            return 'text';
        }]);
        $field = $this->muffin(Field::class, ['form_id' => $form->id, 'type' => function () {
            return 'email';
        }]);

        $this->assertEquals(
            $field->id,
            $this->repository->getContactable($field->resource, $form->id, $field->seasonId)->first()->id
        );

        $this->assertEmpty($this->repository->getContactable('random', $form->id, $field->seasonId));
        $this->assertEmpty($this->repository->getContactable($field->resource, $form->id, $field->seasonId + 12));
    }

    public function testGetTextFields(): void
    {
        // Damn you FactoryMuffin!! I WILL PURGE YOU FROM OUR CODEBASE ONE DAY!
        $this->muffin(Field::class, ['type' => function () {
            return 'email';
        }]);
        $field = $this->muffin(Field::class, ['type' => function () {
            return 'text';
        }]);

        $this->assertEquals(
            $field->id,
            $this->repository->getTextFields($field->resource, $field->seasonId)->first()->id
        );

        $this->assertEmpty($this->repository->getTextFields('random', $field->seasonId));
        $this->assertEmpty($this->repository->getTextFields($field->resource, $field->seasonId + 12));
    }

    public function testGetForEntryTabCategoryFiltering(): void
    {
        $categoryA = $this->muffin(Category::class);
        $categoryB = $this->muffin(Category::class);

        $tabInCategoryA = $this->muffin(Tab::class, ['category_option' => 'select']);
        $tabInCategoryA->categories()->attach($categoryA);
        $tabInCategoryB = $this->muffin(Tab::class, ['category_option' => 'select']);
        $tabInCategoryB->categories()->attach($categoryB);
        $tabWithoutCategory = $this->muffin(Tab::class, ['category_option' => 'select']);
        $tabInAllCategories = $this->muffin(Tab::class, ['category_option' => 'all']);
        $deletedTab = $this->muffin(Tab::class, ['deleted_at' => \Carbon\Carbon::now()]);

        $fieldsInCategoryA = $this->muffins(5, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS, 'category_option' => 'select']);
        collect($fieldsInCategoryA)->each(
            function (Field $field) use ($categoryA) {
                $field->categories()->attach($categoryA);
            }
        );

        $fieldsInCategoryB = $this->muffins(5, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS, 'category_option' => 'select']);
        collect($fieldsInCategoryA)->each(
            function (Field $field) use ($categoryB) {
                $field->categories()->attach($categoryB);
            }
        );

        $fieldsInAllCategories = $this->muffins(5, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS, 'category_option' => 'all']);

        /**
         * below there is a setup for fields under different conditions
         * this data will be tested against its visibility in categoryA
         */

        // no field that belongs in deleted tab should be visible - regardles of a category it belongs too
        $fieldsInCategoryA[3]->tab()->associate($deletedTab)->save();
        $fieldsInCategoryB[3]->tab()->associate($deletedTab)->save();
        $fieldsInAllCategories[3]->tab()->associate($deletedTab)->save();

        // no field that belongs to wrong category should be visible - regardless of tab it belongs too
        $fieldsInCategoryB[1]->tab()->associate($tabInCategoryB)->save();
        $fieldsInCategoryB[0]->tab()->associate($tabInCategoryA)->save();
        $fieldsInCategoryB[2]->tab()->associate($tabInAllCategories)->save();
        $fieldsInCategoryB[4]->tab()->associate($tabWithoutCategory)->save();

        // no field in tab that is from wrong category should be visible - regardless of category of field itself
        $fieldsInCategoryA[1]->tab()->associate($tabInCategoryB)->save();
        $fieldsInAllCategories[1]->tab()->associate($tabInCategoryB)->save();

        // to be visible
        //
        // field needs to be in the right category
        $fieldsInCategoryA[0]->tab()->associate($tabInCategoryA)->save();           // and tab needs to be in the right category
        $fieldsInCategoryA[2]->tab()->associate($tabInAllCategories)->save();       // or tab has to be category independent

        //
        // or field needs to be category independent
        $fieldsInAllCategories[0]->tab()->associate($tabInCategoryA)->save();       // and tab needs to be in the right category
        $fieldsInAllCategories[2]->tab()->associate($tabInAllCategories)->save();   // or tab has to be category independent

        //
        // or fields tab needs to be unassigned to category
        $fieldsInCategoryA[4]->tab()->associate($tabWithoutCategory)->save();
        $fieldsInAllCategories[4]->tab()->associate($tabWithoutCategory)->save();

        $fields = $this->repository->getForSubmittable($categoryA->formId, $categoryA->id);
        $this->assertCount(6, $fields);

        $retrieved = $fields->pluck('id')->toArray();
        $expected = [
            $fieldsInCategoryA[0]->id,
            $fieldsInCategoryA[2]->id,
            $fieldsInAllCategories[0]->id,
            $fieldsInAllCategories[2]->id,
            $fieldsInCategoryA[4]->id,
            $fieldsInAllCategories[4]->id,
        ];

        sort($retrieved);
        sort($expected);

        $this->assertSame($retrieved, $expected);
    }

    public function testGetForSubmittable(): void
    {
        $category = $this->muffin(Category::class);
        $this->muffins(3, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS]);
        $this->muffins(2, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS, 'entrant_read_access' => false]);

        $deletedTab = $this->muffin(Tab::class, ['deleted_at' => \Carbon\Carbon::now()]);
        $field = $this->muffin(Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS, 'tab_id' => $deletedTab->id]);

        $fields = $this->repository->getForSubmittable($category->formId, $category->id);

        $this->assertCount(5, $fields);
        $this->assertFalse(in_array($field->id, $fields->just('id')));
    }

    public function testGetForSubmittableNoCategory(): void
    {
        $fields = $this->muffins(3, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS]);
        $this->muffins(2, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS, 'entrant_read_access' => false]);
        $this->muffins(2, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS, 'category_option' => 'select']);

        $deletedTab = $this->muffin(Tab::class, ['deleted_at' => \Carbon\Carbon::now()]);
        $field = $this->muffin(Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS, 'tab_id' => $deletedTab->id]);

        $fields = $this->repository->getForSubmittable($fields[0]->formId, null);

        $this->assertCount(5, $fields);
        $this->assertFalse(in_array($field->id, $fields->just('id')));
    }

    public function testGetWritableForEntrant(): void
    {
        $category = $this->muffin(Category::class);
        $this->muffins(3, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS]);
        $this->muffins(2, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS, 'entrant_write_access' => false]);

        $result = $this->repository->getWritableForEntrant($category->formId, $category->id);
        $this->assertCount(3, $result);
    }

    public function testGetWritableForEntrantNoCategory(): void
    {
        $fields = $this->muffins(3, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS]);
        $this->muffins(2, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS, 'category_option' => 'select']);
        $this->muffins(2, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS, 'entrant_write_access' => false]);

        $result = $this->repository->getWritableForEntrant($fields[0]->formId, null);
        $this->assertCount(3, $result);
    }

    #[TestWith([FieldsResource::Contributors])]
    #[TestWith([FieldsResource::Referees])]
    public function testForFieldResourceTabAndOrderSorted(FieldsResource $fieldResource): void
    {
        $category = $this->muffin(Category::class);
        $resource = $fieldResource->value;
        // Note, out of order to test the order query logic
        $tabTwo = $this->muffin(Tab::class, ['order' => 2]);
        $tabOne = $this->muffin(Tab::class, ['order' => 1]);
        $fieldFour = $this->muffin(Field::class, ['type' => 'text', 'order' => 2, 'tab_id' => $tabTwo->id, 'resource' => $resource]);
        $fieldTwo = $this->muffin(Field::class, ['type' => 'text', 'order' => 4, 'tab_id' => $tabOne->id, 'resource' => $resource]);
        $fieldThree = $this->muffin(Field::class, ['type' => 'text', 'order' => 1, 'tab_id' => $tabTwo->id, 'resource' => $resource]);
        $fieldOne = $this->muffin(Field::class, ['type' => 'text', 'order' => 3, 'tab_id' => $tabOne->id, 'resource' => $resource]);

        $fields = $this->repository->forResourceFormAndCategory($category->formId, $category->id, $fieldResource);
        $this->assertCount(4, $fields);
        $this->assertEquals([$fieldOne->id, $fieldTwo->id, $fieldThree->id, $fieldFour->id], $fields->just('id'));
    }

    #[TestWith([FieldsResource::Contributors])]
    #[TestWith([FieldsResource::Referees])]
    public function testForFieldResourceNoCategory(FieldsResource $fieldResource): void
    {
        // Note, out of order to test the order query logic
        $resource = $fieldResource->value;
        $tabTwo = $this->muffin(Tab::class, ['order' => 2]);
        $tabOne = $this->muffin(Tab::class, ['order' => 1]);
        $fieldFour = $this->muffin(Field::class, ['type' => 'text', 'order' => 2, 'tab_id' => $tabTwo->id, 'resource' => $resource]);
        $fieldTwo = $this->muffin(Field::class, ['type' => 'text', 'order' => 4, 'tab_id' => $tabOne->id, 'resource' => $resource]);
        $fieldThree = $this->muffin(Field::class, ['type' => 'text', 'order' => 1, 'tab_id' => $tabTwo->id, 'resource' => $resource]);
        $fieldOne = $this->muffin(Field::class, ['type' => 'text', 'order' => 3, 'tab_id' => $tabOne->id, 'resource' => $resource]);
        $this->muffin(Field::class, ['type' => 'text', 'order' => 3, 'tab_id' => $tabOne->id, 'resource' => $resource, 'category_option' => 'select']);

        $fields = $this->repository->forResourceFormAndCategory($fieldOne->formId, null, $fieldResource);
        $this->assertCount(4, $fields);
        $this->assertEquals([$fieldOne->id, $fieldTwo->id, $fieldThree->id, $fieldFour->id], $fields->just('id'));
    }

    #[TestWith([FieldsResource::Contributors])]
    #[TestWith([FieldsResource::Referees])]
    public function testForFieldResourceIgnoreDeletedTabs(FieldsResource $fieldResource): void
    {
        $category = $this->muffin(Category::class);
        $resource = $fieldResource->value;
        // Note, out of order to test the order query logic
        $activeTab = $this->muffin(Tab::class);
        $deletedTab = $this->muffin(Tab::class, ['deleted_at' => Carbon::now()]);
        $activeField = $this->muffin(Field::class, ['type' => 'text', 'tab_id' => $activeTab->id, 'resource' => $resource]);
        $ignoredField = $this->muffin(Field::class, ['type' => 'text', 'tab_id' => $deletedTab->id, 'resource' => $resource]);

        $fields = $this->repository->forResourceFormAndCategory($category->formId, $category->id, $fieldResource);
        $this->assertCount(1, $fields);
        $this->assertEquals([$activeField->id], $fields->just('id'));
    }

    public function testGetFieldsForConditionalList(): void
    {
        $type = collect(config('awardforce.fields.conditional_fields'))->random();

        $field = $this->muffin(Field::class, ['type' => function () use ($type) {
            return $type;
        }]);
        $this->muffin(Field::class, ['type' => 'date', 'season_id' => $field->seasonId]);

        $fields = $this->repository->getFieldsForConditionalList($field->seasonId);

        $this->assertCount(1, $fields);
        $this->assertEquals($field->id, $fields->first()->id);
    }

    public function testGetForValidationChecksTabsForEntriesContributors(): void
    {
        $entryFieldWithTab = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]);
        $entryFieldWithoutTab = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'tab_id' => null]);
        $contributorFieldWithTab = $this->muffin(Field::class, ['resource' => Field::RESOURCE_CONTRIBUTORS]);
        $contributorFieldWithoutTab = $this->muffin(Field::class, ['resource' => Field::RESOURCE_CONTRIBUTORS, 'tab_id' => null]);

        $fields = $this->repository->getForValidation($this->season->id, [Field::RESOURCE_FORMS, Field::RESOURCE_CONTRIBUTORS]);

        $this->assertCount(2, $fields);
        $this->assertEquals([$entryFieldWithTab->id, $contributorFieldWithTab->id], $fields->just('id'));
    }

    public function testGetForValidationIgnoresTabsForAttachmentsAndUsers(): void
    {
        $userField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS]);
        $attachmentField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_ATTACHMENTS]);

        $fields = $this->repository->getForValidation($this->season->id, [Field::RESOURCE_USERS, Field::RESOURCE_ATTACHMENTS]);

        $this->assertCount(2, $fields);
        $this->assertEquals([$userField->id, $attachmentField->id], $fields->just('id'));
    }

    public function testGetAllForRole(): void
    {
        $role = $this->muffin(Role::class);

        $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_USERS,
            'role_option' => Field::ROLE_OPTION_ALL,
            'required' => true,
        ]);

        $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_USERS,
            'role_option' => Field::ROLE_OPTION_SELECT,
        ])->roles()->save($role, [
            'read_access' => true,
            'write_access' => true,
            'required' => true,
        ]);

        $fields = $this->repository->getAllForRole($role->id, $this->season->id);

        $this->assertCount(2, $fields);
        $this->assertCount(2, $fields->where('required', true));
        $this->assertArrayHasKey('minVideoLength', $fields->first()->toArray());
        $this->assertArrayHasKey('maxVideoLength', $fields->first()->toArray());
        $this->assertArrayHasKey('imageDimensionConstraints', $fields->first()->toArray());
    }

    public function testUserFieldsCanPersistAcrossSeasons(): void
    {
        $pastSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        $persistentField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $pastSeason->id, 'seasonal' => false]);
        $currentField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS]);

        $fields = $this->repository->getAllBySeasonAndResource($this->season->id, Field::RESOURCE_USERS);

        $this->assertCount(2, $fields);
        $this->assertContains($persistentField->id, $ids = $fields->just('id'));
        $this->assertContains($currentField->id, $ids);
    }

    private function setupField($resource, $order, $required)
    {
        $field = new Field;
        $field->seasonId = $this->season->id;
        $field->resource = $resource;
        $field->formId = FormSelector::getId();
        $field->order = $order;
        $field->type = 'textarea';
        $field->searchable = false;
        $field->required = $required;

        $this->repository->save($field);

        return $field;
    }

    public function testForceDeleteAllFromSeason(): void
    {
        $deleting = $this->muffin(Field::class);
        $safe = $this->muffin(Field::class, ['season_id' => $this->muffin(Season::class)->id]);

        $this->repository->forceDeleteAllFromSeason($deleting->seasonId);

        $this->assertNull(Field::withTrashed()->find($deleting->id));
        $this->assertNotNull(Field::find($safe->id));
    }

    public function testGetFieldsOfType(): void
    {
        $this->muffin(Field::class, ['type' => function () {
            return Field::TYPE_TABLE;
        }]);
        $field = $this->muffin(Field::class, ['type' => function () {
            return Field::TYPE_FILE;
        }]);

        $this->assertEquals(
            $field->id,
            $this->repository->getFieldsOfType($field->resource, Field::TYPE_FILE, $field->seasonId)->first()->id
        );
    }

    public function testGetForAttachmentTypeFieldAndForm(): void
    {
        $this->muffin(Field::class);

        $form = $this->muffin(Form::class);

        $field = $this->muffin(Field::class, [
            'formId' => $form->id,
            'type' => function () {
                return 'radio';
            },
            'resource' => Field::RESOURCE_ATTACHMENTS,
            'protection' => Field::PROTECTION_STANDARD,
        ]);

        $fields = $this->repository->getForAttachmentTypeFieldAndForm($form);

        $this->assertCount(1, $fields);
        $this->assertEquals($field->id, $fields->first()->id);
    }

    public function testGetAllByFormAndResource(): void
    {
        $form = $this->muffin(Form::class);
        $tab = $this->muffin(Tab::class);

        $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_USERS,
        ]);

        $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_FORMS,
        ]);

        $entryField = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_FORMS,
            'form_id' => $form->id,
            'tab_id' => $tab->id,
        ]);

        $fields = $this->repository->getAllByFormAndResource($form, [Field::RESOURCE_USERS, Field::RESOURCE_FORMS]);
        $entryFields = $fields->where('resource', Field::RESOURCE_FORMS);
        $userFields = $fields->where('resource', Field::RESOURCE_USERS);

        $this->assertCount(2, $fields);
        $this->assertCount(1, $entryFields);
        $this->assertCount(1, $userFields);
        $this->assertEquals($entryField->id, $entryFields->first()->id);
    }

    public function testGetAllByFormAndResourceExcludesNonPersistentUserFieldsFromOtherSeasons(): void
    {
        $otherSeason = $this->muffin(Season::class);

        $persistentUserField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $otherSeason, 'seasonal' => false]);
        $otherSeasonUserField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $otherSeason, 'seasonal' => true]);

        $otherAccount = $this->muffin(Account::class);
        $otherAccountPersistentUserField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'account_id' => $otherAccount->id, 'seasonal' => false]);

        $form = $this->muffin(Form::class);

        $thisSeasonUserField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'season_id' => $form->seasonId, 'seasonal' => true]);

        $fields = $this->repository->getAllByFormAndResource($form, Field::RESOURCE_USERS);

        $this->assertCount(2, $ids = $fields->just('id'));

        $this->assertContains($persistentUserField->id, $ids);
        $this->assertContains($thisSeasonUserField->id, $ids);

        $this->assertNotContains($otherSeasonUserField->id, $ids);
        $this->assertNotContains($otherAccountPersistentUserField->id, $ids);
    }

    public function testGetSearchableTypesForForm(): void
    {
        $form = $this->muffin(Form::class);

        $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_FORMS,
            'type' => function () {
                return 'checkbox';
            },
        ]);

        $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_FORMS,
            'type' => function () {
                return 'file';
            },
            'form_id' => $form->id,
        ]);

        $field = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_FORMS,
            'type' => function () {
                return 'checkbox';
            },
            'form_id' => $form->id,
        ]);

        $fields = $this->repository->getSearchableTypesForForm($form, Field::RESOURCE_FORMS);

        $this->assertCount(1, $fields);
        $this->assertEquals($field->id, $fields->first()->id);
    }

    public function testVisibilityInCategories(): void
    {
        $categories = $this->muffins(5, Category::class);
        $tab1 = $this->muffin(Tab::class, ['category_option' => 'all']);
        $tab2 = $this->muffin(Tab::class, ['category_option' => 'select']);
        $tab2->categories()->sync([$categories[0]->id]);
        $tab3 = $this->muffin(Tab::class, ['category_option' => 'select']);
        $tab3->categories()->sync([$categories[1]->id]);

        $tabNotInCategories = $this->muffin(Tab::class, ['category_option' => 'select']);
        $tabNotInCategories->categories()->sync([$this->muffin(Category::class)->id]);

        $field1 = $this->muffin(Field::class, ['category_option' => 'all', 'tab_id' => $tab1->id, 'resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return Field::TYPE_FILE;
        }]);
        $field2 = $this->muffin(Field::class, ['category_option' => 'select', 'tab_id' => $tab1->id, 'resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return Field::TYPE_FILE;
        }]);
        $field2->categories()->sync([$categories[2]->id]);
        $field3 = $this->muffin(Field::class, ['category_option' => 'select', 'tab_id' => $tab1->id, 'resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return Field::TYPE_FILE;
        }]);
        $field3->categories()->sync([$categories[3]->id]);

        $field4 = $this->muffin(Field::class, ['category_option' => 'all', 'tab_id' => $tab2->id, 'resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return Field::TYPE_FILE;
        }]);
        $field5 = $this->muffin(Field::class, ['category_option' => 'all', 'tab_id' => $tab3->id, 'resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return Field::TYPE_FILE;
        }]);

        $field6 = $this->muffin(Field::class, ['category_option' => 'all', 'tab_id' => $tabNotInCategories->id, 'resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return Field::TYPE_FILE;
        }]);

        $field7 = $this->muffin(Field::class, ['category_option' => 'select', 'tab_id' => $tab1->id, 'resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return Field::TYPE_FILE;
        }]);
        $field7->categories()->sync([$this->muffin(Category::class)->id]);

        $results = $this->repository->visibilityInCategories([$field1->id, $field2->id, $field3->id, $field4->id, $field5->id], $categoryIds = collect($categories)->pluck('id')->toArray());

        // Field 1 applies to all categories
        $this->assertArrayHasKey((string) $field1->slug, $results);
        $this->assertCount(5, $results[(string) $field1->slug]);

        // Field 2 applies only to $categories[2]
        $this->assertArrayHasKey((string) $field2->slug, $results);
        $this->assertCount(1, $results[(string) $field2->slug]);
        $this->assertEquals($categories[2]->id, $results[(string) $field2->slug][0]);

        // Field 3 applies only to $categories[3]
        $this->assertArrayHasKey((string) $field3->slug, $results);
        $this->assertCount(1, $results[(string) $field3->slug]);
        $this->assertEquals($categories[3]->id, $results[(string) $field3->slug][0]);

        // Field 4 applies only to $categories[0] through Tab 2
        $this->assertArrayHasKey((string) $field4->slug, $results);
        $this->assertCount(1, $results[(string) $field4->slug]);
        $this->assertEquals($categories[0]->id, $results[(string) $field4->slug][0]);

        // Field 5 applies only to $categories[1] through Tab 3
        $this->assertArrayHasKey((string) $field5->slug, $results);
        $this->assertCount(1, $results[(string) $field5->slug]);
        $this->assertEquals($categories[1]->id, $results[(string) $field5->slug][0]);

        $this->assertArrayNotHasKey((string) $field6->slug, $results);
        $this->assertArrayNotHasKey((string) $field7->slug, $results);
    }

    public function testGetOptionable(): void
    {
        $form = $this->muffin(Form::class);

        $this->muffin(Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return 'drop-down-list';
        }]);

        $this->muffin(Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return 'checkboxlist';
        }]);

        $this->muffin(Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return 'radio';
        }]);

        $this->muffin(Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return 'text';
        }]);

        $this->muffin(Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_USERS, 'type' => function () {
            return 'radio';
        }]);

        $results = $this->repository->getOptionable($form->id, Field::RESOURCE_FORMS);

        $this->assertCount(3, $results);
        $this->assertEmpty($results->filter(function (Field $field) {
            return ! $field->optionable() || $field->resource !== Field::RESOURCE_FORMS;
        }));
    }

    public function testCountFieldsOfType(): void
    {
        $this->muffins(2, Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return Field::TYPE_FILE;
        }]);

        $this->muffins(2, Field::class, ['resource' => Field::RESOURCE_CONTRIBUTORS, 'type' => function () {
            return Field::TYPE_FILE;
        }]);

        $this->muffins(2, Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return Field::TYPE_TABLE;
        }]);

        $this->assertEquals(2, $this->repository->countFieldsOfType(Field::RESOURCE_FORMS, Field::TYPE_FILE));
    }

    public function testItFiltersCorrectFiletypes(): void
    {
        $tab = $this->muffin(Tab::class);
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'file_types' => ['doc', 'docx', 'doc-bad', 'docx-bad'], 'type' => function () {
            return Field::TYPE_FILE;
        }]);
        $field->tab()->associate($tab)->save();

        $results = $this->repository->forTab($tab);

        /** @var Field $fetchedField */
        $fetchedField = $results->first();

        $this->assertFalse(in_array('doc-bad', $fetchedField->file_types));
        $this->assertFalse(in_array('docx-bad', $fetchedField->file_types));

        $this->assertTrue(in_array('doc', $fetchedField->file_types));
        $this->assertTrue(in_array('docx', $fetchedField->file_types));
    }

    public function testGetAllBySlugsForTypes(): void
    {
        $field1 = $this->muffin(Field::class, ['type' => fn() => 'text']);
        $field2 = $this->muffin(Field::class, ['type' => fn() => 'checkbox']);

        // No types passed
        $this->assertCount(2, $this->repository->getAllBySlugsForTypes([(string) $field1->slug, (string) $field2->slug]));

        $this->assertCount(1, $result = $this->repository->getAllBySlugsForTypes([(string) $field1->slug, (string) $field2->slug], ['checkbox']));
        $this->assertEquals((string) $result->first()->slug, (string) $field2->slug);
    }

    public function testGetAllScoreableWithScores(): void
    {
        $this->muffin(Field::class, ['type' => fn() => 'text']);
        $field2 = $this->muffin(Field::class, ['type' => fn() => 'checkbox']);
        $formId = FormSelector::getId();

        $this->assertCount(0, $this->repository->getAllScoreableWithScores($formId));
        $this->assertEquals(0, $this->repository->countAllScoreableWithScores($formId));

        $field2->autoScoring = 1;
        $field2->save();

        $this->assertCount(1, $this->repository->getAllScoreableWithScores($formId));
        $this->assertEquals(1, $this->repository->countAllScoreableWithScores($formId));

        $field2->seasonId = ($season = $this->muffin(Season::class))->id;
        $field2->save();

        $this->assertCount(1, $this->repository->getAllScoreableWithScores($formId));
        $this->assertEquals(1, $this->repository->countAllScoreableWithScores($formId));

        $this->assertCount(0, $this->repository->getAllScoreableWithScores($formId, $this->season->id));
        $this->assertEquals(0, $this->repository->countAllScoreableWithScores($formId, $this->season->id));

        $this->assertCount(1, $this->repository->getAllScoreableWithScores($formId, $season->id));
        $this->assertEquals(1, $this->repository->countAllScoreableWithScores($formId, $season->id));
    }

    public function testGetAllScoreableWithTitleTranslatedByForm(): void
    {
        $this->muffin(Field::class, ['type' => 'text']);
        $field = $this->muffin(Field::class, ['type' => 'checkbox']);
        $formId = FormSelector::getId();

        $this->assertCount(0, $this->repository->getAllScoreableWithTitleTranslatedByForm($formId));

        $field->autoScoring = 1;
        $field->save();

        $this->assertCount(1, $this->repository->getAllScoreableWithTitleTranslatedByForm($formId));

        $field->seasonId = ($season = $this->muffin(Season::class))->id;
        $field->save();

        $this->assertCount(1, $this->repository->getAllScoreableWithTitleTranslatedByForm($formId));

        $this->assertArrayHasKey('slug', $this->repository->getAllScoreableWithTitleTranslatedByForm($formId)->first());
        $this->assertArrayHasKey('type', $this->repository->getAllScoreableWithTitleTranslatedByForm($formId)->first());
        $this->assertArrayHasKey('options', $this->repository->getAllScoreableWithTitleTranslatedByForm($formId)->first());
        $this->assertArrayHasKey('title', $this->repository->getAllScoreableWithTitleTranslatedByForm($formId)->first());
    }

    public function testGetAllScoreableWithLabelTranslatedByFormForCategoryAndTab(): void
    {
        [$category1, $category2] = $this->muffins(2, Category::class);
        $tab = $this->muffin(Tab::class, ['category_option' => 'select']);
        $this->muffin(Field::class, ['type' => 'text']);
        $field = $this->muffin(Field::class, ['type' => 'checkbox']);
        $field1 = $this->muffin(Field::class, ['type' => 'checkbox', 'form_id' => $field->formId, 'category_option' => 'select']);
        $field2 = $this->muffin(Field::class, ['type' => 'checkbox', 'form_id' => $field->formId, 'tab_id' => $tab->id]);
        $this->repository->syncCategories($field1, [$category1->id]);
        app(TabRepository::class)->syncCategories($tab, [$category2->id]);
        $formId = FormSelector::getId();

        $this->assertCount(0, $this->repository->getAllScoreableWithTitleTranslatedByForm($formId));

        $field->autoScoring = 1;
        $field1->autoScoring = 1;
        $field2->autoScoring = 1;
        $field->save();
        $field1->save();
        $field2->save();

        $this->assertCount(3, $this->repository->getAllScoreableWithTitleTranslatedByForm($formId));

        $fields = $this->repository->getAllScoreableWithTitleTranslatedByForm($formId, $category1->id);
        $this->assertCount(2, $fields);

        $this->assertArrayHasKey((string) $field->slug, $slugs = $fields->pluck('slug', 'slug')->toArray());
        $this->assertArrayHasKey((string) $field1->slug, $slugs);

        $fields = $this->repository->getAllScoreableWithTitleTranslatedByForm($formId, $category2->id);
        $this->assertCount(2, $this->repository->getAllScoreableWithTitleTranslatedByForm($formId, $category2->id));
        $this->assertArrayHasKey((string) $field->slug, $slugs = $fields->pluck('slug', 'slug')->toArray());
        $this->assertArrayHasKey((string) $field2->slug, $slugs);
    }

    public function testGetAllScoreableWithLabelTranslatedReturnsCorrectOrder(): void
    {
        $tab2 = $this->muffin(Tab::class, ['order' => 2]);
        $tab1 = $this->muffin(Tab::class, ['order' => 1]);
        $formId = FormSelector::getId();

        $field4 = $this->muffin(
            Field::class,
            ['type' => 'checkbox', 'auto_scoring' => 1, 'tab_id' => $tab2->id, 'order' => 2]
        );
        $field3 = $this->muffin(
            Field::class,
            ['type' => 'checkbox', 'auto_scoring' => 1, 'tab_id' => $tab2->id, 'order' => 1]
        );
        $field2 = $this->muffin(
            Field::class,
            ['type' => 'checkbox', 'auto_scoring' => 1, 'tab_id' => $tab1->id, 'order' => 2]
        );
        $field1 = $this->muffin(
            Field::class,
            ['type' => 'checkbox', 'auto_scoring' => 1, 'tab_id' => $tab1->id, 'order' => 1]
        );

        $this->assertCount(4, $this->repository->getAllScoreableWithTitleTranslatedByForm($formId));

        $this->assertEquals(
            [
                $field1->id,
                $field2->id,
                $field3->id,
                $field4->id,
            ],
            $this->repository->getAllScoreableWithTitleTranslatedByForm($formId)->pluck('id')->toArray()
        );
    }

    public function testGetAllBySlugsWithTitleTranslatedPreferred(): void
    {
        $field1 = $this->muffin(Field::class);

        $results = $this->repository->getAllBySlugsWithTitleTranslated([$field1->slug]);
        $this->assertEquals($results[0]['title'], $field1->translated[default_language_code()]['title']);
    }

    public function testGetAllBySlugsWithTitleTranslatedPreferredIWithOtherLang(): void
    {
        $field1 = $this->muffin(Field::class);
        $field1->saveTranslation('el_GR', 'title', 'Greece field', current_account_id());

        $results = $this->repository->getAllBySlugsWithTitleTranslated([$field1->slug]);
        $this->assertEquals($results[0]['title'], $field1->translated[default_language_code()]['title']);
    }

    public function testGetAllBySlugsWithTitleTranslatedFallbackWithPreferred(): void
    {
        $field1 = $this->muffin(Field::class);
        $field1->saveTranslation('el_GR', 'title', 'Greece field', current_account_id());

        \Consumer::shouldReceive('languageCode')->andReturn('el_GR');
        $results = $this->repository->getAllBySlugsWithTitleTranslated([$field1->slug]);
        $this->assertEquals($results[0]['title'], 'Greece field');
    }

    public function testGetByResourceWhereNot(): void
    {
        $seasonId = $this->muffin(Season::class)->id;

        $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'seasonId' => $seasonId]);
        $this->muffin(Field::class, ['type' => 'content', 'resource' => Field::RESOURCE_FORMS, 'seasonId' => $seasonId]);
        $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'seasonId' => $seasonId]);

        $results = $this->repository->getByResourceWhereNot(Field::RESOURCE_FORMS, ['content'], $seasonId);

        $this->assertCount(1, $results);
    }

    public function testGetByResourceWhereFieldTypeNotAndFormTypeIsNotReport(): void
    {
        $seasonId = $this->muffin(Season::class)->id;
        $form = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);

        $entryField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'seasonId' => $seasonId]);
        $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'seasonId' => $seasonId, 'formId' => $form->id]);
        $this->muffin(Field::class, ['type' => 'content', 'resource' => Field::RESOURCE_FORMS, 'seasonId' => $seasonId]);
        $userField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'seasonId' => $seasonId]);

        $entryFields = $this->repository->getByResourceWhereFieldTypeNotAndFormTypeIsNotReport(Field::RESOURCE_FORMS, ['content'], $seasonId);
        $this->assertCount(1, $entryFields);
        $this->assertContains($entryField->id, $entryFields->pluck('id'));

        $userFields = $this->repository->getByResourceWhereFieldTypeNotAndFormTypeIsNotReport(Field::RESOURCE_USERS, ['content'], $seasonId);
        $this->assertCount(1, $userFields);
        $this->assertContains($userField->id, $userFields->pluck('id'));
    }

    public function testGetUserFieldsByResourceWhereNot(): void
    {
        $seasonId = $this->muffin(Season::class)->id;

        $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'seasonId' => $seasonId]);
        $this->muffin(Field::class, ['type' => 'content', 'resource' => Field::RESOURCE_FORMS, 'seasonId' => $seasonId]);
        $userField1 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'seasonal' => false]);
        $userField2 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'seasonal' => true, 'seasonId' => $seasonId]);
        $this->muffin(Field::class, ['type' => 'content', 'resource' => Field::RESOURCE_USERS, 'seasonal' => true, 'seasonId' => $seasonId]);

        $results = $this->repository->getUserFieldsByResourceWhereNot(['content'], $seasonId);

        $this->assertCount(2, $results);
        $this->assertTrue(in_array($userField1->id, $results->pluck('id')->toArray()));
        $this->assertTrue(in_array($userField2->id, $results->pluck('id')->toArray()));
    }

    public function testGetForEntriesByResourceWhereNot(): void
    {
        $seasonId = $this->muffin(Season::class)->id;
        $reportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);

        $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'seasonId' => $seasonId]);
        $field2 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'seasonId' => $seasonId]);
        $field2->formId = $reportForm->id;
        $field2->save();
        $this->muffin(Field::class, ['type' => 'content', 'resource' => Field::RESOURCE_FORMS, 'seasonId' => $seasonId]);

        $results = $this->repository->getForEntriesByResourceWhereNot(Field::RESOURCE_FORMS, ['content'], $seasonId);

        $this->assertCount(1, $results);
    }

    public function testGetByIdsAndResourceWhereNot(): void
    {
        $seasonId = $this->muffin(Season::class)->id;

        $fieldId1 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'seasonId' => $seasonId])->id;
        $fieldId2 = $this->muffin(Field::class, ['type' => 'content', 'resource' => Field::RESOURCE_FORMS, 'seasonId' => $seasonId])->id;
        $fieldId3 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'seasonId' => $seasonId])->id;
        $fieldId4 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'seasonId' => $seasonId])->id;

        $results = $this->repository->getByIdsAndResourceWhereNot([$fieldId1, $fieldId2, $fieldId3], Field::RESOURCE_FORMS, ['content'], $seasonId);

        $this->assertCount(1, $results);
    }

    public function testGetByFormIdAndResourceWhereNot(): void
    {
        $seasonId = $this->muffin(Season::class)->id;
        $form1 = $this->muffin(Form::class);
        $form2 = $this->muffin(Form::class);

        $field1Id = $this->muffin(
            Field::class,
            [
                'resource' => Field::RESOURCE_FORMS,
                'seasonId' => $seasonId,
                'form_id' => $form1->id,
            ]
        )->id;
        $field2Id = $this->muffin(
            Field::class,
            [
                'type' => 'content',
                'resource' => Field::RESOURCE_FORMS,
                'seasonId' => $seasonId,
                'form_id' => $form2->id,
            ]
        )->id;
        $field3Id = $this->muffin(
            Field::class,
            ['resource' => Field::RESOURCE_USERS, 'seasonId' => $seasonId, 'form_id' => $form2->id]
        );

        $results = $this->repository->getByFormIdAndResourceWhereNot(
            $form1->id,
            Field::RESOURCE_FORMS,
            ['content']
        );

        $this->assertCount(1, $results);
        $this->assertEquals($results->first()->id, $field1Id);
    }

    public function testGetSearchableFields(): void
    {
        $fieldSearchableBySeason1 = $this->muffin(Field::class, ['searchable' => true, 'season_id' => $this->season->id]);
        $fieldSearchableBySeason2 = $this->muffin(Field::class, ['searchable' => true, 'season_id' => $this->season->id]);
        $fieldSearchableById1 = $this->muffin(Field::class);

        $searchableBySeason = $this->repository->getSearchableFields([], $this->season->id);
        $this->assertCount(2, $searchableBySeason);

        $searchableByIds = $this->repository->getSearchableFields([
            'fieldIds' => [$fieldSearchableBySeason1->id, $fieldSearchableById1->id],
        ], $this->season->id);
        $this->assertCount(2, $searchableByIds);

        $searchableByIds = $this->repository->getSearchableFields([
            'fieldIds' => [$fieldSearchableById1->id],
        ], $this->season->id);
        $this->assertCount(1, $searchableByIds);
        $this->assertEquals($fieldSearchableById1->id, ($searchableByIds[0])->id);
    }

    public function testGetAllFormulaCompatible(): void
    {
        $compatibleField = $this->muffin(Field::class, ['type' => 'checkbox']);
        $incompatibleField = $this->muffin(Field::class, ['type' => 'table']);

        $result = $this->repository->getAllFormulaCompatible();

        $this->assertCount(1, $result);
        $this->assertTrue($result->contains($compatibleField));
        $this->assertFalse($result->contains($incompatibleField));
    }

    public function testGetMissingRequiredUserFieldsForSeason(): void
    {
        $this->markTestSkipped('Covered through RegistrationViewTest');
    }

    public function testSlugsBuilderMethod(): void
    {
        $field1 = $this->muffin(Field::class);
        $field2 = $this->muffin(Field::class);
        $this->muffin(Field::class);

        $results = $this->repository->slugs([(string) $field1->slug, (string) $field2->slug])
            ->fields(['slug'])
            ->get();

        $this->assertCount(2, $results);
        $this->assertEqualsCanonicalizing(
            [(string) $field1->slug, (string) $field2->slug],
            $results->pluck('slug')->toArray()
        );
    }

    public function testFormBuilderMethod(): void
    {
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_AI]);
        $this->muffin(Field::class, ['type' => Field::TYPE_FORMULA]);

        $results = $this->repository->type(Field::TYPE_AI)
            ->fields(['type'])
            ->get();

        $this->assertCount(1, $results);
        $this->assertSame($field->type, $results->first()->type);
    }

    public function testTriggersContainBuilderMethod(): void
    {
        $field1 = $this->muffin(Field::class, [
            'type' => Field::TYPE_AI,
            'configuration' => Json::encode([
                'triggers' => [AIFieldTrigger::EntrySubmitted->value, AIFieldTrigger::ManualOnDemand->value],
            ]),
        ]);

        $this->muffin(Field::class);

        $this->muffin(Field::class, [
            'type' => Field::TYPE_AI,
            'configuration' => Json::encode([
                'triggers' => [AIFieldTrigger::ManualOnDemand->value],
            ]),
        ]);

        $results = $this->repository
            ->hasTrigger(AIFieldTrigger::EntrySubmitted->value)
            ->fields(['id'])
            ->get();

        $this->assertCount(1, $results);
        $this->assertEquals($field1->id, $results->first()->id);
    }

    public function testForEligibilityTabBuilderMethod(): void
    {
        $tab1 = $this->muffin(Tab::class, ['order' => 5]);
        $tab2 = $this->muffin(Tab::class, ['order' => 10]);
        $tab3 = $this->muffin(Tab::class, ['order' => 15]);
        $field1 = $this->muffin(Field::class, ['tab_id' => $tab1->id]);
        $field2 = $this->muffin(Field::class, ['tab_id' => $tab2->id]);
        $field3 = $this->muffin(Field::class, ['tab_id' => $tab3->id]);

        $results = $this->repository->forEligibilityTab($tab1)
            ->fields(['id'])
            ->get();
        $this->assertCount(1, $results);
        $this->assertEquals($field1->id, $results->first()->id);

        $results = $this->repository->forEligibilityTab($tab2)
            ->fields(['id'])
            ->get();

        $this->assertCount(2, $results);
        $this->assertEqualsCanonicalizing(
            [$field1->id, $field2->id],
            $results->pluck('id')->toArray()
        );

        $results = $this->repository->forEligibilityTab($tab3)
            ->fields(['id'])
            ->get();

        $this->assertCount(3, $results);
        $this->assertEqualsCanonicalizing(
            [$field1->id, $field2->id, $field3->id],
            $results->pluck('id')->toArray()
        );
    }

    public function testScoreableBuilderMethod(): void
    {
        $field1 = $this->muffin(Field::class, ['auto_scoring' => 1, 'type' => 'checkbox']);
        $field2 = $this->muffin(Field::class, ['auto_scoring' => 1, 'type' => 'checkboxlist']);
        $this->muffin(Field::class, ['auto_scoring' => 0, 'type' => 'drop-down-list']);

        $results = $this->repository->scoreable()
            ->fields(['id'])
            ->get();

        $this->assertCount(2, $results);
        $this->assertEqualsCanonicalizing(
            [$field1->id, $field2->id],
            $results->pluck('id')->toArray()
        );

        $results = $this->repository->scoreable(false)
            ->fields(['id'])
            ->get();

        $this->assertCount(3, $results);
    }

    public function testItCanBeFilteredBySeason(): void
    {
        $field = $this->muffin(Field::class, ['season_id' => $seasonId = $this->muffin(Season::class)->id]);
        $this->muffin(Field::class);

        $results = $this->repository->season($seasonId)
            ->fields(['id'])
            ->get();

        $this->assertCount(1, $results);
        $this->assertContains($field->id, $results->pluck('id'));
    }

    public function testItCanBeFilteredByResource(): void
    {
        $field1 = $this->muffin(Field::class, ['resource' => $resource1 = Field::RESOURCE_FORMS]);
        $field2 = $this->muffin(Field::class, ['resource' => $resource2 = Field::RESOURCE_CONTRIBUTORS]);
        $this->muffin(Field::class, ['resource' => Field::RESOURCE_ATTACHMENTS]);

        $results = $this->repository->resource([$resource1, $resource2])
            ->fields(['id'])
            ->get();

        $this->assertCount(2, $results);
        $this->assertContains($field1->id, $results->pluck('id'));
        $this->assertContains($field2->id, $results->pluck('id'));
    }

    public function testGetForGrantReportsByResourceWhereNot(): void
    {
        $seasonId = $this->muffin(Season::class)->id;
        $reportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);

        $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'season_id' => $seasonId]);
        $this->muffins(2, Field::class, ['type' => 'text', 'resource' => Field::RESOURCE_FORMS, 'form_id' => $reportForm->id, 'season_id' => $seasonId]);
        $this->muffin(Field::class, ['type' => 'content', 'resource' => Field::RESOURCE_FORMS, 'form_id' => $reportForm->id, 'season_id' => $seasonId]);

        $results = $this->repository->getForGrantReportsByResourceWhereNot(Field::RESOURCE_FORMS, 'content', $seasonId);

        $this->assertCount(2, $results);
    }

    public function testCategoryBuilderQueryMethod(): void
    {
        $category1 = $this->muffin(Category::class);
        $category2 = $this->muffin(Category::class);
        $category3 = $this->muffin(Category::class);

        $field1 = $this->muffin(Field::class, ['category_option' => 'all']);
        $field2 = $this->muffin(Field::class, ['category_option' => 'select']);
        $field3 = $this->muffin(Field::class, ['category_option' => 'select']);

        $field2->categories()->sync([$category2->id]);
        $field3->categories()->sync([$category3->id]);

        $results = $this->repository->category($category1->id)
            ->fields(['fields.id'])
            ->get();

        $this->assertCount(1, $results);
        $this->assertEquals($field1->id, $results->first()->id);

        $results = $this->repository->category($category2->id)
            ->fields(['fields.id'])
            ->get();

        $this->assertCount(2, $results);
        $this->assertEqualsCanonicalizing(
            [$field1->id, $field2->id],
            $results->pluck('id')->toArray()
        );
    }

    public function testTabSortQueryBuilderMethod(): void
    {
        $tab1 = $this->muffin(Tab::class, ['order' => 3, 'deleted_at' => null, 'category_option' => 'all']);
        $tab2 = $this->muffin(Tab::class, ['order' => 2, 'deleted_at' => null, 'category_option' => 'select']);
        $tab3 = $this->muffin(Tab::class, ['order' => 1, 'deleted_at' => now(), 'category_option' => 'all']);

        $field1 = $this->muffin(Field::class, ['tab_id' => $tab1->id, 'order' => 1]);
        $field2 = $this->muffin(Field::class, ['tab_id' => $tab2->id, 'order' => 2]);
        $field3 = $this->muffin(Field::class, ['tab_id' => $tab3->id, 'order' => 3]);

        // Test with notNull = true
        $results = $this->repository
            ->tabSort(true)
            ->fields(['fields.id'])
            ->get();
        $this->assertCount(2, $results);
        $this->assertEquals(
            [$field2->id, $field1->id],
            $results->pluck('id')->toArray()
        );

        // Test with notNull = false
        $results = $this->repository
            ->tabSort(false)
            ->fields(['fields.id'])
            ->get();
        $this->assertCount(3, $results);
        $this->assertEquals(
            [$field3->id, $field2->id, $field1->id],
            $results->pluck('id')->toArray()
        );

        // Test with categoryId
        $category = $this->muffin(Category::class);
        $tab1->categories()->sync([$category->id]);
        $results = $this->repository
            ->tabSort(true, $category->id)
            ->fields(['fields.id'])
            ->get();
        $this->assertCount(2, $results);
        $this->assertEquals($field2->id, $results->first()->id);
    }

    public function testScoreSetsQueryBuilderMethod(): void
    {
        $field1 = $this->muffin(Field::class);
        $field2 = $this->muffin(Field::class);
        $this->muffin(Field::class);

        $scoreSets = $this->muffins(2, ScoreSet::class);
        $field1->sets()->sync([$scoreSets[0]->id]);
        $field2->sets()->sync([$scoreSets[0]->id, $scoreSets[1]->id]);

        $results = $this->repository->scoreSets([$scoreSets[0]->id, $scoreSets[1]->id])
            ->fields(['id'])
            ->get();

        $this->assertCount(2, $results);
        $this->assertEqualsCanonicalizing(
            [$field1->id, $field2->id],
            $results->pluck('id')->toArray()
        );

        $results = $this->repository->scoreSets([$scoreSets[1]->id])
            ->fields(['id'])
            ->get();

        $this->assertCount(1, $results);
        $this->assertEquals($field2->id, $results->first()->id);
    }
}
