<?php

namespace AwardForce\Modules\Forms\Fields\Database\Repositories;

use AwardForce\Library\Database\Eloquent\Caching\WithRequestCaching;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Exports\Models\ExportRepository;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\FieldsResource;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Seasons\Contracts\SeasonalBuilderRepository;
use AwardForce\Modules\Seasons\Repositories\SeasonalRepository;
use Illuminate\Support\Collection;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Repository;

interface FieldRepository extends BuilderRepository, ExportRepository, Repository, SeasonalBuilderRepository, SeasonalRepository, WithRequestCaching
{
    /**
     * Retrieves all fields for a given resource.
     *
     * @param  string  $resource
     * @return collection
     */
    public function getByResource($resource);

    /**
     * Retrieves the collection of required fields for the given resource.
     *
     * @param  string  $resource
     * @return Collection
     */
    public function getResourceRequired($resource);

    /**
     * Retrieve all fields for a given resource, that are searchable.
     *
     * @param  string  $resource
     * @param  int  $seasonId
     * @param  array  $excludeIds
     * @return mixed
     */
    public function getSearchable($resource, $seasonId, $excludeIds = []);

    /**
     * Retrieve all searchable fields for a season
     *
     * @param  int  $seasonId
     * @return mixed
     */
    public function getAllSearchable($seasonId);

    /**
     * Return a collection of fields by their slug values.
     *
     * @return mixed
     */
    public function getAllBySlugs(array $slugs);

    /**
     * Returns a collection of fields for the given tab.
     *
     * @return Fields
     */
    public function forTab(Tab $tab);

    /**
     * Returns a collection of fields for the given tab and category.
     *
     * @return \Illuminate\Support\Collection
     */
    public function forTabInCategory(Tab $tab, Category $category, array $excludeTypes = []);

    /**
     * Returns a collection of fields for the given category.
     *
     * @param  array  $excludedTypes
     * @return \Illuminate\Support\Collection
     */
    public function forCategory(Category $category, $resources, $excludedTypes = []);

    /**
     * Synchronise the field categories with the database.
     *
     * @return mixed
     */
    public function syncCategories(Field $field, array $categoryIds);

    /**
     * Return all fields for the season and resource.
     *
     * @param  int  $seasonId
     * @param  string|array  $resource
     * @return mixed
     */
    public function getAllBySeasonAndResource($seasonId, $resource);

    /**
     * @param  string|array  $resource
     * @return mixed
     */
    public function getAllByFormAndResource(Form $form, $resource);

    /**
     * Return all fields for the season and resource, eager load categories including trashed categories.
     *
     * @param  int  $seasonId
     * @param  string|array  $resource
     * @return mixed
     */
    public function getAllBySeasonAndResourceWithTrashedCategories($seasonId, $resource);

    /**
     * Return all fields for the season and resource that can be searchable.
     *
     * @param  int  $seasonId
     * @param  string  $resource
     * @return mixed
     */
    public function getSearchableTypes($seasonId, $resource);

    /**
     * Return all fields for the given form and resource that can be searchable.
     *
     * @param  string  $resource
     * @return Collection
     */
    public function getSearchableTypesForForm(Form $form, $resource);

    /**
     * Return a collection of required fields for the resource that are missing for a given
     * related object. This provides a mechanism to check to see if required field values are missing.
     *
     * @param  int  $foreignId
     * @param  int  $seasonId
     * @return mixed
     */
    public function getMissingRequiredUserFieldsForSeason($foreignId, $seasonId, array $roleIds = []);

    /**
     * Return a collection of fields that the given related object is missing.
     *
     * @param  int  $foreignId
     * @param  int  $seasonId
     * @return mixed
     */
    public function getMissingUserFieldsForSeason($foreignId, $seasonId, array $roleIds = []);

    /**
     * Returns the fields for the specified category.
     *
     * @param  int  $categoryId
     * @return Collection
     */
    public function getFromCategory($categoryId);

    /**
     * Retrieves all fields for a given resource,
     * that are not of a specified type.
     *
     * @param  string  $resource
     * @param  string|array  $type
     * @param  null  $seasonId
     * @return Collection
     */
    public function getByResourceWhereNot($resource, $type, $seasonId = null);

    public function getByResourceWhereFieldTypeNotAndFormTypeIsNotReport(string $resource, string|array $type, ?int $seasonId = null): Collection;

    public function getForEntriesByResourceWhereNot($resource, $type, $seasonId = null);

    public function getForGrantReportsByResourceWhereNot(string $resource, array|string $type, ?int $seasonId = null): Collection;

    public function getUserFieldsByResourceWhereNot($type, $seasonId = null);

    /**
     * Returns all fields for the form, limited by category for a submittable
     */
    public function getByIdsAndResourceWhereNot(int|array $ids, string $resource, array|string $type, ?int $seasonId = null): Collection;

    /**
     * Returns all fields for the given formId and resource that are not of a specified type.
     */
    public function getByFormIdAndResourceWhereNot(int $formId, string $resource, array|string $type): Collection;

    /**
     * Returns all fields for the form, limited by category for an entry
     *
     * @return mixed
     */
    public function getForSubmittable(int $formId, ?int $categoryId);

    /**
     * Returns all fields (with entrant write access) for the form, limited by category for an entry
     */
    public function getWritableForEntrant(int $formId, ?int $categoryId): array;

    /**
     * Returns the fields for the specified season.
     *
     * @param  int  $seasonId
     * @return Collection
     */
    public function getForSeasonCopy($seasonId);

    /**
     * Returns array of field ids for the specified season.
     *
     * @param  int  $seasonId
     * @return array
     */
    public function getIdsForSeason($seasonId);

    /**
     * Return any contactable fields for the resource (email or phone).
     *
     * @param  string  $resource
     * @param  int  $seasonId
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getContactable($resource, int $formId, $seasonId = null);

    /**
     * Return any contactable fields (email or phone) in the form.
     *
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getContactableInForm(int $formId);

    /**
     * Return any Text fields for the resource.
     *
     * @param  string  $resource
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getTextFields($resource, ?int $seasonId = null);

    /**
     * Return any Text fields for the resource by form.
     *
     * @param  string  $resource
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getTextFieldsByForm($resource, int $formId);

    /**
     * Return given type fields for the resource.
     *
     * @param  string  $resource
     * @param  null  $seasonId
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getFieldsOfType($resource, string $type, $seasonId = null);

    /**
     * Return a collection of file fields ordered by order.
     *
     * @param  ?int  $formId
     * @return \Illuminate\Support\Collection
     */
    public function getFileFields($seasonId);

    /**
     * Return a collection of all active fields for a season, excluding the
     * the field specified in the $exclude param for use with conditional fields.
     *
     * @param  int  $seasonId
     * @param  null|int  $exclude
     * @return Collection
     */
    public function getFieldsForConditionalList($seasonId, $exclude = null);

    /**
     * Return a collection of all active fields for a form, excluding the
     * the field specified in the $exclude param for use with conditional fields.
     *
     * @param  int  $formId
     * @param  null|int  $exclude
     * @return Collection
     */
    public function getFormFieldsForConditionalList($formId, $exclude = null);

    /**
     * Return all conditional fields for the season and resource.
     *
     * @param  int  $seasonId
     * @param  string  $resource
     * @return mixed
     */
    public function getConditionalForSeasonAndResource($seasonId, $resource);

    /**
     * Return all fields for the season and resource with conditional field joined
     * for validation.
     *
     * @param  int  $seasonId
     * @param  string  $resource
     * @param  int  $formId
     * @return mixed
     */
    public function getForValidation($seasonId, $resource, $formId = null);

    /** Return all fields that are for a specific role (applies to users only).
     * @param  int|array  $roleId
     * @param  int  $seasonId
     */
    public function getAllForRole($roleId, $seasonId = null);

    /**
     * Return fields filtered by season, resource, and visibility.
     *
     * @param  int  $seasonId
     * @param  string|array  $resource
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getVisible($seasonId, $resource);

    /**
     * Return fields filtered by season, resource, and visibility.
     *
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getVisibleForScoring(Form $form);

    /**
     * Get all writable fields for a specific review stage
     *
     * @param  int  $reviewStageId
     * @return mixed
     */
    public function getReviewStageWritable($reviewStageId);

    /**
     * Get attachment fields of type radio and select, which can be used as attachment type fields, filtered by season.
     *
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getForAttachmentTypeField($seasonId);

    /**
     * Get attachment fields of type radio and select, which can be used as attachment type fields, for a particular form.
     *
     * @return Collection
     */
    public function getForAttachmentTypeFieldAndForm(Form $form);

    /**
     * Force remove all fields from the specified season.
     * WARNING: Bypasses soft deletes.
     */
    public function forceDeleteAllFromSeason(int $season);

    /**
     * Return all field ids for provided resource.
     *
     * @return mixed
     */
    public function getAllFieldIdsForResource($resource);

    /**
     * Returns maximum value of given field (as in SQL), filtered by resource and season.
     *
     * @return mixed
     */
    public function getMaxValue(string $fieldName, ?string $resource = null, ?int $seasonId = null);

    /**
     * increments usage count for fields
     */
    public function setFieldsHaveValues(?array $ids = []): void;

    /**
     * Compute the categories where the given submittable fields are visible.
     *
     * @return mixed
     */
    public function visibilityInCategories(array $fieldIds, array $categoryIds);

    /**
     * @param  int  $fieldId
     * @param  int  $tabId
     */
    public function attachFieldToTab($fieldId, $tabId): void;

    /**
     * @return mixed
     */
    public function getOptionable(int $formId, ?string $resource = null, ?int $seasonId = null);

    /**
     * Return the count of given type fields for the resource.
     *
     * @param  null  $seasonId
     */
    public function countFieldsOfType(string $resource, string $type, $seasonId = null): int;

    public function getAllBySlugsForTypes(array $slugs, $types = []);

    public function getAllScoreableWithScores(int $formId, ?int $seasonId = null): \Platform\Database\Eloquent\Collection;

    public function getAllScoreableWithTitleTranslatedByForm($formId, $categoryId = null): \Platform\Database\Eloquent\Collection;

    public function countAllScoreableWithScores(?int $formId = null, ?int $seasonId = null): int;

    /**
     * Get fields by slugs with translated title
     *
     * @return mixed
     */
    public function getAllBySlugsWithTitleTranslated(array $slugs);

    public function getBySlugWithTrashed(string $slug);

    /**
     * Get searchable fields given a context and a season
     *
     * @param  int  $season
     * @return mixed
     */
    public function getSearchableFields(array $context, int $seasonId);

    public function getAllFormulaCompatible();

    public function getSeasonalUserFields(int $seasonId): Collection;

    public function getNonSeasonalUserFields(): Collection;

    public function slugs(array $slugs): self;

    public function type(string $type): self;

    public function hasTrigger(string $trigger): self;

    public function forEligibilityTab(Tab $tab): self;

    public function scoreAble(bool $enabledOnly = true): self;

    public function resource(string|array $resource): self;

    public function forResourceFormAndCategory(int $formId, ?int $categoryId, FieldsResource $resource): Collection;

    public function forResources(array $resources): self;

    public function havingType(string $type): self;

    public function category(int $categoryId): self;

    public function tabSort(): self;
}
