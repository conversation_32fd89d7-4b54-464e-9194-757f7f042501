<?php

namespace AwardForce\Modules\Tags\Commands;

use AwardForce\Modules\Forms\Fields\Database\Behaviours\HasFields;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Tags\Contracts\TaggableModel;
use AwardForce\Modules\Tags\Services\TagManager;
use Platform\Events\EventDispatcher;

class AutoTagWithFieldValues
{
    use EventDispatcher;

    public function __construct(public readonly TaggableModel&HasFields $model, protected TagManager $tagManager)
    {
    }

    public function handle(): void
    {
        $this->tagManager->tag($this->model, $this->tags());

        $this->dispatchAll($this->tagManager->releaseEvents());
    }

    private function tags(): array
    {
        return $this->taggableFields()
            ->map->value
            ->flatten()
            ->unique()
            ->all();
    }

    private function taggableFields(): Fields
    {
        return $this->model->fields
            ->filter(fn(Field $field) => $field->autoTag && $field->optionable() && $field->isOnCategory($this->model->categoryId ?? 0))
            ->values();
    }
}
