<?php

namespace AwardForce\Modules\Broadcasts\Models;

use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Library\Facades\Vertical;
use AwardForce\Library\Mail\Values\Recipient;
use AwardForce\Library\Mail\Values\Recipients;
use AwardForce\Modules\Accounts\Traits\BelongsToAccount;
use AwardForce\Modules\Broadcasts\Events\BroadcastFilterWasRemoved;
use AwardForce\Modules\Broadcasts\Events\BroadcastStatusChangedToDraft;
use AwardForce\Modules\Broadcasts\Events\BroadcastWasCreated;
use AwardForce\Modules\Broadcasts\Events\BroadcastWasDeleted;
use AwardForce\Modules\Broadcasts\Events\BroadcastWasMarkedAsSending;
use AwardForce\Modules\Broadcasts\Events\BroadcastWasScheduled;
use AwardForce\Modules\Broadcasts\Events\BroadcastWasSent;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Seasons\Traits\Seasonal;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Eloquence\Behaviours\HasSlugs;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Event;
use Platform\Database\Eloquent\TranslatableModel;
use Tectonic\Localisation\Contracts\Translatable;
use Tectonic\Localisation\Translator\Translations;

/**
 * AwardForce\Modules\Broadcasts\Models\Broadcast
 *
 * @property int $id
 * @property int $accountId
 * @property int|null $userId
 * @property int $seasonId
 * @property string|null $slug
 * @property string $type
 * @property array $filters
 * @property string $legalBasis
 * @property bool $consolidateRecipients
 * @property string|null $senderName
 * @property string|null $senderAddress
 * @property string|null $recipients
 * @property string $status
 * @property bool|null $notifySender
 * @property string|null $sentAt
 * @property \Carbon\CarbonImmutable|null $dueDate
 * @property string $dueTimezone
 * @property \Illuminate\Support\Carbon|null $createdAt
 * @property \Illuminate\Support\Carbon|null $updatedAt
 * @property int|null $numberOk
 * @property int|null $numberError
 * @property-read \AwardForce\Modules\Accounts\Models\Account|null $account
 * @property-read \Carbon\CarbonImmutable|null $dueDateUtc
 * @property-read \AwardForce\Modules\Seasons\Models\Season|null $season
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Tectonic\LaravelLocalisation\Database\Translation> $translations
 * @property-read int|null $translationsCount
 * @property-read User|null $user
 *
 * @method static \Platform\Database\Eloquent\Collection<int, static> all($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Broadcast forSeason($seasonId)
 * @method static \Platform\Database\Eloquent\Collection<int, static> get($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Broadcast newModelQuery()
 * @method static \Platform\Database\Eloquent\Builder|Broadcast newQuery()
 * @method static \Platform\Database\Eloquent\Builder|Broadcast preventLazyLoadingInColumnator(bool $prevent = true)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast query()
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereAccountId($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereConsolidateRecipients($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereCreatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereDueDate($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereDueTimezone($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereFilters($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereId($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereLegalBasis($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereNotifySender($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereNumberError($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereNumberOk($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereRecipients($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereSeasonId($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereSenderAddress($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereSenderName($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereSentAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereSlug($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereStatus($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereType($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereUpdatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Broadcast whereUserId($value)
 *
 * @mixin \Eloquent
 */
class Broadcast extends Model implements Translatable
{
    use BelongsToAccount;
    use HasSlugs;
    use Seasonal;
    use TranslatableModel;
    use Translations;

    const STATUS_DRAFT = 'draft';

    const STATUS_SENDING = 'sending';

    const STATUS_SENT = 'sent';

    const STATUS_FAILED = 'failed';

    const STATUS_SCHEDULED = 'scheduled';

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'broadcasts';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['accountId', 'userId', 'seasonId', 'status', 'type', 'filters', 'consolidateRecipients', 'senderName', 'senderAddress', 'notifySender', 'legalBasis', 'number_ok', 'number_error', 'redirect', 'dueDate', 'dueTimezone'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'filters' => 'array',
        'consolidateRecipients' => 'boolean',
        'notifySender' => 'boolean',
        'due_date' => 'immutable_datetime',
    ];

    /**
     * @var Collection
     */
    protected $recipientsCache;

    /** @var string */
    protected $redirect;

    protected $deletedEvent = BroadcastWasDeleted::class;

    /**
     * The required translatable fields for this model.
     *
     * @return array
     */
    public function getTranslatableFields()
    {
        return ['subject', 'body'];
    }

    /**
     * Get user sending the broadcast
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Creates a new Broadcast instance (without saving it to the database)
     *
     *
     * @return Broadcast
     */
    public static function create(array $attributes = [])
    {
        $broadcast = new static($attributes);

        $broadcast->raise(new BroadcastWasCreated($broadcast));

        return $broadcast;
    }

    /**
     * Remove filters that are not relevant to broadcasts.
     */
    public function setFiltersAttribute(array $filters)
    {
        $this->attributes['filters'] = json_encode(Arr::except($filters, ['order', 'dir', 'per_page', '_pjax']));
    }

    /**
     * Removes a specific filter from the array of filters
     */
    public function removeFilter($filter)
    {
        $filters = $this->filters;

        unset($filters[$filter]);

        $this->filters = $filters;

        $this->raise(new BroadcastFilterWasRemoved($this));
    }

    /**
     * Checks if the filter should be locked
     *
     * @param  string  $filter
     */
    public function locksFilter($filter): bool
    {
        return in_array($filter, config('broadcasts.locked_filters.'.$this->type, []));
    }

    /**
     * Checks if the broadcast is a draft
     *
     * @return bool
     */
    public function draft()
    {
        return $this->status == self::STATUS_DRAFT;
    }

    /**
     * Checks if the broadcast was already sent.
     *
     * @return bool
     */
    public function sent()
    {
        return $this->status == self::STATUS_SENT;
    }

    public function scheduled(): bool
    {
        return $this->status == self::STATUS_SCHEDULED;
    }

    /**
     * Changes the Broadcast status to 'sending'
     */
    public function markAsSending()
    {
        $this->numberError = 0;
        $this->numberOk = 0;
        $this->status = self::STATUS_SENDING;

        $this->raise(new BroadcastWasMarkedAsSending($this));
    }

    /**
     * Marks a Broadcast as sent by setting the sentAt timestamp and storing the recipients list
     */
    public function markAsSent()
    {
        $this->recipients = $this->encodeRecipients();
        $this->sentAt = Carbon::now();
        $this->status = self::STATUS_SENT;

        $this->raise(new BroadcastWasSent($this));
    }

    /**
     * Marks a Broadcast as failed by setting the sentAt timestamp and storing the recipients list
     */
    public function markAsFailed()
    {
        $this->recipients = $this->encodeRecipients();
        $this->sentAt = Carbon::now();
        $this->status = self::STATUS_FAILED;
    }

    public function markAsScheduled()
    {
        $this->status = self::STATUS_SCHEDULED;

        $this->raise(new BroadcastWasScheduled($this));
    }

    /**
     * Grabs the slug, full name and email from each recipient and encodes the resulting array.
     *
     * @return string
     */
    protected function encodeRecipients()
    {
        return $this->recipients()->map(function (Recipient $recipient) {
            return [
                'name' => $recipient->fullName(),
                'email' => $recipient->email(),
                'is_sender' => ($this->notifySender && ($recipient->email() == $this->senderAddress)), //to identify which of the recipient is the sender
            ];
        })->toJson();
    }

    /**
     * Returns the Collection of recipients for this broadcast.
     *
     * @return Collection
     */
    public function recipients()
    {
        if ($this->sent()) {
            return $this->recoverRecipients();
        }

        return $this->recipientsCache ?: $this->prepareRecipients();
    }

    /**
     * Fires an event so the appropriate handler can prepare the recipients list.
     *
     * The result is stored in the object to avoid repeating this operation multiple times in a single request.
     *
     * @return Collection
     */
    public function prepareRecipients()
    {
        $preparer = new RecipientsPreparer($this);

        Event::fire($preparer);

        $recipients = $preparer->recipients();

        return $this->recipientsCache = $recipients;
    }

    /**
     * Returns a Collection with the recipients to whom this broadcast was sent.
     *
     * @return Collection
     */
    public function recoverRecipients()
    {
        $recipients = collect(json_decode($this->attributes['recipients'], true))
            // if a copy was sent to the sender,
            // we do not want the sender to be seen as a recipient
            ->reject(function ($recipient) {
                return Arr::get($recipient, 'is_sender', true);
            })
            ->map(function ($recipient) {
                return Recipient::fromNameAndEmail($recipient['email'], $recipient['name']);
            });

        return new Recipients($recipients->all());
    }

    /**
     * Returns an array with the filters for this broadcast plus the seasonId.
     *
     * @return array
     */
    public function filtersWithSeason()
    {
        if (isset($this->filters['season']) && $this->filters['season'] === 'all') {
            return (array) $this->filters;
        }

        return array_merge((array) $this->filters, ['seasonId' => $this->seasonId, 'season' => $this->seasonId]);
    }

    /**
     * Returns an array with the merge tags that apply to this broadcast type.
     */
    public function mergeFields(): array
    {
        return Vertical::replaceArray(
            array_merge(
                config('broadcasts.merge_fields.generic', []),
                config("broadcasts.merge_fields.{$this->type}", [])
            )
        );
    }

    public function consolidatedMergeFields(): array
    {
        return Vertical::replaceArray(
            array_merge(
                config('broadcasts.merge_fields.generic', []),
                config('broadcasts.merge_fields.users', [])
            )
        );
    }

    /**
     * Checks if the given broadcast should consolidate recipients.
     */
    public function shouldConsolidate(): bool
    {
        if (! in_array($this->type, config('broadcasts.optional_consolidation'))) {
            return true;
        }

        return $this->consolidateRecipients ?: false;
    }

    public function isLegitimateInterest()
    {
        return $this->legalBasis === Notification::LEGAL_BASIS_INTEREST;
    }

    public function addOk(int $number = 1)
    {
        $this->numberOk += $number;
    }

    public function addError(int $number = 1)
    {
        $this->numberError += $number;
    }

    public function hasError()
    {
        return $this->numberError && $this->numberError > 0;
    }

    public function redirect()
    {
        return $this->attributes['redirect'];
    }

    public function getDueDateAttribute(): ?CarbonImmutable
    {
        $dueDate = $this->attributes['due_date'];
        if (! $dueDate) {
            return null;
        }

        return CarbonImmutable::createFromTimeString($dueDate)
            ->setTimezone($this->attributes['due_timezone']);
    }

    public function setStatusAttribute($value)
    {
        $this->attributes['status'] = $value;
        if ($this->isDirty('status') && $value == Broadcast::STATUS_DRAFT) {
            $this->raise(new BroadcastStatusChangedToDraft($this));
        }
    }

    public function getDueDateUtcAttribute(): ?CarbonImmutable
    {
        $dueDate = $this->attributes['due_date'];
        if (! $dueDate) {
            return null;
        }

        return CarbonImmutable::createFromTimeString($dueDate)
            ->setTimezone('UTC');
    }
}
