<?php

namespace AwardForce\Modules\Entries\Contracts;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Duplicates\Duplicate;
use Platform\Database\Repository;

interface DuplicateRepository extends Repository
{
    /**
     * Get all duplicates in the given season, sorted by confirmed and entry_id.
     *
     * @return mixed
     */
    public function forDuplicateFinder(int $seasonId);

    /**
     * The number of out of date duplicates in the given season.
     *
     * @return mixed
     */
    public function countOutOfDate(int $seasonId);

    /**
     * The number primaries in the given season.
     *
     * @return mixed
     */
    public function countPrimaries(int $seasonId);

    /**
     * Get all unconfirmed duplicates found for the given entry
     *
     * @return mixed
     */
    public function unconfirmedDuplicates(Entry $entry);

    /**
     * Get all duplicates (confirmed/not confirmed/archived) found for the given duplicate root
     *
     * @return mixed
     */
    public function duplicatesForEntry(Duplicate $root);

    /**
     * Get all submitted duplicates found for the given root
     *
     * @return mixed
     */
    public function submittedDuplicatesForEntry(Duplicate $root);

    /**
     * Permanently delete all Duplicate records in the given season.
     *
     * @return mixed
     */
    public function resetSeason(int $seasonId);
}
