<?php

namespace AwardForce\Modules\Entries\Contracts;

use AwardForce\Library\Database\Eloquent\Caching\WithFlexibleCaching;
use AwardForce\Library\Database\Repository\Builder\WithSlugBuilderRepository;
use AwardForce\Modules\Assignments\Services\SyncFilter;
use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Exports\Models\ExportRepository;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Forms\Database\Repositories\SubmittableRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Panels\Models\Panel;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Eloquent\Collection;
use Platform\Database\Repository;
use Platform\Search\SearchFilterCollection;

interface EntryRepository extends BuilderRepository, ExportRepository, Repository, SubmittableRepository, WithFlexibleCaching, WithSlugBuilderRepository
{
    /**
     * Returns the number of entries owned by the user.
     *
     * @param  int  $seasonId
     * @return int
     */
    public function countUserEntries(User $user, $seasonId);

    /**
     * Retrieve a collection of entries for a given user id.
     *
     * @param  int  $userId
     * @return mixed
     */
    public function getForUser($userId);

    /**
     * Retrieve a collection of entries for a given user id, loading the given relations
     *
     * @return mixed
     */
    public function getForUserWithRelations(int $userId, array $relations);

    /**
     * Retrieve a collection of entries for a given user id.
     *
     * @param  int  $userId
     * @return mixed
     */
    public function getTrashedForUser($userId);

    /**
     * Returns the trashed entries count for the specified user.
     *
     * @param  int  $userId
     */
    public function countTrashedForUser($userId): int;

    /**
     * Returns the entry linked to the specified Attachment.
     *
     * @return Entry
     */
    public function getFromAttachment(Attachment $attachment);

    /**
     * Returns the entries in the specified category, optionally filtered by one or more divisions.
     *
     *     ->getForCategory($category->id); // all divisions
     *     ->getForCategory($category->id, 2); // division 2
     *     ->getForCategory($category->id, [3, 4]); // divisions 3 & 4
     *
     * @param  int  $categoryId
     * @param  bool  $submitted
     * @param  int|array  $division
     * @return \Illuminate\Support\Collection
     */
    public function getForCategory($categoryId, $submitted = false, $division = null);

    /**
     * Returns the volume of submitted entries per period, up to the limit.
     *
     * @param  int  $seasonId
     * @param  int  $formId
     * @param  int  $limit
     * @param  string  $period
     * @return mixed
     */
    public function getSubmittedEntryVolume($seasonId, $formId, $limit, $period);

    /**
     * Returns the volume of created entries per period, up to the limit.
     *
     * @param  int  $seasonId
     * @param  int  $formId
     * @param  int  $limit
     * @param  string  $period
     * @return mixed
     */
    public function getCreatedEntryVolume($seasonId, $formId, $limit, $period);

    /**
     * Returns the total number of entries in progress for the season.
     *
     * @param  int  $seasonId
     * @param  int  $formId
     * @return int
     */
    public function totalInProgress($seasonId, $formId);

    /**
     * Returns the total number of entries that have been submitted for the season.
     *
     * @param  int  $seasonId
     * @param  int  $formId
     * @return int
     */
    public function totalSubmitted($seasonId, $formId);

    /**
     * Return the entry that matches the provided slug.
     *
     * @return mixed
     */
    public function viewFromApi(string $slug);

    /**
     * Return all entries based on the ids provided, and relationships for attachments.
     *
     * @return mixed
     */
    public function forThumbnails(array $entryIds);

    /**
     * Returns all the entries a user has in the given season.
     *
     * @return mixed
     */
    public function getForUserInSeason(int $userId, int $seasonId);

    /**
     * Returns all the entries a user has in the given category.
     *
     * @return mixed
     */
    public function getForUserInCategory(int $userId, int $categoryId);

    /**
     * Returns an iterator over a chunked collection of entries for the specified IDs,
     * with the related recorded loaded and translated, ready for an export.
     *
     * @param  array  $ids
     * @return \Iterator  containing \Illuminate\Support\Collection
     */
    public function yieldForExport($ids): \Iterator;

    /**
     * Returns a collection of entries with categories given an array of IDs.
     *
     * @return mixed
     */
    public function getByIdsWithCategories(array $ids);

    /**
     * Returns a collection of entries with categories given an array of IDs and excluding invited entries.
     *
     * @return mixed
     */
    public function getIdsWithCategoriesExcludingInvited(array $ids): Collection;

    /**
     * Returns an entry with categories given an ID.
     *
     * @param  int  $id
     * @return mixed
     */
    public function getOneByIdWithCategories($id);

    /**
     * Returns a raw collection of results from the search filters
     *
     * @return \Illuminate\Support\Collection
     */
    public function getRawByFilters(SearchFilterCollection $filters);

    /**
     * Returns all entries that match the Panel.
     */
    public function forPanel(Panel $panel, SyncFilter $filter): Collection;

    public function countForPanel(Panel $panel): int;

    /**
     * Returns an Entry with the matching slug, loading essential relations.
     *
     * @param  string  $slug
     * @return Entry
     */
    public function requireBySlugWithRelations($slug, array $relations = []);

    /**
     * Returns entry fees grouped by currency for all in-progress entries, providing an estimation of potential revenue
     * for the account.
     *
     * @return mixed
     */
    public function inProgressEntryFees(int $seasonId, string $defaultCurrency);

    /**
     * Return all entry ids for the given user, including trashed.
     *
     * @return mixed
     */
    public function getAllEntryIdsForUser(int $userId);

    /**
     * Return all entry ids for the given season, including trashed.
     *
     * @return mixed
     */
    public function getAllEntryIdsForSeason(int $seasonId);

    /**
     * Retrieves all entries (irrespective of account) that has any of the associated tags.
     *
     * @return mixed
     */
    public function getEntriesWithAnyTags(array $tagIds);

    /**
     * Retrieves entries that have all of the associated tags.
     *
     * @return mixed
     */
    public function getEntriesWithAllTags(array $tagIds);

    /**
     * Finds entries in the given season that should be scanned for plagiarism and runs the given callback on each.
     *
     * @return mixed
     */
    public function findForPlagiarismScan(int $seasonId, callable $callback);

    /**
     * Return cursor with entries in the given season that should be scanned as potential duplicates.
     *
     * @return mixed
     */
    public function potentialDuplicates(int $seasonId, int $primaryCount = 0);

    /**
     * Count how many potential duplicate entries have not been scanned in the given season.
     *
     * @return mixed
     */
    public function countUnscannedPotentialDuplicates(int $seasonId);

    /**
     * Get primary non archived, non deleted entries for active season based on title.
     *
     * @return mixed
     */
    public function getPrimaryEntriesForSeason(int $seasonId, string $title);

    /**
     * @return mixed
     */
    public function allWithoutLocalId();

    /**
     * Returns the number of entries owned by the user for a particular account.
     *
     * @param  int  $seasonId
     * @return int
     */
    public function countUserEntriesForAccount(User $user, $accountId);

    /**
     * Get entries for which we can set resubmission_required
     */
    public function getForResubmissionByIds(array $ids = []): Collection;

    /**
     * @return mixed
     */
    public function filterByTitle(string $title = '', int $limit = 8);

    /**
     * @return mixed
     */
    public function getBySlugs(array $slugs);

    /**
     * Get entries for which the fund allocation is allowed
     */
    public function getForFundAllocationByIds(array $ids = []): Collection;

    /**
     * Check if there is eligibility for entrant
     */
    public function eligibilityExistsForEntrant(int $entrantId, ?int $seasonId = null): bool;

    /**
     * Get the entries for the specified form.
     */
    public function filterIdsByForm(array|int $ids, int $formId): array;

    /**
     * Returns if exists any entry with the given field.
     */
    public function existsForField(Field $field): bool;

    public function getForField(Field $field): Collection;
}
