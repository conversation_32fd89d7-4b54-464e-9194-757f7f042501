<?php

namespace AwardForce\Modules\Entries\Services\Duplicates;

use AwardForce\Library\Bus\LocksProcess;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;

class LocksFindDuplicates extends LocksProcess
{
    protected function key()
    {
        $seasonId = CurrentAccount::activeSeasonId();

        return "find-duplicates-{$seasonId}";
    }

    protected function storeCompletion()
    {
        return true;
    }
}
