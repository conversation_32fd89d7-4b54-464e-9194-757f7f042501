<?php

namespace AwardForce\Modules\Entries\Services\Duplicates;

use AwardForce\Modules\Entries\Contracts\DuplicateRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Duplicates\Comparers\EntryComparer;
use Illuminate\Support\Collection;

class DuplicateFinder
{
    /** @var EntryComparer */
    protected $comparer;

    /** @var EntryRepository */
    protected $entries;

    /** @var DuplicateRepository */
    protected $duplicates;

    public function __construct(EntryComparer $comparer)
    {
        $this->comparer = $comparer;
        $this->entries = app(EntryRepository::class);
        $this->duplicates = app(DuplicateRepository::class);
    }

    /**
     * Detect potentially duplicate entries in the given season.
     */
    public function findInSeason(int $seasonId)
    {
        $duplicates = $this->duplicates->forDuplicateFinder($seasonId);
        $primaryCount = $this->duplicates->countPrimaries($seasonId);

        foreach ($this->entries->potentialDuplicates($seasonId, $primaryCount) as $entry) {
            $duplicate = Duplicate::getForEntry($entry);

            $match = $this->closestMatch($entry, $duplicates);

            if ($match) {
                $duplicate->markAsDuplicate($match);
            } else {
                $duplicate->markAsPrimary();
            }

            $duplicates[] = $duplicate;
        }

        Duplicate::rebuildScopedTree(['season_id' => $seasonId], true);
    }

    private function closestMatch(Entry $entry, Collection $duplicates)
    {
        [$closestMatch, $percentage] = [null, 0];

        foreach ($duplicates as $duplicate) {
            if (! $duplicate->confirmed() && ($duplicate->entryId >= $entry->id)) {
                break;
            }

            $similarity = $this->comparer->compare($entry, $duplicate->entry);

            if ($similarity == 100) {
                return $duplicate;
            }

            if ($similarity > $percentage) {
                [$closestMatch, $percentage] = [$duplicate, $similarity];
            }
        }

        return $closestMatch;
    }
}
