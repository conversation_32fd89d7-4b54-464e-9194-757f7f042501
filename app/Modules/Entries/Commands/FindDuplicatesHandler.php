<?php

namespace AwardForce\Modules\Entries\Commands;

use AwardForce\Modules\Entries\Events\DuplicatesFound;
use AwardForce\Modules\Entries\Services\Duplicates\Comparers\SameCategorySimilarTitle;
use AwardForce\Modules\Entries\Services\Duplicates\DuplicateFinder;
use AwardForce\Modules\Entries\Services\Duplicates\LocksFindDuplicates;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use Platform\Events\EventDispatcher;

class FindDuplicatesHandler
{
    use EventDispatcher;

    public function __construct(private LocksFindDuplicates $locker, private FormRepository $forms)
    {
    }

    public function handle(FindDuplicates $command)
    {
        try {
            $forms = $this->forms->season($command->seasonId)->fields(['id', 'type', 'settings'])->get();

            foreach ($forms as $form) {
                (new DuplicateFinder(
                    new SameCategorySimilarTitle($form->settings->minimumSimilarityPercentage ?? 80)
                ))->findInSeason($command->seasonId);
            }

            $this->dispatch(new DuplicatesFound);
        } finally {
            $this->locker->releaseLock();
        }
    }
}
