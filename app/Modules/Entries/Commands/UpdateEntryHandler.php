<?php

namespace AwardForce\Modules\Entries\Commands;

use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Entries;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use Platform\Events\EventDispatcher;

class UpdateEntryHandler
{
    use EventDispatcher;

    public function __construct(
        protected Entries $entries,
        protected EntryRepository $repository,
        protected ValuesService $values
    ) {
    }

    public function handle(UpdateEntry $command): Entry
    {
        $currentEntry = $this->repository->getById($command->submittableId());

        $updatedEntry = $this->entries->updateEntry(
            $command->submittableId(),
            $command->userId() ?? $currentEntry->user->id,
            $command->title() ?? $currentEntry->title,
            $command->chapterId() ?? $currentEntry->chapter?->id,
            $command->categoryId() ?? $currentEntry->category?->id,
            $command->isEntrant(),
            $command->values() ?? $this->values->getValuesForObject($currentEntry)->toArray(),
            $command->time(),
            $command->status(),
            $command->moderationStatus(),
            $command->grantStatus(),
            $command->grantEndDate(),
            $command->customDeadline()
        );

        $this->dispatch($updatedEntry->releaseEvents());

        return $updatedEntry;
    }
}
