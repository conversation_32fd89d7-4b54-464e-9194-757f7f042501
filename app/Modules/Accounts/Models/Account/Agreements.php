<?php

namespace AwardForce\Modules\Accounts\Models\Account;

trait Agreements
{
    public function customAgreementToTerms(): bool
    {
        foreach ($this->supportedLanguageCodes() as $languageCode) {
            if ($this->agreementToTerms($languageCode) !== $this->defaultAgreementToTerms($languageCode)) {
                return true;
            }
        }

        return false;
    }

    public function customConsentToNotificationsAndBroadcasts(): bool
    {
        foreach ($this->supportedLanguageCodes() as $languageCode) {
            if ($this->consentToNotificationsAndBroadcasts($languageCode) !== $this->defaultConsentToNotificationsAndBroadcasts($languageCode)) {
                return true;
            }
        }

        return false;
    }

    public function agreementToTerms(?string $languageCode = null)
    {
        $agreement = lang($this, 'agreementToTerms', $languageCode);

        return $agreement !== 'NTA' ? $agreement : $this->defaultAgreementToTerms($languageCode);
    }

    public function defaultAgreementToTerms($languageCode = null)
    {
        $replace = ['tos' => $this->tosLink ?: config('links.default_tos_link')];

        return trans('agreements.default.agreement_to_terms', $replace, $languageCode);
    }

    public function consentToNotificationsAndBroadcasts(?string $languageCode = null)
    {
        $consent = lang($this, 'consentToNotificationsAndBroadcasts', $languageCode);

        return $consent !== 'NTA' ? $consent : $this->defaultConsentToNotificationsAndBroadcasts($languageCode);
    }

    public function defaultConsentToNotificationsAndBroadcasts($languageCode = null)
    {
        $replace = ['account' => lang($this, 'name')];

        return trans('agreements.default.consent_to_notifications_and_broadcasts', $replace, $languageCode);
    }
}
