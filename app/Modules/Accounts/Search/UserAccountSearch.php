<?php

namespace AwardForce\Modules\Accounts\Search;

use AwardForce\Modules\Accounts\Contracts\GlobalAccountRepository;
use AwardForce\Modules\Identity\Users\Models\GlobalUser;
use AwardForce\Modules\Identity\Users\Search\Filters\UserAccountSearchFilter;
use Illuminate\Support\Arr;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\SearchFilterCollection;

class UserAccountSearch
{
    private $accounts;

    public function __construct(GlobalAccountRepository $accounts)
    {
        $this->accounts = $accounts;
    }

    public function forGlobalUser(GlobalUser $user, array $input)
    {
        $filterCollection = new SearchFilterCollection;

        $filterCollection->add(new ColumnFilter([
            'global_accounts.id',
            'global_accounts.translations',
            'global_accounts.domains',
            'global_accounts.default_language',
        ]));

        $filterCollection->add(
            new UserAccountSearchFilter($user, Arr::get($input, 'keywords', null))
        );

        return $this->accounts->getByFilters($filterCollection, $paginate = false);
    }
}
