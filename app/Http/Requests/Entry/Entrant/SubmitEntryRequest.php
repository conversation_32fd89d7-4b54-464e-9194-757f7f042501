<?php

namespace AwardForce\Http\Requests\Entry\Entrant;

use AwardForce\Http\Requests\Entry\EligibilityValidation;
use AwardForce\Modules\Entries\Validation\ValidatesAttachmentsWithLinks;
use AwardForce\Modules\Entries\Validation\ValidatesSubmittable;
use AwardForce\Modules\Forms\Fields\Http\Validation\ValidatesFields;
use AwardForce\Modules\Forms\Fields\Http\Validation\ValidatesTabs;

class SubmitEntryRequest extends EntryFormRequest implements EligibilityValidation
{
    use ValidatesAttachmentsWithLinks;
    use ValidatesFields {
        getFields as getFieldsForValidation;
    }
    use ValidatesSubmittable;
    use ValidatesTabs;

    /**
     * Optionally add additional rules.
     */
    protected function modifyRules(array $rules): array
    {
        if (! $this->entry->invitedCategoryId) {
            array_push($rules['categoryId'], 'category_max_entries:'.$this->get('categoryId').','.$this->entry->id);
        }

        return array_merge($rules, $this->attachmentRules(), $this->contributorRules(), $this->fieldRules(), $this->refereeRules());
    }

    /**
     * Optionally add additional messages.
     */
    protected function modifyMessages(array $messages): array
    {
        return array_merge($messages, $this->fieldMessages());
    }

    /**
     * Returns an array mapping the resources in the entry form that require field validation with the string
     * representation of those fields in the form request.
     *
     * @return array
     */
    public function resource()
    {
        return $this->submittableFormResources();
    }

    /**
     * Retrieve the fields specified for a given resource.
     *
     * @param  string  $resource
     * @return mixed
     */
    protected function getFields($resource)
    {
        $fields = $this->getFieldsForValidation($resource);

        // Only validate fields the entrant has write access to
        return $fields->filter(function ($field) {
            return $field->entrantWriteAccess;
        });
    }
}
