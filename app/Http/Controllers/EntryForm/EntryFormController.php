<?php

namespace AwardForce\Http\Controllers\EntryForm;

use AwardForce\Http\Requests\Entry\BlankPdf\EntryBlankPDFDownloadRequest;
use AwardForce\Http\Requests\Entry\EligibilityValidation;
use AwardForce\Http\Requests\Entry\Entrant\InitateRefereeReviewStageRequest;
use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Ecommerce\Cart\Costing\NoPriceAmountAvailableException;
use AwardForce\Modules\Ecommerce\Cart\Costing\NoPriceFeeAvailableException;
use AwardForce\Modules\Entries\Commands\AddEntryToCartCommand;
use AwardForce\Modules\Entries\Commands\CalculateEligibility;
use AwardForce\Modules\Entries\Commands\GenerateBlankFormPDF;
use AwardForce\Modules\Entries\Commands\ResubmitEntryCommand;
use AwardForce\Modules\Entries\Commands\StartEntry;
use AwardForce\Modules\Entries\Commands\SubmitEntryCommand;
use AwardForce\Modules\Entries\Commands\UpdateEntry;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\EntrantEntryFormService;
use AwardForce\Modules\Forms\Collaboration\Commands\DeleteSubmittableLockables;
use AwardForce\Modules\Forms\Forms\Bus\UpdateSubmittableAttachments;
use AwardForce\Modules\Forms\Forms\Bus\UpdateSubmittableContributors;
use AwardForce\Modules\Forms\Forms\Bus\UpdateSubmittableLinks;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Http\Controllers\SubmittableFormController;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Referees\Commands\UpdateSubmittableReferees;
use AwardForce\Modules\ReviewFlow\Commands\InitiateRefereeReviewStage;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\ReviewFlow\Events\SendReviewTaskNotificationWasRequested;
use AwardForce\Modules\Rounds\Services\RoundStatus;
use AwardForce\Modules\Seasons\Models\Season;
use Carbon\Carbon;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Platform\Events\EventDispatcher;

abstract class EntryFormController extends SubmittableFormController
{
    use DispatchesJobs, EventDispatcher {
        DispatchesJobs::dispatch insteadof EventDispatcher;
        EventDispatcher::dispatch as dispatchEvent;
    }

    protected FormRepository $forms;
    protected RoundStatus $rounds;
    protected ContentBlockRepository $contentBlocks;

    abstract protected function isEntrant(): bool;

    abstract protected function getSeason(): Season;

    abstract protected function getUserId(Request $request);

    abstract protected function paidEntries(): bool;

    protected function submit(Request $request, $redirect = null)
    {
        $entry = $request->entry;
        if ($this->orderRequired($entry)) {
            $this->addToCart($entry);

            return response()->json(['redirect' => 'cart']);
        }

        if ($entry->resubmissionRequired()) {
            if (feature_disabled($feature = 'resubmission')) {
                return redirect()->route('feature.disabled', $feature);
            }
            $this->dispatch(new ResubmitEntryCommand($entry->id));

            return $redirect ?: $this->redirectAfterSubmit($entry);
        }

        $this->dispatch(new SubmitEntryCommand($entry->id));

        return $redirect ?: $this->redirectAfterSubmit($entry);
    }

    protected function mapSubmittable(Submittable $submittable): array
    {
        return $this->mapEntry($submittable);
    }

    protected function newSubmittable(Request $request): Submittable
    {
        return $this->newEntry($request);
    }

    protected function updateSubmittable(Request $request, &$response = [])
    {
        $this->updateEntry($request, $response);

        return $response;
    }

    /**
     * Runs the various entry update commands. Used by both the putUpdate() and putSubmit() actions.
     *
     * @param  array  $response
     * @return Entry
     */
    protected function updateEntry(Request $request, &$response = [])
    {
        $entry = $this->dispatchSync(new UpdateEntry(
            $request->entry->id,
            $this->getUserId($request),
            $request->get('values', []),
            Carbon::now(),
            $request->get('title'),
            $request->get('chapterId'),
            $request->get('categoryId'),
            $this->isEntrant()
        ));

        $this->dispatch(new UpdateSubmittableAttachments(
            Form::FORM_TYPE_ENTRY,
            $entry->id,
            $request->get('attachmentFields', []),
            $entry->updatedAt
        ));

        $response['contributors'] = $request->has('contributors')
            ? $this->dispatchSync(
                new UpdateSubmittableContributors(
                    Form::FORM_TYPE_ENTRY,
                    $entry->id,
                    $request->get('contributors', []),
                    $entry->updatedAt
                ))
            : [];

        $response['links'] = $this->dispatchSync(new UpdateSubmittableLinks(
            Form::FORM_TYPE_ENTRY,
            $entry->id,
            $request->get('links', [])
        ));

        $response['referees'] = [];

        if ($request->has('referees')) {
            $this->dispatchSync(new UpdateSubmittableReferees(
                Form::FORM_TYPE_ENTRY,
                $entry->id,
                $request->collect('referees')->except('total')->collapse()->toArray(),
                $entry->updatedAt
            ));
            $response['referees'] = $this->referees($entry)->flatMap->all()->toArray();
        }

        if ($entry->form->supportsRealTimeUpdates()) {
            $this->dispatch(new DeleteSubmittableLockables($entry->slug));
        }

        $response['updatedAt'] = (string) $entry->updatedAt;

        return $entry;
    }

    protected function mapEntry($entry): array
    {
        $mappedEntry = EntrantEntryFormService::mapSubmittable($entry);
        $mappedEntry['submissionPossible'] = $this->rounds->entriesAllowed($entry->form) && ! $entry->submittedAt;

        return $mappedEntry;
    }

    protected function newEntry(Request $request): Entry
    {
        return $this->dispatchSync(StartEntry::fromApp(
            $this->getUserId($request),
            $request->get('title'),
            $this->getSeason()->id,
            $request->formId(),
            $request->get('chapterId'),
            $request->get('categoryId'),
            $request->get('values', []),
            $this->isEntrant()
        ));
    }

    protected function addToCart(Entry $entry, bool $submit = true)
    {
        if ($this->paidEntries() && ! $entry->paymentComplete()) {
            try {
                $this->dispatch(new AddEntryToCartCommand($entry, $submit));
            } catch (NoPriceAmountAvailableException|NoPriceFeeAvailableException $ex) {
                // Ignore. User will be warned when redirected to the cart
            }
        }
    }

    public function getCategories(Request $request, $chapterId, $seasonId)
    {
        return response()->json(
            $this->generator->chapterCategoriesTree(
                $seasonId,
                $chapterId,
                filter_var($request->get('active'), FILTER_VALIDATE_BOOLEAN),
                filter_var($request->get('trashed'), FILTER_VALIDATE_BOOLEAN),
                $this->getForm()
            )
        );
    }

    public function blankPdf(EntryBlankPDFDownloadRequest $request, $category, $chapter = null)
    {
        $pdf = $this->dispatchSync(new GenerateBlankFormPDF($category, $chapter));

        return response($pdf)->header('Content-Type', 'application/pdf');
    }

    protected function processEligibilityRequest(EligibilityValidation $request)
    {
        $entry = $this->updateEntry($request, $response);
        $eligibilityTab = $request->eligibilityTab();

        $tabs = $this->getTabs($entry);

        if (! $request->eligibilityTab()) {
            $eligibilityTab = $this->eligibilityTab($this->getTabs($entry));
        }
        if (is_null($eligibilityTab)) {
            return $this->submit($request);
        }

        $eligibilityTab->isLastTab = ! $tabs->contains(fn($tab) => $tab['visibleToEntrants'] && $tab['order'] > $eligibilityTab->order && ($tab['categoryOption'] == 'all' || in_array($entry->categoryId, $tab['categories'])));

        return $this->calculateEligibility($request, $entry, $eligibilityTab, $response);
    }

    protected function calculateEligibility(EligibilityValidation $request, Entry $entry, Tab $eligibilityTab, $response)
    {
        $wasEligible = $entry->isEligible();
        $lastEligibleAt = $entry->eligibleAt;
        $entry = $this->dispatchSync(new CalculateEligibility($entry, $eligibilityTab));

        // If there are new eligibility tabs added after the entry was eligible, we should redirect back to entry form
        $newEligibilityTabs = $wasEligible && $request->eligibilityTabs()->some(fn(Tab $tab) => $tab->createdAt->gte($lastEligibleAt));

        if ($entry->isIneligible()) {
            return $this->redirectAfterEligibility($entry, $eligibilityTab->getSetting('ineligible-content-block'));
        } elseif ($entry->isEligible() && ! $newEligibilityTabs && ($wasEligible || $eligibilityTab->isLastTab)) {
            // If the eligibility tab is the last tab in the entry form then we redirect to the eligibility content block page
            $redirect = $eligibilityTab->isLastTab ? $this->redirectAfterEligibility($entry, $eligibilityTab->getSetting('eligible-content-block')) : null;

            return $this->submit($request, $redirect);
        }

        // There are more tabs
        $response['updatedAt'] = (string) $entry->updatedAt;
        $response['tabs'] = $this->getTabs($entry);
        $response['isEligible'] = $entry->isEligible();
        $response['eligibilityContentBlock'] = $this->eligibilityContentBlock($entry, $eligibilityTab->getSetting('eligible-content-block'));

        return $response;
    }

    protected function redirectAfterEligibility(Entry $entry, string $contentBlock)
    {
        return response()->json([
            'redirect' => 'eligibility',
            'entrySlug' => (string) $entry->slug,
            'contentBlock' => $contentBlock,
        ]);
    }

    protected function eligibilityContentBlock(Entry $entry, ?string $contentBlock)
    {
        if (! $contentBlock) {
            return null;
        }
        $contentBlock = translate(app(ContentBlockRepository::class)->getBySlug($contentBlock));

        return $contentBlock->content(
            Vertical::replaceArrayKeys([
                'first_name' => $entry->user->firstName,
                'last_name' => $entry->user->lastName,
                'entry_name' => $entry->title,
                'entry_slug' => (string) $entry->slug,
                'user_slug' => (string) $entry->user->slug,
            ])
        );
    }

    protected function eligibilityTab(Collection $tabs): ?Tab
    {
        $lastTab = $tabs->filter(fn($tab) => $tab['type'] === Tab::TYPE_ELIGIBILITY && $tab['visibleToEntrants'])->last();
        if ($lastTab['id'] ?? false) {
            return app(TabRepository::class)->getById($lastTab['id']);
        }

        return null;
    }

    private function orderRequired($entry)
    {
        return $this->paidEntries() && ! $entry->paymentComplete() && ! $entry->hasOrderAwaitingPayment();
    }

    public function initiateRefereeReviewStage(InitateRefereeReviewStageRequest $request)
    {
        $reviewTask = $this->dispatchSync(new InitiateRefereeReviewStage($request->integer('entry'), $request->integer('referee')));

        return response()->json(['review_task' => (string) $reviewTask?->token, 'request_sent_at' => $reviewTask?->referee->requestSentAtFormatted()]);
    }

    public function resendRefereeReviewTask(ReviewTask $reviewTask)
    {
        $this->dispatchEvent(new SendReviewTaskNotificationWasRequested($reviewTask));

        return response()->json();
    }
}
