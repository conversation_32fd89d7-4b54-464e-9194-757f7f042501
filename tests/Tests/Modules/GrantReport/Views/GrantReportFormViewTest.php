<?php

namespace Tests\Modules\GrantReport\Views;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Library\Database\Firebase\Database;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\GrantReports\Views\EntrantGrantReportFormView;
use AwardForce\Modules\GrantReports\Views\ManagerGrantReportFormView;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Request;
use Tests\IntegratedTestCase;

final class GrantReportFormViewTest extends IntegratedTestCase
{
    private Form $reportForm;

    public function init()
    {
        $this->reportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        $this->reportForm->categories()->save($this->muffin(Category::class));
        $this->spy(Database::class);
    }

    public function testMappedSubmittable(): void
    {
        $grantReport = $this->muffin(GrantReport::class, ['form_id' => $this->reportForm->id]);
        Request::setRouteResolver($this->routeResolver($grantReport));

        $view = app(ManagerGrantReportFormView::class);
        $mappedSubmittable = $view->mappedSubmittable();

        $this->assertSame($grantReport->id, $mappedSubmittable['id']);
        $this->assertSame((string) $grantReport->slug, $mappedSubmittable['slug']);
        $this->assertSame($grantReport->getChapterId(), $mappedSubmittable['chapterId']);
        $this->assertSame($grantReport->getCategoryId(), $mappedSubmittable['categoryId']);
        $this->assertSame($grantReport->getSeasonId(), $mappedSubmittable['seasonId']);
    }

    public function testMappedTab(): void
    {
        $grantReport = $this->muffin(GrantReport::class, ['form_id' => $this->reportForm->id]);
        Request::setRouteResolver($this->routeResolver($grantReport));
        Request::merge(['tabSlug' => (string) ($tab = $this->muffin(Tab::class))->slug]);

        $mappedTab = app(ManagerGrantReportFormView::class)->mappedTab();

        $this->assertSame((string) $tab->slug, $mappedTab['slug']);
        $this->assertSame($tab->id, $mappedTab['id']);
    }

    public function testTabTypes(): void
    {
        $user = $this->setupUserWithRole('ProgramManager');
        Consumer::set(new UserConsumer($user));

        $types = app(ManagerGrantReportFormView::class)->tabTypes();

        $this->assertNotFalse(array_search(Tab::TYPE_ATTACHMENTS, $types));
        $this->assertNotFalse(array_search(Tab::TYPE_CONTRIBUTORS, $types));
        $this->assertNotFalse(array_search(Tab::TYPE_FIELDS, $types));
        $this->assertFalse(array_search(Tab::TYPE_ELIGIBILITY, $types));
    }

    public function testGrantReportBreadcrumbs(): void
    {
        $grantReport = $this->muffin(GrantReport::class, ['form_id' => $this->reportForm->id]);
        $managerView = app(ManagerGrantReportFormView::class);
        $entrantView = app(EntrantGrantReportFormView::class);
        Request::setRouteResolver($this->routeResolver($grantReport));

        $managerBreadcrumbs = collect(json_decode($managerView->breadcrumbs(), true));
        $entrantBreadcrumbs = collect(json_decode($entrantView->breadcrumbs(), true));

        $this->assertEquals(trans('grant_reports.titles.manager'), Arr::get($managerBreadcrumbs, '0.text'));
        $this->assertEquals(route('grant-report.manager.index'), Arr::get($managerBreadcrumbs, '0.link'));
        $this->assertEquals($managerView->form()->name, Arr::get($managerBreadcrumbs, '1.text'));
        $this->assertEquals(trans('grant_reports.titles.entrant'), Arr::get($entrantBreadcrumbs, '0.text'));
        $this->assertEquals(route('grant-report.entrant.index'), Arr::get($entrantBreadcrumbs, '0.link'));
        $this->assertEquals($managerView->form()->name, Arr::get($entrantBreadcrumbs, '1.text'));
    }

    public function testReadOnlyIfFormIsLockedWhenSubmitted(): void
    {
        $this->reportForm->settings = FormSettings::create(['lockWhenSubmitted' => true]);
        $this->reportForm->save();
        $grantReport = $this->muffin(GrantReport::class, ['form_id' => $this->reportForm->id, 'submitted_at' => now()]);
        Request::setRouteResolver($this->routeResolver($grantReport));

        $view = app(EntrantGrantReportFormView::class);

        $this->assertTrue($view->readOnly());
    }

    public function testLockWhenSubmittedShouldReturnFalseForManagerView()
    {
        $this->reportForm->settings = FormSettings::create(['lockWhenSubmitted' => true]);
        $this->reportForm->save();
        Request::merge(['formSlug' => $this->reportForm->slug]);

        $view = app(ManagerGrantReportFormView::class);

        $this->assertFalse($view->lockWhenSubmitted());
    }

    protected function routeResolver(GrantReport $grantReport): callable
    {
        return function () use ($grantReport) {
            $router = $this->mock('router');
            $router->shouldReceive('parameter')->with('grantReport', null)->andReturn($grantReport);

            return $router;
        };
    }
}
