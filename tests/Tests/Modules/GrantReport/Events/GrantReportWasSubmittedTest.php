<?php

namespace Tests\Modules\GrantReport\Events;

use AwardForce\Library\Request\RequestSource;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\GrantReports\Events\GrantReportWasSubmitted;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use Tests\IntegratedTestCase;

class GrantReportWasSubmittedTest extends IntegratedTestCase
{
    public function testWebhookPayloadIsIntact(): void
    {
        $grantReport = $this->muffin(GrantReport::class);
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'form_id' => $grantReport->formId, 'type' => 'text']);

        app(ValuesService::class)->setValuesForObject([
            (string) $field->slug => 'Test Value',
        ], $grantReport);

        $event = new GrantReportWasSubmitted($grantReport->fresh());

        $payload = $event->webhookPayload();
        $this->assertArrayHasKey('entrant', $payload);
        $this->assertEquals((string) $grantReport->user->slug, $payload['entrant']['slug']);
        $this->assertArrayHasKey('entry_fields', $payload);
        $this->assertEquals((string) $field->slug, $payload['entry_fields'][0]['slug']);
        $this->assertEquals('Test Value', $payload['entry_fields'][0]['value']);
        $this->assertArrayHasKey('event', $payload);
        $this->assertEquals(trans('webhooks.subscription_events.GrantReportWasSubmitted'), $payload['event']);
        $this->assertArrayHasKey('source', $payload);
        $this->assertEquals(RequestSource::UI, $payload['source']);
        $this->assertArrayHasKey('timestamp', $payload);
        $this->assertArrayHasKey('updated', $payload);
    }
}
