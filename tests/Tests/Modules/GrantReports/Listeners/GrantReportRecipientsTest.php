<?php

namespace Tests\Modules\GrantReports\Listeners;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Broadcasts\Models\Broadcast;
use AwardForce\Modules\Broadcasts\Models\RecipientsPreparer;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\GrantReports\Listeners\GrantReportRecipients;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Grants\Models\GrantStatus;
use AwardForce\Modules\Identity\Users\Models\User;
use Illuminate\Support\Arr;
use Tests\IntegratedTestCase;

final class GrantReportRecipientsTest extends IntegratedTestCase
{
    public function testCreatesRecipientsList(): void
    {
        Feature::shouldReceive('enabled')->andReturnTrue();
        Consumer::spy()
            ->makePartial()
            ->shouldReceive('can')
            ->with('view', 'Grants')
            ->andReturnTrue();

        $user = $this->muffin(User::class);
        Membership::register(current_account(), $user, 'da_DK');
        $user->addToAccount($this->account);
        $category = $this->muffin(Category::class);
        $category->makeChildOf($parent = $this->muffin(Category::class));
        $chapter = $this->muffin(Chapter::class);

        $grantStatus = $this->muffin(GrantStatus::class);
        $entry = $this->muffin(Entry::class, [
            'category_id' => $category->id,
            'chapter_id' => $chapter->id,
            'user_id' => $user->id,
            'account_id' => $this->account->id,
            'grant_status_id' => $grantStatus->id,
            'grant_ends_at' => now()->toDateString(),
        ]);
        $form = $this->muffin(Form::class, [
            'type' => fn() => Form::FORM_TYPE_REPORT,
        ]);
        $grantReport = $this->muffin(GrantReport::class, [
            'account_id' => $this->account->id,
            'entry_id' => $entry->id,
            'form_id' => $form->id,
        ]);

        app(GrantReportRecipients::class)->handle($preparer = $this->setupPreparer());

        $recipients = $preparer->recipients();
        $this->assertCount(1, $recipients);
        $this->assertEquals($entry->entrant->email, ($recipient = $recipients->first())->email());
        $this->assertSame(Arr::get($data = $recipient->data(), 'application_name'), $entry->title);
        $this->assertSame(Arr::get($data, 'application_slug'), (string) $entry->slug);
        $this->assertSame(Arr::get($data, 'report_name'), $grantReport->name);
        $this->assertSame(Arr::get($data, 'report_status'), $grantReport->status);
        $this->assertSame(Arr::get($data, 'parent_category'), $parent->name);
        $this->assertSame(Arr::get($data, 'chapter'), $chapter->name);
        $this->assertSame(Arr::get($data, 'category'), $category->name);
    }

    private function setupPreparer()
    {
        $broadcast = new Broadcast;
        $broadcast->type = 'grant-report-recipients';
        $broadcast->filters = [];
        $broadcast->seasonId = $this->account->activeSeason()->id;
        $broadcast->accountId = $this->account->id;

        return new RecipientsPreparer($broadcast);
    }
}
