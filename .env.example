APP_ENV=local
APP_DEBUG=true
APP_KEY_NEW=base64:kJZlrAzSspAtF5h6Pe4mK21bIoeDWBC8zhMb8YfVh0o=
APP_DOMAIN=awardforce.local
API_DOMAIN=api.awardforce.local
API_STRICT=true
DEBUGBAR_ENABLED=true

AF_REGIONS=eu,au,us,ca
AF_REGION=au

DB_DRIVER=mysql
DB_HOST=localhost
DB_DATABASE=homestead
DB_USERNAME=homestead
DB_PASSWORD=secret
DB_QUERYLOG=true

# Format: driver://username:password@host:port/database
DB_CONNECTIONS=mysql://awardforce:awardforce@localhost:3306/awardforce

HOLOCRON_DB_DRIVER=mysql
HOLOCRON_DB_HOST=localhost
HOLOCRON_DB_DATABASE=holocron
HOLOCRON_DB_USERNAME=homestead
HOLOCRON_DB_PASSWORD=secret
HOLOCRON_ENABLED=true

TESTING_DB_CONNECTION=mysql://awardforce:awardforce@localhost:3306/awardforce
TESTING_DB_DROPDB=

# Passport database connection
IDENTITY_DB_HOST=127.0.0.1
IDENTITY_DB_PORT=3306
IDENTITY_DB_DATABASE=homestead
IDENTITY_DB_USERNAME=homestead
IDENTITY_DB_PASSWORD=secret

CACHE_DRIVER=redis
LOG_DRIVER=daily
QUEUE_DRIVER=redis
SESSION_DRIVER=file
COOKIE_SECURE=false

#AWS storage configuration
AWS_ACCOUNT_ID=
S3_ACCESS_KEY=
S3_SECRET_KEY=
S3_SECURE_BUCKET=

S3_REGION_SYDNEY=
S3_BUCKET_SYDNEY=

S3_REGION_AU=
S3_BUCKET_AU=

S3_REGION_EU=
S3_BUCKET_EU=

S3_REGION_US=
S3_BUCKET_US=

S3_BUCKET_AU_DELETED=
S3_BUCKET_EU_DELETED=
S3_BUCKET_US_DELETED=

#Assets configuration
CLOUDFRONT_PRIVATE_KEY_PATH=
CLOUDFRONT_KEY_PAIR_ID=
CLOUDFRONT_DOMAIN_ASSETS=

CLOUDFRONT_DOMAIN_AU=
CLOUDFRONT_DOMAIN_EU=
CLOUDFRONT_DOMAIN_US=
CLOUDFRONT_DOMAIN_CA=

#Images configuration, caching, sizing
IMGIX_HTTPS=false

IMGIX_SECURE_KEY_AU=
IMGIX_SECURE_KEY_EU=
IMGIX_SECURE_KEY_US=

IMGIX_DOMAIN_AU=
IMGIX_DOMAIN_EU=
IMGIX_DOMAIN_US=

IMGIX_HOLOCRON_DOMAIN_IRELAND=
IMGIX_HOLOCRON_SECURE_KEY_IRELAND=

MAIL_DRIVER=log
# i.e. api.eu.mailgun.net
MAILGUN_ENDPOINT=

MAILGUN_AF_DOMAIN=
MAILGUN_AF_KEY=

MAILGUN_GG_DOMAIN=
MAILGUN_GG_KEY=

WKHTMLTOPDF=wkhtmltopdf

AUTH_DOMAIN=auth.awardforce.local

AUTH_GOOGLE_ID=
AUTH_GOOGLE_SECRET=
AUTH_TWITTER_ID=
AUTH_TWITTER_SECRET=
AUTH_FACEBOOK_ID=
AUTH_FACEBOOK_SECRET=
AUTH_GITHUB_ID=
AUTH_GITHUB_SECRET=

BROADCAST_DRIVER=pusher
PUSHER_KEY=
PUSHER_SECRET=
PUSHER_APP_ID=
PUSHER_APP_CLUSTER=

NEXMO_KEY=
NEXMO_SECRET=
NEXMO_TIMEOUT=5.0
NEXMO_NUMBER_USA=
NEXMO_DEFAULT_UAE=********
NEXMO_CONFIRMATION_UAE=CR4CE

URL2PNG_KEY=
URL2PNG_SECRET=

#Kessel API endpoint configuration
KESSEL_KEY=
KESSEL_DOMAIN=
KESSEL_ORIGIN=
KESSEL_ENDPOINT_MY_AF=
KESSEL_ENDPOINT_BILLING_PORTAL=

ROOT_PROVISIONING_DOMAINS=provisioning.au.awardforce.local,provisioning.eu.awardforce.local,provisioning.us.awardforce.local

#Provisioning payment configuration
CHARGIFY_ENDPOINT=
CHARGIFY_KEY=

WHITE_LABEL_DOMAINS_AWARDFORCE=awardforce.local,awardsplatform.com
WHITE_LABEL_DOMAINS_GOODGRANTS=goodgrants.local,grantsplatform.com

CUSTOM_DOMAINS_CNAME=.cr4ce.com

DATADOME_SERVER_KEY=
DATADOME_CLIENT_KEY=

MY_AWARDFORCE_DOMAIN=http://myaf-app.local
MY_GOODGRANTS_DOMAIN=http://mygg-app.local

CONSUL_HOST=http://consul.local

#Tracker
INTERCOM_AF_ID=
INTERCOM_AF_KEY=
INTERCOM_GG_ID=
INTERCOM_GG_KEY=

#FAQ configuration
ELEVIO_AF_ACCOUNT_ID=
ELEVIO_GG_ACCOUNT_ID=

#Provisioning payment gateway configuration
STRIPE_API_KEY=
STRIPE_PUBLIC_KEY=
SUBSCRIPTION_TAX_ITEM=

#AWS queue configuration
SQS_REGION=

SQS_BULK_DOWNLOADS=speeder
SQS_BULK_DOWNLOADS_COMPLETE=speeder-completed

#Client success integration tool
ZENDESK_AF_KEY=
ZENDESK_GG_KEY=

ELASTIC_HOST=127.0.0.1
ELASTIC_PORT=9200
ELASTIC_USER=
ELASTIC_PASS=
ELASTIC_SCHEME=http
ELASTIC_INDEX=af_index
ELASTIC_LOGGING_ENABLED=false
ELASTIC_LOGGING_LEVEL=all

#Provisioning payment gateway configuration
SUBSCRIPTION_GATEWAY=chargebee

CHARGEBEE_CF_SITE=
CHARGEBEE_CF_FULL_ACCESS_KEY=
CHARGEBEE_CF_PUBLISHABLE_KEY=

CHARGEBEE_AF_SITE=
CHARGEBEE_AF_FULL_ACCESS_KEY=
CHARGEBEE_AF_PUBLISHABLE_KEY=

#Executable binaries
PANDOC_BINARY=/opt/homebrew/bin/pandoc
LIBREOFFICE_BINARY=/opt/homebrew/bin/soffice

#Tracker
HEAP_APP_ID=

SLACK_ENDPOINT=null
SLACK_ENG_NOTIFICATIONS_CHANNEL=
SLACK_BILLING_MEMBER_IDS=
EXPORT_TMP_DIR=/tmp/files/export

#Communication tool configuration
ANNOUNCEKIT_WIDGET_AF_ID=321
ANNOUNCEKIT_WIDGET_GG_ID=123

FIREBASE_API_KEY=af4_key
FIREBASE_AUTH_DOMAIN=af4_auth_domain
FIREBASE_DATABASE_URL=https://af4.database.url
FIREBASE_PROJECT_ID=af4
FIREBASE_STORAGE_BUCKET=af4_bucket
FIREBASE_MESSAGING_SENDERID=af4_sender_id
FIREBASE_APP_ID=af4_app_id
FIREBASE_MEASUREMENT_ID=af4_measurment_id

FIREBASE_TYPE=service_account
FIREBASE_PRIVATE_KEY_ID=
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----
-----END PRIVATE KEY-----"
FIREBASE_CLIENT_EMAIL=
FIREBASE_CLIENT_ID=
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=
FIREBASE_UNIVERSE_DOMAIN=googleapis.com
FIREBASE_FIRESTORE_DATABASE=
FIREBASE_THROTTLE_TIME=1000

FIREBASE_USE_EMULATOR=false
# changing encrypt data requires existing Firestore collection to be deleted
FIREBASE_ENCRYPT_DATA=false
FIREBASE_EMULATOR_DATASOURCE=firestore
FIREBASE_EMULATOR_FIRESTORE_HOST=127.0.0.1
FIREBASE_EMULATOR_FIRESTORE_PORT=8080
FIRESTORE_EMULATOR_HOST=127.0.0.1:8080


#Enable to log stack traces in the logs
LOG_INCLUDE_STACKTRACES=false

#SMS configuration
TWILIO_CUSTOM_SENDER=CR4CE
TWILIO_ACCOUNT_SID=1234
TWILIO_DEFAULT_SERVICE_ID=1234
TWILIO_CUSTOM_SERVICE_ID=1234
TWILIO_VERIFY_SERVICE_ID=1234
TWILIO_DO_NOT_RETRY_ERROR_CODES=
TWILIO_API_KEY=
TWILIO_API_SECRET=

DATADOG_FRONTEND_CLIENT_TOKEN=
DATADOG_FRONTEND_APPLICATION_ID=
DATADOG_FRONTEND_SITE=
DATADOG_FRONTEND_SERVICE=
DATADOG_FRONTEND_ENV=
DATADOG_FRONTEND_SAMPLE_RATE=100
DATADOG_FRONTEND_REPLAY_RATE=20
DATADOG_FRONTEND_TRACK_USER_INTERACTIONS=true

HYPER_FORMULA_LICENSE_KEY=gpl-v3

#AWS Media converter configuration.
MEDIA_CONVERT_TEMPLATE_AU
MEDIA_CONVERT_TEMPLATE_EU
MEDIA_CONVERT_TEMPLATE_US
MEDIA_CONVERT_TEMPLATE_CA

MEDIA_CONVERT_VERSION_AU
MEDIA_CONVERT_VERSION_EU
MEDIA_CONVERT_VERSION_US
MEDIA_CONVERT_VERSION_CA

MEDIA_CONVERT_ENDPOINT_AU
MEDIA_CONVERT_ENDPOINT_EU
MEDIA_CONVERT_ENDPOINT_US
MEDIA_CONVERT_ENDPOINT_CA

MEDIA_CONVERT_ROLE_AU
MEDIA_CONVERT_ROLE_EU
MEDIA_CONVERT_ROLE_US
MEDIA_CONVERT_ROLE_CA

# Can be used if S3 region differs from MediaConvert region
MEDIA_CONVERT_REGION_HK=

AWS_KEY=
AWS_SECRET=

#AWS encryption service configuration
KMS_REGION=
KMS_KEY_MAXIMUM=

API_SUBTYPE="Award Force|Creative Force"

#Marketing/Sales integration
HUBSPOT_ENDPOINT=
HUBSPOT_KEY=

THROTTLE_REQUESTS=true

# Dashboards
DASHBOARD_ENABLED=true
EMBEDDABLE_ENVIRONMENT=
EMBEDDABLE_API_KEY=
EMBEDDABLE_BASE_URL=
EMBEDDABLE_AF_PROGRAM_MANAGER_DASHBOARD_ID=
EMBEDDABLE_AF_CHAPTER_MANAGER_DASHBOARD_ID=
EMBEDDABLE_GG_PROGRAM_MANAGER_DASHBOARD_ID=
EMBEDDABLE_GG_CHAPTER_MANAGER_DASHBOARD_ID=

# 5-bit worker identifiers (int<0,31>) used by our snowflake ID library
WORKER_ID=
PROCESS_ID=
