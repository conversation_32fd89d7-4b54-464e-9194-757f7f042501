import { execSync } from "child_process";

const lintSubjectsFilter =
	/resources\/assets\/js\/.*\.(js|ts|vue|mjs)/i;

const runEslintSync = (file, autofix) => {
	try {
		const output = execSync(`npx eslint ${autofix ? '--fix' : ''} ${file}`, { encoding: 'utf-8', stdio: 'pipe' });
		return { file, output };
	} catch (error) {
		return { file, output: error.stdout.toString().trim() + error.stderr.toString().trim() };
	}
}

const EsLintAll = (stagedFiles, autofix) => {
	const filesToLint = stagedFiles
		.filter((file) => lintSubjectsFilter.test(file));

	const failedFiles = [];

	const results = filesToLint
		.map(file => {
			const result = runEslintSync(file, autofix);
			if(result.output) {
				failedFiles.push(file);
			}
			return result.output || null
		})
		.filter(output => output !== null)

	return {
		success: results.length === 0,
		message: results.join('\n\n'),
		failed: failedFiles
	};
};


const EsLintOne = (file, autofix) => {
	if(!lintSubjectsFilter.test(file)) {
		return {
			success: true,
			message: '',
			failed: []
		};
	}

	const result = runEslintSync(file, autofix);
	return {
		success: !result.output,
		message: result.output,
		failed: result.output ? [file] : []
	};
}

export { EsLintAll, EsLintOne };
