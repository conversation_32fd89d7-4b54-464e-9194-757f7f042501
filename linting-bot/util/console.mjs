const format = input => {
	if(!Array.isArray(input)) {
		input = [input];
	}

	input = input.reduce((acc, item) => acc.concat(item.split("\n")), []);

	const length = input.reduce((acc, item) => Math.max(acc, item.length), 0);

	input = input.map(item => `* ${item}${' '.repeat(length-item.length)} *`);
	input.push('*'.repeat(length+4));
	input.unshift('*'.repeat(length+4));
	input.push("\n\n");
	input.unshift("\n\n");

	return input.join("\n");
}

export default {
	error: input => {console.error(format(input));},
	warn: input => {console.warn(format(input));},
	log: input => {console.log(format(input));}
};
