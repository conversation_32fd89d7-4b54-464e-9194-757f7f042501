import Console from './console.mjs';
import { execSync } from "child_process";

const run = command => execSync(command, { encoding: 'utf-8', stdio: 'pipe' }).trim();

const checkout = branch => run(`git checkout ${branch}`);

const create = (branch, from = 'origin/master') => run(`git checkout -b ${branch} ${from}`);

const push = () => run(`git push`);

const getHash = () => run('git rev-parse --short HEAD');

const commit = message => {
	try {
		run(`git commit --no-verify -a -m "${message}"`);
	} catch (error) {
		if (!error.stdout.includes('nothing to commit')) {
			Console.error(['Commit failed', `\ngit commit --no-verify -a -m "${message}"`, "\n", error.stdout]);
			process.exit(1);
		}
	}
};

const reset = hash => run(`git reset --soft ${hash}`);

const stage = hash => run(`git all --all`);

const getCurrentBranch = () => run('git rev-parse --abbrev-ref HEAD');

const getStagedFiles = () => execSync('git diff --cached --name-only', { encoding: 'utf-8' });

const getDiffFiles = (from, to = 'origin/master') => execSync(`git diff --name-only ${to}...${from}`, { encoding: 'utf-8' });

const uncomittedChanges = () => run(`git status --short --untracked-files=no`).split("\n");

const getBranches = () => run(`git branch -l` )
	.split('\n')
	.map(branch => branch.trim().replace(/(\*|\s*)(.*)/g, '$2').trim());

//  git diff --name-only feature/js-rooting-and-modulizer origin/master | grep -E "^resources/assets/.*\.(vue|js|ts|mjs)$" | tee changed_files.txt | xargs -I {} sh -c '[ -f "{}" ] && npx eslint "{}"'
export default { checkout, create, getHash, commit, reset, getCurrentBranch, getStagedFiles, getBranches, stage, push, getDiffFiles, uncomittedChanges };
