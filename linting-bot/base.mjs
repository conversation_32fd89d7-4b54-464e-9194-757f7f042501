import Console from './util/console.mjs';
import { EsLintAll, EsLintOne } from './util/eslint.mjs';
import fs from 'fs';
import Git from './util/git.mjs';
import reader from "readline-sync";

const uncomittedChanges = Git.uncomittedChanges().filter(file => file.trim().length > 0);

if(uncomittedChanges.length > 0) {
	console.log('Uncommitted changes:');
	console.log(uncomittedChanges.map(file => file.trim()).join('\n'));
	console.log('');

	if(reader.keyInYN('Do you want to commit them?')) {
		const message = reader.question('Commit message [linting base branch]:', {defaultInput: 'linting base branch'});
		if(!message) {
			Console.error('Commit message is required.');
			process.exit(0);
		}
		Git.commit(message);
	} else {
		Console.error('Commit or stash your changes and retry.');
		process.exit(0);
	}
}

const currentBranch = Git.getCurrentBranch();
const branches = Git.getBranches();

var baseBranch = '', workingBranch = '', failures;

var fromSession = false;

if(fs.existsSync('./linting-bot/.failed')) {
	({ baseBranch, workingBranch, failures } = JSON.parse(fs.readFileSync('./linting-bot/.failed')));
	console.log(`\nSaved session found:\nBase branch: ${baseBranch}\nWorking branch: ${workingBranch}\n`);
	if(reader.keyInYN('Do you want to use saved session?')) {
		fromSession = true;
		console.log('Retrying failed linting...');
	} else {
		fs.unlinkSync('./linting-bot/.failed');
		failures = undefined;
	}
}

if(!fromSession) {
	console.log('Linting base branch from scratch...');

	baseBranch = reader.question(`Enter base branch name [${currentBranch}---base]: `, {
		defaultInput: `${currentBranch}---base` });

	workingBranch = reader.question(`Enter working branch name [${currentBranch}]:`, {
		defaultInput: currentBranch });

	if(!branches.includes(workingBranch)) {
		Console.error(`Working branch ${workingBranch} does not exist.`);
		process.exit(0);
	}
}

Git.checkout(workingBranch);
var touchedFiles = failures || ((Git.getDiffFiles(workingBranch).split('\n') || [] )
	.filter(file => file && fs.existsSync(`./${file}`)));

if(!branches.includes(baseBranch)) {
	if(reader.keyInYN(`Base branch ${baseBranch} does not exist. Do you want to create it from origin/master?`)) {
		Git.create(baseBranch);
	} else {
		Console.error('Create base branch and retry.');
		process.exit(0);
	}
} else {
	Git.checkout(baseBranch);
}

touchedFiles = touchedFiles.filter(file => file && fs.existsSync(`./${file}`));

if(touchedFiles.length > 20) {
	if(!reader.keyInYN(`There lots of files (${touchedFiles.length}) modified, are you sure you've merged origin/master to your branch?`)) {
		Console.error('Merge origin/master to your branch and retry.');
		process.exit(0);
	}
}

var eslintResult = {
	success: true,
	message: '',
	failed: []
};

var success = true;

if(fromSession) {
	for(const touchedFile of touchedFiles) {
		let result;
		while(true) {
			result = EsLintOne(touchedFile, true);
			if(result.success === false) {
				console.log(result.message);
				if(reader.keyInYN('Do you want to check again?')) {
					continue;
				}
			}
			break;
		}

		if(result.success === false) {
			eslintResult.success = false;
			eslintResult.message += result.message + '\n\n';
			eslintResult.failed.push(touchedFile);
		}
	}
} else {
	eslintResult = EsLintAll(touchedFiles, true);
}

if(eslintResult.success === false) {
	Git.commit(`linted with errors on ${new Date().toLocaleString()}`);

	console.log('')
	console.error(eslintResult.message);
	Console.error([
		'ESLint failed on base branch. Fix it manually and retry.\n',
		`1. go to your base branch (${baseBranch})`,
		`2. fix the errors manually`,
		`3. commit the changes`,
		`4. go to your working branch (${workingBranch})`,
		`5. merge base branch (${baseBranch}) to working branch (${workingBranch})`,
		`6. rerun check`
	]);

	const todo = {
		baseBranch,
		workingBranch,
		failures: eslintResult.failed
	};

	Git.checkout(currentBranch);
	fs.writeFileSync('./linting-bot/.failed', JSON.stringify(todo, null, 2));

	process.exit(0);
}

if(fs.existsSync('./linting-bot/.failed')){
	fs.unlinkSync('./linting-bot/.failed');
}

Git.commit(`linted on ${new Date().toLocaleString()}`);
Git.checkout(currentBranch);

Console.log([
	'Base branch linted successfully.\n',
	`1. go to your base branch (${baseBranch})`,
	`2. push the changes`,
	`3. go to your working branch (${workingBranch})`,
	`4. merge base branch (${baseBranch}) to working branch (${workingBranch})`,
	`5. open PR from base branch (${baseBranch}) to master`
]);
