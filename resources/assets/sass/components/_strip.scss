/*!
 * Strip - A Less Intrusive Responsive Lightbox - v<%= pkg.version %>
 * (c) 2014-<%= grunt.template.today("yyyy") %> <PERSON>
 *
 * http://www.stripjs.com
 *
 * Licensing:
 * - Commercial: http://www.stripjs.com/license
 * - Non-commercial: http://creativecommons.org/licenses/by-nc-nd/3.0
 */

/* An overlay that will be inserted before .strp-window element */
.strp-overlay {
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 99998;
  background-color: $modal-backdrop-bg;
  @include opacity($modal-backdrop-opacity);
}

.strp-window {
  position: fixed;
  width: 0;
  height: 100%;
  overflow: hidden;
  background: #292929;
  font: 13px/20px 'Lucida Sans', 'Lucida Sans Unicode', 'Lucida Grande', Verdana, Arial, sans-serif;
}

.strp-window.strp-vertical {
  height: 0;
  width: 100%;
}

/* margin is added around the window to keep a visual reference
 * to the underlying page at all times.
 */
.strp-window { margin-left: 40px; }

.strp-window.strp-vertical { margin-left: 0; margin-bottom: 40px; }

/* fullscreen on smaller screens (iPhone 6+ and smaller)
 * since min-width is used to make this work it won't show on IE8, but
 * we're not expecting that browser to have a mobile sized screen anyway
 */
@media all and (max-width: 414px) and (orientation: portrait),
  all and (max-width: 736px) and (max-height: 414px) {
  .strp-window.strp-horizontal { min-width: 100%; }
}
@media all and (max-height: 414px) and (orientation: landscape),
  all and (max-height: 736px) and (max-width: 414px){
  .strp-window.strp-vertical { min-height: 100%; }
}

/* z-index */
.strp-window,
.strp-spinner-move {
  z-index: 99999;
}

/* reset box-sizing */
.strp-window,
.strp-window [class^='strp-'],
.strp-spinner-move,
.strp-spinner-move [class^='strp-'] {
  box-sizing: border-box;
}

/* Chrome hack, this fixes a visual glitch when quickly toggling a video */
.strp-window { transform: translateZ(0px); }

/* some properties on the window are used to toggle things
 * like margin and the fullscreen mode,
 * we reset those properties after measuring them
 */
.strp-measured {
  margin: 0 !important;
  min-width: 0 !important;
  min-height: 0 !important;
}

.strp-pages {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* sides */
.strp-side-right { top: 0; right: 0; }

.strp-side-right .strp-pages { top: 0; right: 0; }

.strp-side-right .strp-page { top: 0; right: 0; }

.strp-side-right .strp-close { top: 0; right: 0; }

.strp-side-left { top: 0; left: 0; }

.strp-side-left .strp-pages { top: 0; left: 0; }

.strp-side-left .strp-page { top: 0; left: 0; }

.strp-side-left .strp-close { top: 0; right: 0; }

.strp-side-top { top: 0; left: 0; }

.strp-side-top .strp-pages { top: 0; left: 0; }

.strp-side-top .strp-page { top: 0; left: 0; }

.strp-side-top .strp-close { top: 0; right: 0; }

.strp-side-bottom { bottom: 0; left: 0; }

.strp-side-bottom .strp-pages { bottom: 0; left: 0; }

.strp-side-bottom .strp-page { bottom: 0; left: 0; }

.strp-side-bottom .strp-close { top: 0; right: 0; }

.strp-page {
  position: absolute;
  width: 100%;
  height: 100%;
}

.strp-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
  text-align: center;
  background: #292929;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

/* youtube & vimeo use overlap:false, this adds padding
   to make sure buttons don't overlap the content.
   navbutton = 72 = 54 + (2 * 9 margin)
   closebutton = 48
*/
.strp-no-overlap .strp-container { padding: 48px 72px; }

.strp-no-overlap.strp-no-sides .strp-container { padding: 48px 0; }

.strp-vertical .strp-no-overlap .strp-container { padding: 0 72px; }

.strp-vertical .strp-no-overlap.strp-no-sides .strp-container { padding: 0 48px; }

.strp-hovering-clickable .strp-container { cursor: pointer; }

.strp-content-element {
  position: absolute;
  top: 50%;
  left: 50%;
}

.strp-content-element iframe {
  float: left;
  width: 100%;
  height: 100%;
}

.strp-container img {
  display: inline-block;
  vertical-align: middle;
}

.strp-info {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  color: #efefef;
  font-size: 13px;
  line-height: 20px;
  background: #000;
  -webkit-text-size-adjust: none;
  text-size-adjust: none;
}

.strp-info-padder {
  display: block;
  overflow: hidden;
  padding: 12px;
  position: relative;
  width: auto;
}

.strp-caption {
  width: auto;
  display: inline;
  white-space: wrap;
}

.strp-position {
  color: #b3b3b3;
  float: right;
  line-height: 21px;
  opacity: 0.99;
  position: relative;
  text-align: right;
  margin-left: 15px;
  white-space: nowrap;
}

/* links */
.strp-info a,
.strp-info a:hover {
  color: #ccc;
  border: 0;
  background: none;
  text-decoration: underline;
}

.strp-info a:hover { color: #eee; }

/* < > */
.strp-nav {
  position: absolute;
  top: 50%;
  width: 54px;
  height: 72px;
  margin: 0 9px;
  margin-top: -36px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.strp-nav-previous { left: 25px; }

.strp-nav-next { right: 25px; left: auto; }

.strp-nav-disabled { cursor: default; }

.strp-nav-button {
  float: left;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}

.strp-nav-button-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #101010;
}

.strp-nav-button-icon {
  float: left;
  position: relative;
  height: 100%;
  width: 100%;
  zoom: 1;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

/* X */
.strp-close {
  position: absolute;
  width: 48px;
  height: 48px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.strp-close-background,
.strp-close-icon {
  position: absolute;
  top: 12px;
  left: 12px;
  height: 26px;
  width: 26px;
  background-position: 50% 50%;
  background-repeat: no-repeat;
}

.strp-close-background {
  filter: alpha(opacity=80);
  opacity: .8;
  background-color: #101010;
}

.strp-close:hover .strp-close-background { background-color: #161616; }

.strp-has-error .strp-container { background-color: #ca3434; }

.strp-error {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 240px;
  height: 240px;
  margin-left: -120px;
  margin-top: -120px;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: url('../img/strip-skins/strip/error.svg?rev=4');
}

.strp-no-svg .strp-error { background-image: url('../img/strip-skins/strip/error.png?rev=4'); }

/* Spinner - loading icon, wrapped by a div that moves it */
.strp-spinner-move {
  position: fixed;
  top: 0px;
  right: 0px;
  height: 48px;
  width: 0;

  /* ensure overflow during a jQuery animation */
  overflow: visible !important;
}

.strp-spinner-move.strp-vertical { width: 48px; height: 0; }

.strp-spinner {
  width: 48px;
  height: 48px;
  float: left;
  position: relative;
}

/* different sides */
.strp-spinner-move.strp-side-top { top: 0; right: 0; bottom: auto; left: auto; }

.strp-spinner-move.strp-side-bottom { top: auto; right: 0; bottom: 0; left: auto; }

.strp-spinner-move.strp-side-right .strp-spinner { margin-left: -48px; }

.strp-spinner-move.strp-side-left .strp-spinner { margin-right: -48px; float: right; }

.strp-spinner-move.strp-side-bottom .strp-spinner { margin-top: -48px; }

.strp-spinner-move.strp-side-top .strp-spinner { position: absolute; bottom: 0; right: 0; margin-bottom: -48px; }

@-moz-keyframes strp-spinner-spin { 100% { -moz-transform: rotate(360deg); } }
@-webkit-keyframes strp-spinner-spin { 100% { -webkit-transform: rotate(360deg); } }
@keyframes strp-spinner-spin { 100% { -webkit-transform: rotate(360deg); transform:rotate(360deg); } }

.strp-spinner-rotate,
.strp-spinner-frame {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.strp-spinner-line {
  position: absolute;
  left: 50%;
  top: 15px;
  width: 2px;
  margin-left: -1px;
  height: 3px;
  border-radius: 1px;
  z-index: 80; /* amount of lines, removed later on */
  color: inherit; /* color of the dots, inherited from text on the page */
}

/*
 * ===== Skin: strip =====
 */
/* < > */
.strp-window-skin-strip .strp-nav-button-background { background-color: transparent; }

.strp-window-skin-strip .strp-nav-previous .strp-nav-button-icon { background-image: url('../img/strip-skins/strip/previous.svg?rev=4');  }

.strp-window-skin-strip .strp-nav-next .strp-nav-button-icon { background-image: url('../img/strip-skins/strip/next.svg?rev=4'); }

/* IE7-8/no-svg (using a faded png) */
.strp-window-skin-strip.strp-no-svg .strp-nav-previous .strp-nav-button-icon { background-image: url('../img/strip-skins/strip/previous-faded.png?rev=4'); opacity: 1; }

.strp-window-skin-strip.strp-no-svg .strp-nav-next .strp-nav-button-icon { background-image: url('../img/strip-skins/strip/next-faded.png?rev=4'); opacity: 1; }

.strp-window-skin-strip .strp-nav .strp-nav-button-icon { opacity: .6; } /* normal state */
.strp-window-skin-strip.strp-mobile-touch .strp-nav .strp-nav-button-icon { opacity: 1; } /* mobile-touch always has normal states at full opacity */

/* < > : hover */
.strp-window-skin-strip .strp-nav:hover .strp-nav-button-icon,
.strp-window-skin-strip .strp-nav-hover .strp-nav-button-icon { opacity: 1; }

/* IE7-8/no-svg (unfaded png) */
.strp-window-skin-strip.strp-no-svg .strp-nav.strp-nav-previous:hover .strp-nav-button-icon,
.strp-window-skin-strip.strp-no-svg .strp-nav-hover.strp-nav-previous .strp-nav-button-icon { background-image: url('../img/strip-skins/strip/previous.png?rev=4'); }

.strp-window-skin-strip.strp-no-svg .strp-nav.strp-nav-next:hover .strp-nav-button-icon,
.strp-window-skin-strip.strp-no-svg .strp-nav-hover.strp-nav-next .strp-nav-button-icon { background-image: url('../img/strip-skins/strip/next.png?rev=4'); }

/* Reduce < > button size on narrow screens (iPhone 6 and smaller) */
@media all and (max-width: 375px) and (orientation: portrait),
  all and (max-height: 375px) and (max-width: 667px) {
  .strp-window-skin-strip .strp-nav {
    width: 48px;
    height: 60px;
    margin: 0 5px;
    margin-top: -30px;
  }

  /* < > */
  .strp-window-skin-strip .strp-nav-previous .strp-nav-button-icon { background-image: url('../img/strip-skins/strip/previous-small.svg?rev=4');  }

  .strp-window-skin-strip .strp-nav-next .strp-nav-button-icon { background-image: url('../img/strip-skins/strip/next-small.svg?rev=4'); }

  /* IE7-8/no-svg (using a faded png) */
  .strp-window-skin-strip.strp-no-svg .strp-nav-previous .strp-nav-button-icon { background-image: url('../img/strip-skins/strip/previous-small-faded.png?rev=4'); }

  .strp-window-skin-strip.strp-no-svg .strp-nav-next .strp-nav-button-icon { background-image: url('../img/strip-skins/strip/next-small-faded.png?rev=4'); }

  /* IE7-8/no-svg (unfaded png) */
  .strp-window-skin-strip.strp-no-svg .strp-nav.strp-nav-previous:hover .strp-nav-button-icon,
  .strp-window-skin-strip.strp-no-svg .strp-nav-hover.strp-nav-previous .strp-nav-button-icon { background-image: url('../img/strip-skins/strip/previous-small.png?rev=4'); }

  .strp-window-skin-strip.strp-no-svg .strp-nav.strp-nav-next:hover .strp-nav-button-icon,
  .strp-window-skin-strip.strp-no-svg .strp-nav-hover.strp-nav-next .strp-nav-button-icon { background-image: url('../img/strip-skins/strip/next-small.png?rev=4'); }

  /* also reduce padding
     navbutton = 58 = 48 + (2 * 5 margin)
     closebutton = 48
   */
  .strp-no-overlap .strp-container { padding: 48px 58px; }

  .strp-no-overlap.strp-no-sides .strp-container { padding: 48px 0; }

  .strp-vertical .strp-no-overlap .strp-container { padding: 0 58px; }

  .strp-vertical .strp-no-overlap.strp-no-sides .strp-container { padding: 0 48px; }
}

/* X */
.strp-window-skin-strip .strp-close .strp-close-icon { background-image: url('../img/strip-skins/strip/close.svg?rev=4'); opacity: .8; }

.strp-window-skin-strip .strp-close:hover .strp-close-icon { opacity: 1; }

/* IE7-8/no-svg */
.strp-window-skin-strip.strp-no-svg .strp-close .strp-close-icon { background-image: url('../img/strip-skins/strip/close.png?rev=4'); opacity: 1; }

/* here's how to have content slide in with the window when opening and closing  */
/*
.strp-window-skin-strip.strp-side-left.strp-opening  .strp-page,
.strp-window-skin-strip.strp-side-left.strp-closing  .strp-page { left: auto; right: 0; }
.strp-window-skin-strip.strp-side-right.strp-opening  .strp-page,
.strp-window-skin-strip.strp-side-right.strp-closing  .strp-page { left: 0; right: auto; }
.strp-window-skin-strip.strp-side-top.strp-opening  .strp-page,
.strp-window-skin-strip.strp-side-top.strp-closing  .strp-page { bottom: 0; top: auto; }
.strp-window-skin-strip.strp-side-bottom.strp-opening  .strp-page,
.strp-window-skin-strip.strp-side-bottom.strp-closing  .strp-page { bottom: auto; top: 0; }
*/
