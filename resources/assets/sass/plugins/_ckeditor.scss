@import 'ckeditor5/ckeditor5.css';

$colour-secondary-rgba: rgba($colour-secondary, 0.2);

:root {
  --ck-content-font-family: #{$font-primary-colour};
  --ck-content-font-size: $font-size-base;
  --ck-content-font-color: #{$font-primary-colour};
  --ck-color-text: #{$font-primary-colour};
  --ck-font-face: $font-primary;
  --ck-color-base-text: #{$font-primary-colour};
  --ck-balloon-arrow-drop-shadow: none;
  --ck-balloon-border-width: 1px;
  --ck-border-radius: 4px;
  --ck-button-border-radius: 4px;
  --ck-color-base-border: #{$font-primary-colour};
  --ck-color-focus-outer-shadow: none;
  --ck-color-input-border: #{$input-border};
  --ck-color-input-text: #{$input-color};
  --ck-drop-shadow-active: none;
  --ck-drop-shadow: none;
  --ck-focus-ring: 1px solid transparent;
  --ck-inner-shadow: none;
  --ck-spacing-standard: 12px;
  --ck-switch-button-inner-hover-shadow: none;
  --ck-ui-component-height: 15px;
  --ck-ui-component-width: 15px;
  --ck-color-toolbar-border: transparent;
  --ck-icon-font-size: 0.6em;
  --ck-color-button-on: #{$font-primary-colour};
  --ck-color-button-default-background: tranparent;
  --ck-color-button-default-hover-background: #{$colour-link-hover};
  --ck-color-button-default-active-background: #{$colour-secondary-rgba};
  --ck-color-button-on-background: #{$colour-secondary-rgba};
  --ck-color-button-on-hover-background: #{$colour-secondary-rgba};
  --ck-color-button-on-active-background: #{$colour-secondary-rgba};
  --ck-color-button-on-color: #{$font-primary-colour};
  --ck-color-button-action-background: #{$colour-primary};
  --ck-color-button-action-hover-background: #{$colour-primary-hover};
  --ck-color-button-action-active-background: #{$colour-primary-hover};
  --ck-color-button-action-disabled-background: #{$colour-disabled};
  --ck-color-button-action-text: #fff;
  --ck-color-base-action: #fff;
  --ck-color-link-default: #{$colour-link};
}

.ckeditor-wrapper {
  border: 1px solid #{$input-border};
  border-radius: 4px;
  width: 100%;
  display: block;

  &.ck-focused {
    outline: 2px solid #000;

    .ck.ck-toolbar {
      box-shadow: none;
      border-color: transparent;
    }

    .ck.ck-content {
      border: 1px solid transparent;
      background-color: #fff;
      box-shadow: none;
    }
  }
}

/* Toolbar */
.ck.ck-toolbar {
  border: none;
  .ck.ck-button {
    cursor: pointer;
    box-shadow: none;
    transition: background 0.2s linear, outline 0.1s linear;

    &:active,
    &:focus {
      color: #{$colour-link-active};
      outline: 2px solid #{$colour-link-active};
      border: 1px solid transparent;
      box-shadow: none;
    }

    &:hover:not(:active):not(a):not(.ck-disabled) {
      background-color: #{$colour-link-hover};
      color: #fff;
    }
  }

  /* Dropdown typography */
  .ck.ck-dropdown {
    .ck.ck-list {
      padding: 0;
      transition: background 0.2s linear, outline 0.1s linear;

      .ck-list-item-button  {
        &:focus, &:active, &:hover {
          outline: none;
          background-color: #{$colour-link};
          color: #fff;
          .ck-icon {
            color: #fff;
            fill: #fff;
          }
        }
      }

      .ck-list__item:not(:first-child):not(:last-child) {
        .ck-button {
          border-radius: 0;
          &:focus, &:active, &:hover {
            border-radius: 0;
          }
        }
      }
    }

    /* Matches the dropdown button label items with our style */
    .ck-heading_paragraph > .ck-button__label { font: 400 14px/20px $font-primary; }
    .ck-heading_heading1 > .ck-button__label { font: 300 30px/40px $font-primary; letter-spacing: -0.025em; font-size: $font-size-h1; }
    .ck-heading_heading2 > .ck-button__label { font: 300 24px/34px $font-primary; letter-spacing: -0.04em; font-size: $font-size-h2; }
    .ck-heading_heading3 > .ck-button__label { font: 300 22px/1.4 $font-primary; letter-spacing: -0.01em; font-size: $font-size-h3; }
    .ck-heading_heading4 > .ck-button__label { font: 600 17px/1.4 $font-primary; letter-spacing: -0.01em; font-size: $font-size-h4; }
    .ck-heading_heading5 > .ck-button__label { font: 600 15px/1.4 $font-primary; font-size: $font-size-h5; }
    .ck-heading_heading6 > .ck-button__label { font: 700 12px/1.4 $font-primary; font-size: $font-size-h6; }

    /* Fix for dropdown button label overflow */
    .ck-dropdown__button > .ck-button__label {
      overflow: initial;
    }
  }
}

/* Modals */
.ck-dialog,
.ck-balloon-panel {
  .ck-button {
    transition: background 0.2s linear, outline 0.1s linear;
    font-weight: 400;
    &:hover:not(a), &:focus:not(a), &:active:not(a) {
      box-shadow: none;
      border: 1px solid transparent;
      color: #fff;
    }
  }

  .ck-form__row,
  .ck-dialog__actions {
    .ck-button {
      &:focus:not(a):not(.ck-disabled),
      &:active:not(a):not(.ck-disabled) {
        background-color: #{$colour-link-hover};
        outline: 2px solid #{$colour-link-hover};
        outline-offset: 2px;
      }
    }
    /* Primary button */
    .ck-button-action {
      &:focus:not(a):not(.ck-disabled),
      &:active:not(a):not(.ck-disabled) {
        background-color: #{$colour-primary-hover};
        outline: 2px solid #{$colour-primary-hover};
        outline-offset: 2px;
      }
    }
  }

  .ck-input {
    border: 1px solid $input-border;
    border-radius: $input-border-radius;

    &:focus {
      outline: 2px solid #000;
      outline-offset: -2px;
      box-shadow: none;
    }
  }
}

/* Content */
.ck.ck-content {
  min-height: 146px;
  word-break: break-word;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  background-color: #fff;

  &.ck-focused {
    border: 1px solid transparent;
    box-shadow: none;
  }

  &.ck-read-only {
    background-color: $colour-disabled-input;
  }

  a {
    text-decoration: underline;
  }
}

/**
 Assert that the embedded media is playable (https://github.com/ckeditor/ckeditor5/issues/2773).
 It can also be archived by setting `editor.isReadOnly = false`,
 but I can't see that working with the current implementation of the entry fields.
*/
*[contenteditable='true'] .ck-media__wrapper > *:not(.ck-media__placeholder) {
  pointer-events: initial !important;
}

/**
 * Stops links with '{' in the editor from being clickable, as they are merge fields.
 * These links have a unique style to show they are merge fields.
 * Most importantly, this prevents users from breaking the app by trying to access URLs like http://test.com/entries&keyword={entry_slug}.
 */
.ck a[href*="{"] {
  pointer-events: none;
  cursor: default;
  color: #666;
  text-decoration: underline dotted;
}

.multilingual {
  .input-group {
    .ckeditor-wrapper {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

.ckeditor-wrapper {
  a {
    cursor: text;
  }

  table td {
    vertical-align: inherit;
  }
}

/* makes embedded video responsive */
figure.media {
  width: 100%;
  height: auto;
  overflow: visible;

  > div[data-oembed-url] {
    position: relative;
    width: 100%;
    padding-bottom: 56.25%; // 16:9 aspect ratio
    height: 0;
    overflow: hidden;

    iframe {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
    }
  }
}
