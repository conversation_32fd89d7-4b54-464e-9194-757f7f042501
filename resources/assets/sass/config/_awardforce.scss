@import '../theme-colors.awardforce.scss';
/*
|--------------------------------------------------------------------------
| Configuration
|--------------------------------------------------------------------------
|
| Store all global variables and font declarations here.
| No styles should be defined here.
|
*/

// Colours
$colour-primary: #8b1360;
$colour-primary-hover: #5e0d41;
$colour-secondary: #18aecf;
$colour-secondary-hover: #1388a1;
$colour-tertiary: #333;
$colour-tertiary-hover: #1a1919;
$colour-quaternary: #1d1d1d;
$colour-gray-sublight: lighten(#000, 65%) !default;
$colour-home-background: $theme-var-home-background;

// Links
$colour-link: #106779;
$colour-link-hover: #253342;
$colour-link-active: #137e94;

// Focus box
$colour-focus-box-text: #fff;
$colour-focus-box-background: $colour-link;

// Forms
$colour-form-box-text: #333;
$colour-form-box-background: $theme-var-form-box-background;
$colour-breadcrumbs: #edeff2;
$colour-filters: #f9f9f9;
$colour-form: #f4f4f4;
$colour-border: #ddd;

// Header
$colour-header-background: #000;
$colour-header-link: #8e96a2;
$colour-header-link-hover: #fff;

// Alerts
$colour-warning: #c84503;
$colour-warning-text: #fff;
$colour-info: #15869D;
$colour-info-text: #fff;
$colour-success: #147524;
$colour-success-text: #fff;
$colour-error: #e11b46;
$colour-error-text: #fff;
$colour-neutral: #fff;
$colour-neutral-text: #333;
$colour-info-box-heading: #106779;
$colour-info-box-icon: #106779;
$colour-alert-box-text: #ffffff;


$colour-warning-dark: #c84503;
$colour-error-hover: lighten($colour-error, 20);

// Status
$colour-disabled: #767676;
$colour-disabled-dark: #545454;
$colour-disabled-input: #f2f2f2;
$colour-danger: #bb0000;

// Tabs
$colour-tab: #767676;
$colour-tab-text: #ffffff;
$colour-tab-hover: #8b1360;
$colour-tab-active: #1d1d1d;

// Input
$colour-input-border: #6f6f6f;
$colour-input-border-focus: #000;
$colour-input-border-error: lighten($colour-error, 20%);
$colour-input-border-error-focus: darken($colour-input-border-error, 35%);

// Scoring
$colour-approved: #008441;
$colour-rejected: #bb0000;
$colour-unsure: #ff7500;

// PDF
$colour-pdf: #db4437;

// Navigation
$navigation-background: $colour-quaternary;
$navigation-text: #8e96a2;
$navigation-text-hover: #fff;
$navigation-hover: #141414;
$navigation-text-active: #fff;
$navigation-active: #106779;
$secondary-submenu-navigation-text: #000;

// Validation
$validation-error-label-text: $colour-error;
$validation-error-background: #ffffff;
$validation-error-border: $colour-error;

// Brand header/footer
$brand-header-background: #fff;
$brand-footer-background: #fff;

// Fonts
@import url('//fonts.googleapis.com/css?family=Open+Sans:300,400,600,700');
$font-primary: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif !default;
$font-primary-colour: #333333;
$font-secondary-colour: #989898;

// Directories
$img: '../img/';

// Misc settings
$default-radius: 4px;
$transition-speed: 0.25s;
$box-shadow-focus: 0 0 8px rgba(0, 0, 0, 0.7);

// Modals
$modal-width: 500px;

// Pill buttons
$button-pill-height: 24px;
$button-pill-close: 20px;
$button-pill-close-spacing: 3px;

// When viewed on mobile/tablet, this variable determines how much padding is
// added to the left and right of the element.
$handheld-device-padding: 20px;

// Texts
$colour-primary-hover-text: $navigation-text-hover
