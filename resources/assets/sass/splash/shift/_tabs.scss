ul.tabs {
  @include user-select(none);

  list-style: none;
  padding: 0;
  margin: 0;
  height: 45px;
  border-bottom: 1px solid $colour-border;

  margin-top: -5px;

  li {
    float: left;
    margin: 5px 8px 0 0;

    &.active > a {
      background: $colour-tab-active;
      color: #fff !important;

      &:hover {
        background: $colour-tab-hover;
        color: $colour-tab-text;
      }

      &:focus {
        background: $colour-tab-active;
      }

    }

    &.disabled {
      @include opacity(1);
    }

    &.disabled > a,
    &.disabled > a:hover,
    &.disabled > a:active {
      background: #efefef !important;
      color: #666 !important;
      border: none;
      cursor: default;
    }

    &.invalid > a {
      color: $colour-error;
    }

    &:focus {
      outline: none;
    }
  }

  li > a {
    @include transition(color $transition-speed);

    background: $colour-tab;
    border-bottom: none;
    border-radius: $default-radius $default-radius 0 0;
    color: $colour-tab-text;
    display: block;
    padding: 10px 20px;
    text-decoration: none;

    &:hover {
      background: $colour-tab-hover;
      color: $colour-tab-text;

      &:focus {
        background: $colour-tab-hover;
      }
    }
  }

  @media (max-width: $screen-sm) {
    li {
      + li {
        margin-left: 0;
      }

      width: 100%;

      &:first-child a {
        border-radius: $default-radius $default-radius 0 0;
      }

      &:last-child a {
        border-radius: 0 0 $default-radius $default-radius;
      }
    }

    > a {
      border-radius: 0;
    }
  }
}

ul.tabs-borderless {
  border-bottom: none;

  li {
    min-height: 42px;

    > a {
      transform: translateY(2px);
      border-radius: 5px 5px 0 0;
    }

    &.active > a {
      padding-bottom: 12px;
    }

    &.invalid > a {
      color: $colour-tab-text;
      border-top: 4px solid $colour-error;
      padding-top: 6px;

      &:hover {
        border-top: 4px solid $colour-error;
      }

      &:focus {
        border-top: 4px solid $colour-error;
      }
    }
  }
}

ul.tabs-loading {
  li {
    &.disabled a {
      min-width: 85px;
      min-height: 40px;
    }
  }
}

// Specifically for disabled tabs - we want to make sure that the LI doesn't
// get a background, otherwise tabs will lose their border radius.
.tabs > li.disabled {
  background: transparent !important;
}

.tab-content {
  display: none;

  &.active {
    display: block;
  }
}

.tab-content-flat {
  margin-bottom: 60px;
}
