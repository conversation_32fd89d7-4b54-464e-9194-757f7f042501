// Variables
@import "variables";

// Core variables and mixins
//@import "bootstrap/variables";
//@import "bootstrap/mixins";

// Mixins - Utilities
@import "bootstrap/mixins/hide-text";
@import "bootstrap/mixins/opacity";
@import "bootstrap/mixins/image";
@import "bootstrap/mixins/labels";
@import "bootstrap/mixins/reset-filter";
@import "bootstrap/mixins/resize";
@import "bootstrap/mixins/responsive-visibility";
//@import "bootstrap/mixins/size";
@import "bootstrap/mixins/hyperlink-focus";
@import "bootstrap/mixins/text-emphasis";
@import "bootstrap/mixins/text-overflow";
@import "bootstrap/mixins/vendor-prefixes";

// Mixins - Components
@import "bootstrap/mixins/alerts";
@import "bootstrap/mixins/buttons";
@import "bootstrap/mixins/forms";

// Mixins - Skins
@import "bootstrap/mixins/background-variant";
@import "bootstrap/mixins/border-radius";
@import "bootstrap/mixins/gradients";

// Mixins - Layout
@import "bootstrap/mixins/clearfix";
@import "bootstrap/mixins/center-block";
@import "bootstrap/mixins/nav-vertical-align";
@import "bootstrap/mixins/grid-framework";
@import "bootstrap/mixins/grid";

// Core CSS
@import "bootstrap/scaffolding";
@import "bootstrap/type";
@import "bootstrap/grid";
@import "bootstrap/forms";
@import "bootstrap/buttons";

// Components
@import "bootstrap/alerts";
@import "bootstrap/wells";

// Components w/ JavaScript
@import "bootstrap/modals";
@import "bootstrap/popovers";

// Utility classes
@import "bootstrap/utilities";

// Overrides
@import "overrides";
