// Headings.
%headings-shared {
  color: $font-primary-colour;

  &.first { margin-top: 0; }

  &.collapse { margin: 0 }
}

h1, .h1 {
  @extend %headings-shared;
  font: 300 30px/40px $font-primary;
  letter-spacing: -0.025em;
  margin-top: 20px;
  margin-bottom: 10px;
}

h2, .h2 {
  @extend %headings-shared;
  font: 300 24px/34px $font-primary;
  letter-spacing: -0.04em;
  margin-top: 20px;
  margin-bottom: 10px;
}

h3, .h3 {
  @extend %headings-shared;
  font: 300 22px/1.4 $font-primary;
  letter-spacing: -0.01em;
  margin-top: 20px;
  margin-bottom: 10px;
}

h4, .h4 {
  @extend %headings-shared;
  font: 600 17px/1.4 $font-primary;
  letter-spacing: -0.01em;
  margin-top: 10px;
  margin-bottom: 10px;
}

h5, .h5 {
  @extend %headings-shared;
  font: 600 15px/1.4 $font-primary;
  margin-top: 10px;
  margin-bottom: 10px;
}

h6, .h6 {
  @extend %headings-shared;
  font: 700 12px/1.4 $font-primary;
  margin-top: 10px;
  margin-bottom: 10px;
}

h4, .h4 {
  @extend %headings-shared;
  font: 600 17px/1.4 $font-primary;
  letter-spacing: -0.01em;
  margin-top: 10px;
  margin-bottom: 10px;
}

h1 a, h2 a, h3 a, h4 a, h5 a, h6 a,
.h1 a, .h2 a, .h3 a, .h4 a, .h5 a, .h6 a {
  @include undecorated();
}

.account-details {
  h1, h2, h3 {
    color: $font-primary-colour;
  }
}

// Paragraphs.
p {}

// div > p:first-child {
// 	margin-top: 0;
// }

// Links.
.deleted a {
  &:hover {
    color: $colour-link-hover;
    text-decoration: underline;
  }

  &:active, &:focus {
    text-decoration: underline;
    background-color: transparent;
  }

  &:active {
    color: $colour-link-active;
  }
}

// Lists.
ul, ol {
  padding-left: 25px;
}

ul, ol {
  // All nested unordered lists to have the same style.
  ul, ul ul, ul ul ul { list-style: disc; }
}

li {}

tr.warning,
td.warning,
span.warning {
  color: $colour-warning
}

.strong {
  font-weight: bold;
}

.inactive {
  color: #cc0000 !important;
}

.deleted,
.deleted a {
  color: $colour-error;
  text-decoration: line-through;
}

.timezone-output {
  color: $colour-disabled-dark;
  font-size: 80%;
}

// Font size modifiers
$font-sizes: 100, 110, 120, 130, 140, 150, 200;

@each $size in $font-sizes {
  .fs-#{$size} {
    font-size: #{$size * 1%};
  }
}
