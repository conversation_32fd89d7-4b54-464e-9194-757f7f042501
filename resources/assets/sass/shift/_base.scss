/*
|--------------------------------------------------------------------------
| Base Elements
|--------------------------------------------------------------------------
|
| All the base elements should be placed here. Elements that are necessary
| for the app and that are used frequently throughout the app.
|
*/

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  background: $colour-home-background;
  color: $text-color;
  font: 400 14px/20px $font-primary;
  overflow-y: scroll;
}

// Message for unsupported browsers.
// Hidden on all normal browsers.
#unsupported-browser {
  background: $colour-error;
  border-radius: $default-radius;
  color: white;
  display: none;
  margin: 50px auto;
  padding: 30px;
  width: 600px;

  a {
    color: white;
  }
}

#no-javascript {
  @extend #unsupported-browser;
  display: block;
}

.title {
  display: inline-block;

  h1 {
    display: inline-block;
  }
}

#content {
  min-height: 450px;
}

small { font-size: 0.813em; }
