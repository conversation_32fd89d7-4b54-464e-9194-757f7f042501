div[class^='alert-'] {
  border-radius: $default-radius;
  border: 0;
  cursor: pointer;
  overflow: hidden;
  padding: 23px;
  display: table;
  width: 100%;

  // When alerts are followed by other alerts.
  & + & { margin-top: 10px; }

  &.sticky {
      cursor: default;
      z-index: 1;
  }

  &.inline {
    margin: 10px 0;
    padding: 5px;
    background: transparent;
  }

  &.system-message {
    padding: 12px 25px;
    border-radius: 0;
    cursor: auto;
    height: 52px;
  }

  .icon {
    display: table-cell;
    width: $af-icons-md-base-size;
    font-size: $af-icons-md-base-size;
  }

  .message {
    display: table-cell;
    vertical-align: middle;

    @include text-content;
  }

  .icon + .message {
    padding-left: 20px;
  }

  p:last-child, ul:last-child {
    margin-bottom: .5em;
  }

  .error-link {
    color: $colour-link;
  }

  .validation-errors .error-link {
    color: #fff;
  }
}

@media screen and (min-width: $screen-sm-min) {
  div[class^='alert-'] {
    .message .column {
      width: 50%;
      float: left;
      padding-right: 20px;
    }
  }
}

// A list of array types followed by their colour so that we can loop through them and
// perform magic.
$alert-types:
  info    $colour-info $colour-info-text,
  warning $colour-warning $colour-warning-text,
  success $colour-success $colour-success-text,
  error   $colour-error $colour-error-text,
  neutral $colour-neutral $colour-neutral-text;

@for $i from 1 through length($alert-types) {
  $type-list: nth($alert-types, $i);
  $type: nth($type-list, 1);
  $colour: nth($type-list, 2);
  $text-colour: nth($type-list, 3);

  .alert-#{$type} {
    @include new-alert($colour, $text-colour, $i - 1);
  }
}

div.alert-neutral {
  border: 1px solid #ddd;
}

.alert-error span span a {
  color: $validation-error-label-text
}
