
// overrides from theme color defaults
$colour-link: $theme-var-link;
$colour-link-hover: $theme-var-link-hover;
$colour-link-active: $theme-var-link-active;
$colour-success: $theme-var-alert-box-success;
$colour-warning: $theme-var-alert-box-warning;
$colour-info: $theme-var-alert-box-info;
$colour-error: $theme-var-alert-box-error;
$colour-form-box-background: $theme-var-form-box-background;
$colour-form-box-text: $theme-var-form-box-text;
$colour-focus-box-background: $theme-var-focus-box-background;
$colour-focus-box-text: $theme-var-focus-box-text;
$brand-header-background: $theme-var-brand-header-background;
$brand-footer-background: $theme-var-brand-footer-background;
$colour-home-background: $theme-var-home-background;
$colour-primary: $theme-var-primary-button;
$colour-primary-hover: $theme-var-primary-button-hover;
$colour-secondary: $theme-var-secondary-button;
$colour-secondary-hover: $theme-var-secondary-button-hover;
$colour-tertiary: $theme-var-tertiary-button;
$colour-tertiary-hover: $theme-var-tertiary-button-hover;
$colour-tab: $theme-var-tab;
$colour-tab-text: $theme-var-tab-text;
$colour-tab-active: $theme-var-tab-active;
$colour-tab-hover: $theme-var-tab-hover;
$navigation-background: $theme-var-menu-background;
$navigation-text: $theme-var-menu-text;
$navigation-hover: $theme-var-menu-hover-background;
$navigation-text-hover: $theme-var-menu-hover-text;
$navigation-active: $theme-var-menu-active-background;
$navigation-text-active: $theme-var-menu-active-text;
