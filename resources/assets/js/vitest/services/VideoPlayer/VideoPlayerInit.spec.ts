import init from '@/domain/services/VideoPlayer/VideoPlayerInit';
import { addVideoQualitySelector, instantiatePlayer } from '@/domain/services/VideoPlayer/VideoPlayerWrapper';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { onStart, onStop } from '@/domain/services/VideoPlayer/VideoPlayerTracker';

vi.mock('@/domain/services/VideoPlayer/VideoPlayerWrapper', () => ({
	addVideoQualitySelector: vi.fn(),
	instantiatePlayer: vi.fn((videoId, options, onReady) => {
		const player = {
			on: vi.fn((event, callback) => {
				callback(); // Simulate video.js ready event
			}),
			tech_: { vhs: { xhr: { onRequest: vi.fn() } } },
			error: vi.fn(),
		};

		onReady.call(player);

		return player;
	}),
}));

vi.mock('@/domain/services/VideoPlayer/VideoPlayerTracker');

describe('video-init', () => {
	afterEach(() => {
		vi.resetAllMocks();
	});

	it('should initialize video.js player and register event listeners', () => {
		init('video-id', {});

		expect(instantiatePlayer).toHaveBeenCalledWith('video-id', {}, expect.any(Function));
		expect(addVideoQualitySelector).toHaveBeenCalledWith(expect.any(Object));
		expect(onStart).toHaveBeenCalledTimes(1);
		expect(onStop).toHaveBeenCalledTimes(3);
	});
});
