import videojs from 'video.js';
import {
	addVideoQualitySelector,
	getPlayer,
	instantiatePlayer,
	Player,
} from '@/domain/services/VideoPlayer/VideoPlayerWrapper';
import { afterEach, describe, expect, it, vi } from 'vitest';

describe('instantiatePlayer', () => {
	afterEach(() => {
		vi.clearAllMocks();
		vi.resetAllMocks();
		vi.resetModules();
	});

	it('should call videojs with provided arguments', () => {
		vi.mock('video.js', () => ({
			default: vi.fn(),
		}));

		const videoID = 'videoID';
		const options = {};
		const callback = vi.fn();

		instantiatePlayer(videoID, options, callback);

		expect(videojs).toHaveBeenCalledWith(videoID, options, callback);
	});

	it('should wrap video.js getPlayer method', () => {
		// change videojs.getPlayer to a mock function
		videojs.getPlayer = vi.fn();

		const videoID = 'videoID';

		getPlayer(videoID);

		expect(videojs.getPlayer).toHaveBeenCalledWith('videoID');
	});

	it('should call qualityMenu method on player', () => {
		const player = { qualityMenu: vi.fn() } as unknown as Player;

		const result = addVideoQualitySelector(player);

		expect(player.qualityMenu).toHaveBeenCalledTimes(1);
		expect(result).toBe(player);
	});
});
