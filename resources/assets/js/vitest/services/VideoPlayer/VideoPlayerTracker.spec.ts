import { Player } from '@/domain/services/VideoPlayer/VideoPlayerWrapper';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { isSending, onStart, onStop } from '@/domain/services/VideoPlayer/VideoPlayerTracker';

vi.mock('@/lib/storage.js', () => ({
	default: vi.fn(() => ({
		get: vi.fn(),
		set: vi.fn(),
	})),
}));

vi.mock('lodash', () => ({
	throttle: vi.fn((func) => func),
}));

describe('videoTracker', () => {
	let mockPlayer: Player;

	beforeEach(() => {
		vi.useFakeTimers();
		mockPlayer = {
			setInterval: vi.fn((func) => setInterval(func, 1000)),
			clearInterval: vi.fn((id) => clearInterval(id)),
			readyState: vi.fn(() => 4),
			id: vi.fn(() => 'mockPlayerId'),
		} as unknown as Player;
	});

	afterEach(() => {
		vi.clearAllMocks();
		vi.clearAllTimers();
	});

	it('should start tracking viewing time', () => {
		onStart(mockPlayer);

		expect(mockPlayer.setInterval).toHaveBeenCalled();
	});

	it('should stop tracking viewing time', () => {
		onStart(mockPlayer);
		onStop(mockPlayer);

		expect(mockPlayer.clearInterval).toHaveBeenCalled();
	});

	it('should send the viewing time to the server if the video is watched for more than 30 seconds', () => {
		onStart(mockPlayer);
		expect(isSending).toBe(false);

		vi.advanceTimersByTime(3000);

		onStop(mockPlayer);

		expect(mockPlayer.clearInterval).toHaveBeenCalled();
		expect(isSending).toBe(true);
	});
});
