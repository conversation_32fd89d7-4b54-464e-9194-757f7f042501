import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { getTrans, useTranslations } from '@/services/global/translations.interface.js';

vi.mock('@/lib/utils', () => ({
	getGlobalData: (key: string) => {
		switch (key) {
			case 'translations':
				return {
					'en_GB.test': {
						a: {
							b: 'val-test-a-b',
							c: 'val-test-a-c',
						},
					},
				};
			case 'language':
				return {
					locale: 'en_GB',
					fallback: 'en_GB',
				};
		}
	},
}));

describe('Translations interface test suite', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should return a Lang instance for translations', () => {
		const lang = getTrans();

		const testAB = lang.get('test.a.b');
		const testAC = lang.get('test.a.c');

		expect(testAB).toBe('val-test-a-b');
		expect(testAC).toBe('val-test-a-c');
	});

	it('should detect active step', () => {
		const [testAB, testAC, testA, testAX] = useTranslations(['test.a.b', 'test.a.c', 'test.a', 'test.a.x']);

		expect(testAB()).toBe('val-test-a-b');
		expect(testAC()).toBe('val-test-a-c');
		expect(testA()).toBe('test.a');
		expect(testAX()).toBe('test.a.x');
	});
});
