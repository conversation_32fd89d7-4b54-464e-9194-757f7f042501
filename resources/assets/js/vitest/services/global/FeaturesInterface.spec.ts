import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { featureEnabled, useFeatures } from '@/services/global/features.interface';

vi.mock('@/lib/utils', () => ({
	getGlobalData: () => [
		{
			feature: 'foo',
			enabled: true,
		},
		{
			feature: 'bar',
			enabled: false,
		},
	],
}));

describe('Features interface test suite', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should return proper features', () => {
		const [f1, f2, f5] = useFeatures(['foo', 'bar', 'blah-blah']);

		expect(f1).toBe(true);
		expect(f2).toBe(false);
		expect(f5).toBe(false);

		expect(featureEnabled('foo')).toBe(true);
		expect(featureEnabled('bar')).toBe(false);
		expect(featureEnabled('blah')).toBe(false);
	});
});
