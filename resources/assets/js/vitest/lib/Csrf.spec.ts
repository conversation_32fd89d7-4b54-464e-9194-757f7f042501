import { vueData } from '@/domain/services/VueData';
import $ from 'jquery';
import { refreshCsrfToken } from '@/lib/csrf';
import Vue from 'vue';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/domain/services/VueData');

vi.mock('jquery', () => ({
	default: {
		ajaxSetup: vi.fn(),
	},
}));

vi.mock('vue', () => ({
	default: {
		prototype: {
			$http: {
				defaults: {
					headers: {
						common: {
							foo: 'bar',
						},
					},
				},
			},
		},
	},
}));

describe('Api service', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('injects token into ajax', () => {
		vi.spyOn(vueData, 'CSRF_TOKEN', 'get').mockReturnValue('test-token');

		refreshCsrfToken();

		expect($.ajaxSetup).toHaveBeenCalledWith({
			headers: { 'X-CSRF-TOKEN': 'test-token' },
		});

		expect(Vue.prototype.$http.defaults.headers.common).toStrictEqual({
			'X-CSRF-TOKEN': 'test-token',
			foo: 'bar',
		});
	});

	it('breaks if theres no csrf in VueData', () => {
		expect(() => refreshCsrfToken()).toThrowError('CSRF token not injected.');
	});
});
