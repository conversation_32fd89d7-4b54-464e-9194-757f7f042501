import { emptyCtx } from '@/../vitest/_common/helpers';
import { SetupContext } from 'vue';
import { beforeEach, describe, expect, it, test, vi } from 'vitest';
import { Collaborator, CollaboratorPrivilege } from '@/domain/models/Collaborator';
import { DataResponse, dataSource } from '@/domain/services/Api/DataSource';
import { privilegeSelectorController, View } from '@/lib/components/Collaboration/PrivilegeSelector.controller';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		routes: {
			'collaborators.privilege.update': 'collaborators/{collaborator}',
		},
		language: {
			locale: 'en_GB',
			fallback: 'en_GB',
		},
	},
}));

describe('collaboratorActions controller', () => {
	beforeEach(() => {
		vi.resetAllMocks();
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: '' } as DataResponse<unknown>);
	});

	const props = {
		collaborator: {
			slug: 'collaborator-slug',
		} as unknown as Collaborator,
		canManage: true,
	};

	test('loading should default to false', () => {
		const view = privilegeSelectorController(props, emptyCtx) as View;

		expect(view.loading.value).toBe(false);
	});

	test('privileges default values', () => {
		const view = privilegeSelectorController(props, emptyCtx) as View;

		expect(view.privileges[0].id).toBe('viewer');
		expect(view.privileges[1].id).toBe('editor');
		expect(view.privileges[2].id).toBe('submitter');

		expect(view.privileges[0].name).toBe('collaboration.privileges.viewer');
		expect(view.privileges[1].name).toBe('collaboration.privileges.editor');
		expect(view.privileges[2].name).toBe('collaboration.privileges.submitter');
	});

	it('should emit "updated" event when the changePrivilege was called', async () => {
		const emit = vi.fn();
		const view = privilegeSelectorController(props, { emit } as unknown as SetupContext) as View;

		await view.changePrivilege(CollaboratorPrivilege.VIEWER);

		expect(emit).toHaveBeenCalledWith('updated', undefined);
	});
});
