import { useApi } from '@/lib/components/Collaboration/PrivilegeSelector.api';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Collaborator, CollaboratorPrivilege } from '@/domain/models/Collaborator';
import { DataRequestMethod, DataResponse, dataSource } from '@/domain/services/Api/DataSource';

vi.mock('@/domain/services/Api/Headers', async () => ({
	headersFactory: () => ({}),
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		routes: {
			'collaborators.privilege.update': 'collaborators/{collaborator}',
		},
	},
}));

const collaborator: Collaborator = { slug: 'collaborator-slug' } as Collaborator;

const api = useApi(collaborator);

describe('PrivilegeSelector api', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('should call privilege update endpoint when updatePrivilege was called', async () => {
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: 'update-privilege' } as DataResponse<unknown>);

		const response = await api.updatePrivilege(CollaboratorPrivilege.EDITOR);

		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/collaborators/collaborator-slug',
			method: DataRequestMethod.PUT,
			data: {
				privilege: 'editor',
			},

			headers: {},
		});
		expect(response).toBe('update-privilege');
	});
});
