import * as featureExports from '@/domain/dao/Features';
import { Collaborator } from '@/domain/models/Collaborator';
import { collaboratorsService } from '@/domain/services/Collaboration/Collaborators';
import { Form } from '@/domain/models/Form';
import { useContainer } from '@/domain/services/Container';
import { beforeEach, describe, expect, it, Mock, test, vi } from 'vitest';
import { collaborationUIBus, CollaborationUISignals } from '@/domain/signals/Collaboration';
import {
	collaboratorsInviteModalController,
	View,
} from '@/lib/components/Collaboration/CollaboratorsInviteModal.controller';
import { DataError, DataResponse, dataSource } from '@/domain/services/Api/DataSource';
import dataSources, { DataSource } from '@/domain/services/Rt/DataSource';

vi.mock('@/domain/services/Rt/DataSource', () => ({
	default: {
		collaborators: vi.fn(),
	},
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		language: {
			locale: 'en_GB',
			fallback: 'en_GB',
		},
		routes: {
			'collaborators.invite': 'collaborators/{form}/{submittableSlug}/invite',
		},
	},
}));
vi.mock('@/domain/services/Container', () => ({
	useContainer: vi.fn(),
}));

vi.mock('@/lib/components/Collaboration/CollaboratorsInviteModal.api', () => ({
	useApi: vi.fn(),
}));

const form: Form = {
	slug: 'form-slug',
} as unknown as Form;

// eslint-disable-next-line @typescript-eslint/naming-convention
const Submittable = {
	getSubmittable: vi.fn(),
	getForm: vi.fn().mockReturnValue(form),
};

// eslint-disable-next-line @typescript-eslint/naming-convention
const Collaborators = {
	update: vi.fn(),
};
vi.spyOn(featureExports, 'featureEnabled').mockReturnValue(false);
describe('collaboratorActions controller', () => {
	const onWatch = vi.fn();

	beforeEach(() => {
		vi.resetAllMocks();
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: '' } as DataResponse<unknown>);
		vi.spyOn(dataSources, 'collaborators').mockReturnValue({
			get: () => Promise.resolve([]),
			subscribe: vi.fn(),
			set: vi.fn(),
		} as unknown as DataSource<Collaborator[]>);
		(useContainer as Mock).mockReturnValue({ provide: vi.fn(), Submittable, Collaborators, onWatch });
	});

	test('loading should be false by default', () => {
		const view = collaboratorsInviteModalController() as View;

		expect(view.loading.value).toBe(false);
	});

	test('ready should be false by default', () => {
		const view = collaboratorsInviteModalController() as View;

		expect(view.ready.value).toBe(false);
	});

	test('modal is close by default', () => {
		const view = collaboratorsInviteModalController() as View;

		expect(view.isModalOpen.value).toBe(false);
	});

	it('should call invite() when onInvite was called', async () => {
		const collaborators = [{ owner: true, manager: false }] as Collaborator[];
		const view = collaboratorsInviteModalController() as View;
		const service = collaboratorsService()('form-slug', 'submittable-slug');
		vi.spyOn(service, 'destroy');
		const spyInvite = vi.spyOn(service, 'invite').mockReturnValue(new Promise((resolve) => resolve(collaborators)));

		collaborationUIBus.emit(CollaborationUISignals.OPEN_INVITER, service);

		await view.onInvite();

		expect(spyInvite).toHaveBeenCalled();
	});

	it('should call update() when onInvite was called', async () => {
		const collaborators: Collaborator[] = [{ owner: true, manager: false }] as Collaborator[];
		const view = collaboratorsInviteModalController() as View;
		const service = collaboratorsService()('form-slug', 'submittable-slug');
		vi.spyOn(service, 'destroy');
		vi.spyOn(service, 'invite').mockReturnValue(new Promise((resolve) => resolve(collaborators)));
		const spyUpdate = vi.spyOn(service, 'update');

		collaborationUIBus.emit(CollaborationUISignals.OPEN_INVITER, service);

		await view.onInvite();

		expect(spyUpdate).toHaveBeenCalled();
	});

	it('should not call update() and invite() when collaboratorsService is null', async () => {
		const collaborators: Collaborator[] = [{ owner: true, manager: false }] as Collaborator[];
		const view = collaboratorsInviteModalController() as View;
		const service = collaboratorsService()('form-slug', 'submittable-slug');
		vi.spyOn(service, 'destroy');
		const spyInvite = vi.spyOn(service, 'invite').mockReturnValue(new Promise((resolve) => resolve(collaborators)));
		const spyUpdate = vi.spyOn(service, 'update');

		await view.onInvite();

		expect(spyInvite).toHaveBeenCalledTimes(0);
		expect(spyUpdate).toHaveBeenCalledTimes(0);
	});

	test('modal is usable after validation error', async () => {
		const service = collaboratorsService(true)('form-slug', 'submittable-slug');

		vi.spyOn(dataSource, 'request').mockRejectedValue({
			response: { status: 422 },
		} as DataError<unknown>);

		const view = collaboratorsInviteModalController() as View;
		collaborationUIBus.emit(CollaborationUISignals.OPEN_INVITER, service);

		await view.onInvite();
		expect(view.loading.value).toBe(false);
	});

	it('should reset form values when onInvite was called', async () => {
		let watchCallback: () => void = () => {};

		(onWatch as Mock).mockImplementation((source, cb) => (watchCallback = cb));

		const view = collaboratorsInviteModalController() as View;

		view.form.value = {
			emails: ['<EMAIL>'],
			privilege: 'manager',
			message: 'this is a fake message',
		};

		watchCallback(true);

		expect(view.form.value).toMatchObject({
			emails: [],
			privilege: 'viewer',
			message: '',
		});
		expect(onWatch).toHaveBeenCalled();
	});

	it('should increment the key when onInvite was called', async () => {
		let watchCallback: () => void = () => {};

		(onWatch as Mock).mockImplementation((source, cb) => (watchCallback = cb));

		const view = collaboratorsInviteModalController() as View;
		collaboratorsService()('form-slug', 'submittable-slug');

		watchCallback(true);

		expect(view.componentKey.value).to.equal(1);
	});
});
