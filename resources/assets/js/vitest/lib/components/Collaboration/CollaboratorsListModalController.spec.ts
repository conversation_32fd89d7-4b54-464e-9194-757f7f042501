import * as featureExports from '@/domain/dao/Features';
import { Collaborator } from '@/domain/models/Collaborator';
import { emptyCtx } from '@/../vitest/_common/helpers';
import { Mock } from 'vitest/index';
import { SubmittableTokens } from '@/domain/services/Rt/SubmittableTokens';
import { useApi } from '@/domain/services/Collaboration/Api';
import { useManageCollaboratorsContainer } from '@/lib/components/ListActions/ManageCollaboratorsProvider';
import { beforeEach, describe, expect, vi } from 'vitest';
import { collaborationUIBus, CollaborationUISignals } from '@/domain/signals/Collaboration';
import {
	collaboratorsListModalController,
	View,
} from '@/lib/components/Collaboration/CollaboratorsListModal.controller';
import { DataResponse, dataSource } from '@/domain/services/Api/DataSource';
import dataSources, { DataSource } from '@/domain/services/Rt/DataSource';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		routes: {
			'collaborators.owner': '',
			'collaborators.delete': '',
		},
		language: {
			locale: 'en_GB',
			fallback: 'en_GB',
		},
	},
}));

vi.mock('@/domain/services/Container', () => ({
	useContainer: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Collaborators', () => ({
	collaboratorsService: vi.fn(),
}));

vi.mock('@/lib/components/ListActions/ManageCollaboratorsProvider', () => ({
	useManageCollaboratorsContainer: vi.fn(),
}));

vi.mock('@/domain/services/Rt/Firebase', () => ({
	bootFirebase: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Api', () => ({
	useApi: vi.fn(),
}));

vi.mock('@/domain/services/Rt/Firebase', () => ({
	bootFirebase: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Api', () => ({
	useApi: vi.fn(),
}));

const collaboratorsInstance = {
	getMyself: vi.fn<never, Collaborator>(),
	onUpdate: vi.fn(),
	getAll: vi.fn(),
};

const collaborators = [{ user: 'myself' }, { user: 'foo' }, { user: 'bar' }] as Collaborator[];
collaboratorsInstance.getAll.mockReturnValue(collaborators);

vi.spyOn(featureExports, 'featureEnabled').mockReturnValue(true);

describe('collaboratorsListModal controller', () => {
	const onBeforeUnmount = vi.fn();

	beforeEach(() => {
		vi.resetAllMocks();

		vi.spyOn(dataSources, 'collaborators').mockReturnValue({
			get: () => Promise.resolve([]),
			subscribe: vi.fn(),
			set: vi.fn(),
		} as unknown as DataSource<Collaborator[]>);

		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: '' } as DataResponse<unknown>);

		(useManageCollaboratorsContainer as Mock).mockReturnValue({
			Collaborators: () => collaboratorsInstance,
			onBeforeUnmount,
		});

		const getCollaborators = vi.fn();
		const getSubmittableTokens = vi.fn();

		(useApi as Mock).mockReturnValue({
			getCollaborators,
			getSubmittableTokens,
		});

		getCollaborators.mockReturnValue({
			then: (cb: (collaborators: Collaborator[]) => void) => cb(collaborators),
		});

		getSubmittableTokens.mockReturnValue({
			then: (cb: (submittableTokens: SubmittableTokens) => void) =>
				// eslint-disable-next-line standard/no-callback-literal
				cb({ authToken: 'authToken', submittableToken: 'submittableToken' }),
		});
	});

	test('modal should be close by default', () => {
		const view = collaboratorsListModalController('', emptyCtx) as View;

		expect(view.isModalOpen.value).toBe(false);
	});

	it('should call onUpdate when open-collaborators-list signal emitted', async () => {
		collaboratorsListModalController('', emptyCtx) as View;

		collaborationUIBus.emit(CollaborationUISignals.OPEN_COLLABORATORS_LIST, {
			authToken: 'authToken',
			submittableToken: 'submittableToken',
		});
		await (async () => new Promise((resolve) => setTimeout(resolve, 10)))();

		expect(collaboratorsInstance.onUpdate).toHaveBeenCalled();
	});

	it('should call getAll when open-collaborators-list signal emitted', async () => {
		collaboratorsListModalController('', emptyCtx) as View;

		collaborationUIBus.emit(CollaborationUISignals.OPEN_COLLABORATORS_LIST, {
			authToken: 'authToken',
			submittableToken: 'submittableToken',
		});
		await (async () => new Promise((resolve) => setTimeout(resolve, 10)))();

		expect(collaboratorsInstance.getAll).toHaveBeenCalled();
	});

	it('should call getMyself when open-collaborators-list signal emitted', async () => {
		collaboratorsListModalController('', emptyCtx) as View;

		collaborationUIBus.emit(CollaborationUISignals.OPEN_COLLABORATORS_LIST, {
			authToken: 'authToken',
			submittableToken: 'submittableToken',
		});
		await (async () => new Promise((resolve) => setTimeout(resolve, 10)))();

		expect(collaboratorsInstance.getMyself).toHaveBeenCalled();
	});

	test('confirmation data should be false or null by default', () => {
		const view = collaboratorsListModalController('', emptyCtx) as View;

		expect(view.confirmation.value.isOpen).toBe(false);
		expect(view.confirmation.value.action).toBe(null);
		expect(view.confirmation.value.cancel).toBe(null);
		expect(view.confirmation.value.prompt).toBe('');
	});

	test('the collaborator manager should can manage', async () => {
		const view = collaboratorsListModalController('', emptyCtx) as View;
		const collaborator: Collaborator = { user: 'user-slug', owner: false, manager: true } as Collaborator;
		vi
			.spyOn(collaboratorsInstance, 'getMyself')
			.mockReturnValue({ user: 'myself-slug', owner: false, manager: true } as Collaborator);

		collaborationUIBus.emit(CollaborationUISignals.OPEN_COLLABORATORS_LIST, {
			authToken: 'authToken',
			submittableToken: 'submittableToken',
		});
		await (async () => new Promise((resolve) => setTimeout(resolve, 10)))();

		expect(view.canManage(collaborator)).toBe(true);
	});

	it('should emit collaboration open inviter event when onInviteMore was called', async () => {
		const view = collaboratorsListModalController('', emptyCtx) as View;
		const collaborationUIBusEmit = vi.spyOn(collaborationUIBus, 'emit');

		collaborationUIBus.emit(CollaborationUISignals.OPEN_COLLABORATORS_LIST, {
			authToken: 'authToken',
			submittableToken: 'submittableToken',
		});
		await (async () => new Promise((resolve) => setTimeout(resolve, 10)))();

		view.onInviteMore();

		expect(collaborationUIBusEmit).toHaveBeenCalledWith(CollaborationUISignals.OPEN_INVITER, collaboratorsInstance);
	});

	test('collaboration owner should cant manage', () => {
		const view = collaboratorsListModalController('', emptyCtx) as View;
		const collaborator: Collaborator = { owner: true, manager: false } as Collaborator;

		expect(view.canManage(collaborator)).toBe(false);
	});

	test('the collaborator should cant manage itself', () => {
		const view = collaboratorsListModalController('', emptyCtx) as View;
		const collaborator: Collaborator = { user: 'myself-user-slug', owner: false, manager: false } as Collaborator;

		expect(view.canManage(collaborator)).toBe(false);
	});

	test('confirmation default values', () => {
		const view = collaboratorsListModalController('', emptyCtx) as View;

		expect(view.confirmation.value.isOpen).toBe(false);
		expect(view.confirmation.value.prompt).toBe('');
		expect(view.confirmation.value.action).toBe(null);
		expect(view.confirmation.value.cancel).toBe(null);
	});

	test('confirmation values after calling onWantsUpdate ', () => {
		const view = collaboratorsListModalController('', emptyCtx) as View;

		view.onWantsUpdate({
			prompt: 'prompt-test',
			action: async () => {},
			cancel: () => {},
		} as WantsUpdatePayload);

		expect(view.confirmation.value.isOpen).toBe(true);
		expect(view.confirmation.value.prompt).toBe('prompt-test');
		expect(view.confirmation.value.action).toBeInstanceOf(Function);
		expect(view.confirmation.value.cancel).toBeInstanceOf(Function);
	});

	test('unsubscribe from opening event on unload', () => {
		const collaborationUIBusOff = vi.spyOn(collaborationUIBus, 'off');
		let unsubscribe = () => {};

		onBeforeUnmount.mockImplementation((cb) => (unsubscribe = cb));

		collaboratorsListModalController('', emptyCtx) as View;
		unsubscribe();
		expect(collaborationUIBusOff).toHaveBeenCalledWith(CollaborationUISignals.OPEN_COLLABORATORS_LIST, expect.anything());
	});
});
