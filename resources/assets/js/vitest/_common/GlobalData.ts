// eslint-disable-next-line @typescript-eslint/naming-convention
const GlobalData = function () {
	const proxy = new Proxy(global as Record<string, unknown>, {
		set(target, property, value) {
			target[property as string] = value;
			return true;
		},
	});

	proxy.originalData = global;

	proxy.reset = function () {
		// eslint-disable-next-line no-global-assign
		(global as unknown) = this.originalData;
	};

	return proxy as typeof proxy & { reset: () => void };
};

export default GlobalData;
