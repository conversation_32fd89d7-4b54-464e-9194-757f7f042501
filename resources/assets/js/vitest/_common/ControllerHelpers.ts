import { expect } from 'vitest';
import { isRef } from 'vue';

const checkComputed = (view: { [k: string]: unknown }) => (prop: string, value?: unknown) => {
	expect(view).toHaveProperty(prop);
	expect(isRef(view[prop])).toBeTruthy();

	if (value !== undefined) {
		expect((view[prop] as { value: unknown }).value).toStrictEqual(value);
	}
};

const checkMethod = (view: { [k: string]: unknown }) => (prop: string) => {
	expect(view).toHaveProperty(prop);
	expect(typeof view[prop]).toStrictEqual('function');
};

const checkLang =
	(view: { [k: string]: unknown }) =>
	(prop = 'lang') => {
		expect(view).toHaveProperty(prop);
		expect(typeof view[prop]).toStrictEqual('object');
		expect(view[prop]).toHaveProperty('get');

		const lang = view[prop] as { get: (key: string) => string };
		expect(typeof lang.get).toStrictEqual('function');
		expect(lang.get('test')).toStrictEqual('translation: test');
	};

const flushPromises = () => new Promise((resolve) => setTimeout(resolve));

export { checkComputed, checkMethod, checkLang, flushPromises };
