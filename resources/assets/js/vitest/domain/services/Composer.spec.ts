import { Api } from '@/legacy/OldApi';
import GlobalData from '../../_common/GlobalData';
import { routes } from '@/routes';
import { VueDataProvider } from '@/domain/services/VueDataProvider';
import { VueModule } from '@/domain/services/VueModule';
import Vuex from 'vuex';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { Component, SetupContext, VueConstructor } from 'vue';
import { Composer, useController } from '@/domain/services/Composer';

type Routes = {
	[key: string]: () => Promise<unknown>;
};

vi.mock('@/routes', () => ({
	routes: {
		testA: vi.fn(),
		testB: vi.fn(),
	},
}));

vi.mock('@/legacy/OldApi', () => ({
	Api: {
		setup: vi.fn(),
	},
}));

vi.mock('@/global-components', () => ({
	default: {
		GlobalComponent1: 'GlobalComponent1',
		GlobalComponent2: 'GlobalComponent2',
		GlobalComponent3: 'GlobalComponent3',
	},
}));

vi.mock('@/domain/services/VueModule', () => ({
	VueModule: {
		create: vi.fn(),
		destroy: vi.fn(),
	},
}));

const vueContext = {
	component: vi.fn(),
	use: vi.fn(),
	directive: vi.fn(),
};

vi.mock('@/domain/services/VueDataProvider', () => ({
	VueDataProvider: {
		bootGlobalData: vi.fn(),
	},
}));

describe('Composer', () => {
	const globalData = GlobalData();

	beforeEach(() => {
		globalData.window = { App: { foo: 'bar' } };
		globalData.Route = 'test';
		vi.resetAllMocks();

		vi.spyOn(routes, 'testA').mockResolvedValue({
			default: vi.fn(),
		});

		vi.spyOn(routes, 'testB').mockResolvedValue({
			default: vi.fn(),
		});

		Composer.boot(vueContext as unknown as VueConstructor);
	});

	afterEach(() => {
		vi.clearAllMocks();
		globalData.reset();
	});

	it('boots global data repository', () => {
		Composer.loadModule();
		expect(VueDataProvider.bootGlobalData).toHaveBeenCalledWith({ foo: 'bar' });
	});

	it('boots vuex', () => {
		expect(vueContext.use).toHaveBeenCalledWith(Vuex);
	});

	it('boot global directives', () => {
		expect(vueContext.directive).toHaveBeenCalledWith('output', {
			bind: expect.any(Function),
			update: expect.any(Function),
		});
	});

	it('boots api', () => {
		Composer.loadModule();
		expect(Api.setup).toHaveBeenCalledOnce();
	});

	it("doesn't load module if route is not defined", () => {
		Composer.loadModule();
		expect((routes as Routes).testA).not.toHaveBeenCalled();
		expect((routes as Routes).testB).not.toHaveBeenCalled();
		expect(VueModule.create).not.toHaveBeenCalled();
	});

	it('unloads module', async () => {
		globalData.Route = 'testA';
		vi.spyOn(document, 'querySelector').mockReturnValue(document.createElement('div'));
		vi.spyOn(VueModule, 'create').mockReturnValue('myVueInstance' as unknown as Vue);

		await Composer.loadModule();
		expect(VueModule.create).toHaveBeenCalled();

		globalData.Route = 'testB';
		await Composer.loadModule();
		expect(VueModule.destroy).toHaveBeenCalledWith('myVueInstance');
		expect(VueModule.create).toHaveBeenCalledTimes(2);
	});

	it('loads module for defined route with registered components and plugins', async () => {
		globalData.Route = 'testA';

		vi.spyOn(document, 'querySelector').mockReturnValue(document.createElement('div'));

		const plugins = {
			plugin1: 'plugin1',
			plugin2: 'plugin2',
		};
		Composer.registerVuePlugins(plugins);

		const components = {
			childComponent1: {} as Component,
			childComponent2: {} as Component,
			childComponent3: {} as Component,
		};
		Composer.registerComponents(components);

		await Composer.loadModule();
		expect((routes as Routes).testA).toHaveBeenCalledOnce();
		expect((routes as Routes).testB).not.toHaveBeenCalled();
		expect(VueModule.create).toHaveBeenCalledWith({
			...plugins,
			el: '#vue-root',
			name: 'Test A',
			components: { testA: { name: 'testA', components } },
		});
	});

	it('should not load module if top level route mounting point is missing from DOM', async () => {
		document.body.innerHTML = '<div id="vue-root"></div>';
		globalData.Route = 'testA';

		const mockModule = {
			default: vi.fn(),
			name: 'testA',
		};

		const routeSpy = vi.spyOn(routes, 'testA').mockResolvedValue(mockModule);
		const querySelectorSpy = vi.spyOn(document, 'querySelector').mockReturnValue(null);
		const createSpy = vi.spyOn(VueModule, 'create');

		await Composer.loadModule();

		expect(routeSpy).toHaveBeenCalled();
		expect(querySelectorSpy).toHaveBeenCalledWith('#vue-root testA');
		expect(createSpy).not.toHaveBeenCalled();
	});

	it('makes setup function out of controller', () => {
		const realView = {
			foo: 'bar-from-controller',
			lorem: 'ipsum-from-controller',
			dolor: 'sit-from-controller',
		};

		const controller = vi.fn(() => realView);

		const setup = useController(controller, 'TestView');

		const view = setup({ prop: 'val' }, { foo: 'bar' } as unknown as SetupContext);
		expect(controller).toHaveBeenCalledWith({ prop: 'val' }, { foo: 'bar' });
		expect(view).toStrictEqual(realView);
	});

	it('mocks controller for snapshot tests', () => {
		const realView = {
			foo: 'bar-from-controller',
			lorem: 'ipsum-from-controller',
			dolor: 'sit-from-controller',
		};

		const controller = vi.fn(() => realView);
		const setup = useController(controller, 'TestView');

		const mockedView = {
			foo: 'bar-from-mock',
			lorem: 'ipsum-from-mock',
			dolor: 'sit-from-mock',
		};
		Composer.mockView('TestView', mockedView);

		const view = setup({ prop: 'val' }, { foo: 'bar' } as unknown as SetupContext);
		expect(controller).not.toHaveBeenCalled();
		expect(view).toStrictEqual(mockedView);
		expect(view).not.toStrictEqual(realView);
	});
});
