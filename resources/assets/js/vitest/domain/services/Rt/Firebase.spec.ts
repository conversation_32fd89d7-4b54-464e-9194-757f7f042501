import * as auth from 'firebase/auth';
import * as configDao from '@/domain/services/Rt/Config';
import * as fb from 'firebase/app';
import * as firestore from 'firebase/firestore';
import { bootFirebase } from '@/domain/services/Rt/Firebase';
import { FirebaseApp } from 'firebase/app';
import { RtConfig } from '@/domain/services/Rt/Config';
import { SubmittableTokens } from '@/domain/services/Rt/SubmittableTokens';
import { vueData } from '@/domain/services/VueData';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { collaborationUIBus, CollaborationUISignals } from '@/domain/signals/Collaboration';

vi.mock('firebase/app', () => ({
	initializeApp: vi.fn(),
	deleteApp: vi.fn(),
	getApps: vi.fn(),
}));

vi.mock('firebase/auth', () => ({
	getAuth: vi.fn(),
	signInWithCustomToken: vi.fn(),
	connectAuthEmulator: vi.fn(),
}));

vi.mock('firebase/firestore', () => ({
	getFirestore: vi.fn(),
	connectFirestoreEmulator: vi.fn(),
}));

vi.mock('@/domain/services/Rt/SubmittableTokens', () => ({
	useSubmittableTokens: {
		data: {
			submittableToken: 'submittableToken',
			authToken: 'authToken',
		},
	},
}));

const submittableTokens: SubmittableTokens = {
	authToken: 'authToken',
	submittableToken: 'submittableToken',
};

describe('Firebase provider', () => {
	const initializeApp = vi.spyOn(fb, 'initializeApp');
	const deleteApp = vi.spyOn(fb, 'deleteApp');
	const getApps = vi.spyOn(fb, 'getApps');

	beforeEach(() => {
		vi.resetAllMocks();
		vi.spyOn(vueData, 'variables', 'get').mockReturnValue({ submittableToken: 'test-super-long-custom-token' });
		getApps.mockReturnValue([]);
	});

	it('throws error if there is no config record passed', async () => {
		vi.spyOn(configDao, 'useRtConfig').mockReturnValue({} as unknown as RtConfig);

		await expect(() => bootFirebase()).rejects.toThrow('Firebase boot failed!');
		expect(initializeApp).not.toHaveBeenCalled();
	});

	it('delete old apps if there were any', async () => {
		vi.spyOn(configDao, 'useRtConfig').mockReturnValue({
			services: { firebase: 'foo-bar' },
		} as unknown as RtConfig);

		getApps.mockReturnValue(['test-app' as FirebaseApp, 'test-app-2' as FirebaseApp]);

		initializeApp.mockResolvedValue('forebase-app-for-foo-bar' as unknown as FirebaseApp);

		const fb1 = await bootFirebase();
		expect(deleteApp).toHaveBeenCalledWith('test-app');
		expect(deleteApp).toHaveBeenCalledWith('test-app-2');
		expect(initializeApp).toHaveBeenCalledWith('foo-bar');
		expect(initializeApp).toHaveBeenCalledWith('foo-bar');
		expect(fb1.firebase).toBe('forebase-app-for-foo-bar');
	});

	it('initialize provider', async () => {
		const emitSignal = vi.spyOn(collaborationUIBus, 'emit');

		vi.spyOn(configDao, 'useRtConfig').mockReturnValue({
			services: { firebase: 'foo-bar' },
		} as unknown as RtConfig);

		initializeApp.mockResolvedValue('forebase-app-for-foo-bar' as unknown as FirebaseApp);

		const fb1 = await bootFirebase();
		expect(initializeApp).toHaveBeenCalledWith('foo-bar');
		expect(emitSignal).toHaveBeenCalledWith(CollaborationUISignals.RELOAD, undefined);
		expect(fb1.firebase).toBe('forebase-app-for-foo-bar');
	});

	it('initializes for emulators', async () => {
		vi.spyOn(configDao, 'useRtConfig').mockReturnValue({
			firebase: {
				emulators: {
					useEmulator: true,
					firestore: { host: '127.0.0.1', port: 8080 },
					auth: { host: 'http://127.0.0.1:9099' },
				},
			},
			services: { firebase: 'foo-bar' },
		} as unknown as RtConfig);
		const connectAuthEmulatorSpy = vi.spyOn(auth, 'connectAuthEmulator');
		const connectFirestoreEmulatorSpy = vi.spyOn(firestore, 'connectFirestoreEmulator');

		initializeApp.mockResolvedValue({ name: '[DEFAULT]' } as FirebaseApp);

		const { emulated, getFirestore } = await bootFirebase(submittableTokens);
		getFirestore();

		expect(emulated).toBeTruthy();
		expect(connectAuthEmulatorSpy).toHaveBeenCalled();
		expect(connectFirestoreEmulatorSpy).toHaveBeenCalled();
	});

	it('initializes for remote Firebase', async () => {
		vi.spyOn(configDao, 'useRtConfig').mockReturnValue({
			firebase: { emulators: { useEmulator: false } },
			services: { firebase: 'foo-bar' },
		} as unknown as RtConfig);

		const { emulated } = await bootFirebase();
		expect(emulated).to.be.false;
	});

	it('throws error when firebase config is missing', async () => {
		vi.spyOn(configDao, 'useRtConfig').mockReturnValue({
			services: {},
		} as unknown as RtConfig);

		await expect(bootFirebase(submittableTokens)).rejects.toThrow('Firebase boot failed!');
	});

	it('authenticates with custom token if authToken is provided', async () => {
		vi.spyOn(configDao, 'useRtConfig').mockReturnValue({
			services: { firebase: 'firebase-config' },
		} as unknown as RtConfig);

		const mockFirebaseApp = { name: 'test-app' } as FirebaseApp;
		initializeApp.mockResolvedValue(mockFirebaseApp);

		const mockAuth = { currentUser: null };
		const getAuthSpy = vi.spyOn(auth, 'getAuth').mockReturnValue(mockAuth as any);
		const signInWithCustomTokenSpy = vi.spyOn(auth, 'signInWithCustomToken').mockResolvedValue('user-credential');

		await bootFirebase(submittableTokens);

		expect(getAuthSpy).toHaveBeenCalledWith(mockFirebaseApp);
		expect(signInWithCustomTokenSpy).toHaveBeenCalledWith(mockAuth, 'authToken');
	});

	it('does not authenticate if no authToken is provided', async () => {
		vi.spyOn(configDao, 'useRtConfig').mockReturnValue({
			services: { firebase: 'firebase-config' },
		} as unknown as RtConfig);

		const mockFirebaseApp = { name: 'test-app' } as FirebaseApp;
		initializeApp.mockResolvedValue(mockFirebaseApp);

		const getAuthSpy = vi.spyOn(auth, 'getAuth');
		const signInWithCustomTokenSpy = vi.spyOn(auth, 'signInWithCustomToken');

		await bootFirebase();

		expect(getAuthSpy).not.toHaveBeenCalled();
		expect(signInWithCustomTokenSpy).not.toHaveBeenCalled();
	});

	it('can encrypt data', async () => {
		vi.spyOn(configDao, 'useRtConfig').mockReturnValue({
			firebase: { encrypt: true },
			services: { firebase: 'foo-bar' },
		} as unknown as RtConfig);

		const { getEncrypter } = await bootFirebase();
		const data = 'foo';
		expect(getEncrypter().encrypt(data)).not.toBe(data);
	});

	it('can bypass encrypting data', async () => {
		vi.spyOn(configDao, 'useRtConfig').mockReturnValue({
			firebase: { encrypt: false },
			services: { firebase: 'foo-bar' },
		} as unknown as RtConfig);

		const { getEncrypter } = await bootFirebase();
		const data = 'foo';
		expect(getEncrypter().encrypt(data)).toBe(data);
	});
});
