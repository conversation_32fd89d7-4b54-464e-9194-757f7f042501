import { dataSourceFactory } from '@/domain/services/Rt/Firestore';
import { getFirebase } from '@/domain/services/Rt/Firebase';
import { transparentEncrypter } from '@/domain/services/Encrypter';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { doc, getDoc, onSnapshot, runTransaction, setDoc, Timestamp } from 'firebase/firestore';

vi.mock('@/domain/services/Rt/Firebase', () => ({
	getFirebase: vi.fn(),
}));

vi.mock('firebase/firestore', () => ({
	doc: vi.fn(),
	getDoc: vi.fn(),
	onSnapshot: vi.fn(),
	runTransaction: vi.fn(),
	setDoc: vi.fn(),
	Timestamp: {
		fromDate: vi.fn(),
	},
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		variables: {
			firebaseTTL: new Date(),
		},
	},
}));

const dataSource = (strategy: unknown, nullableRecords = true) =>
	dataSourceFactory('ds-id', 'collection-id', 'document-id', strategy, nullableRecords, true);

describe('Firestore datasource', () => {
	const destroyer = vi.fn();
	let mockedOnSnapshotCallback = () => {};

	const datSourceMapper = {
		extractData: vi.fn(),
		prepareData: vi.fn(),
		rawProps: ['foo', 'bar'],
	};

	beforeEach(() => {
		vi.resetAllMocks();
		(doc as Mock).mockReturnValue('docRef');
		(onSnapshot as Mock).mockImplementation((docRef, fn) => {
			mockedOnSnapshotCallback = fn;
			return destroyer;
		});

		(getFirebase as Mock).mockImplementation(() => ({
			getFirestore: vi.fn(() => 'db'),
			getEncrypter: vi.fn(transparentEncrypter),
			isReadonly: vi.fn(() => false),
		}));
	});

	it('boots with firestore', () => {
		dataSource(datSourceMapper);
		expect(doc).toHaveBeenCalledWith('db', 'collection-id/document-id');
		expect(onSnapshot).toHaveBeenCalledWith('docRef', expect.any(Function));
	});

	it('sends data', () => {
		dataSource(datSourceMapper).set({ foo: 'foo', bar: 'bar', baz: 'baz', qux: 'qux' });
		expect(onSnapshot).toHaveBeenCalledWith('docRef', expect.any(Function));
	});

	it('doesnt send data on guard failure', () => {
		dataSource({...datSourceMapper, guard: () => false }).set({ foo: 'foo', bar: 'bar', baz: 'baz', qux: 'qux' });
		expect(onSnapshot).toHaveBeenCalledWith('docRef', expect.any(Function));
	});

	it('doesnt send data on readonly instance', () => {
		(getFirebase as Mock).mockImplementation(() => ({
			getFirestore: vi.fn(() => 'db'),
			getEncrypter: vi.fn(transparentEncrypter),
			isReadonly: vi.fn(() => true),
		}));

		dataSource(datSourceMapper).set({ foo: 'foo', bar: 'bar', baz: 'baz', qux: 'qux' });
		expect(setDoc).not.toHaveBeenCalled();
	});

	it('sends data via transaction', async () => {
		const firestoreTransaction = {
			get: vi.fn(),
			update: vi.fn(),
			set: vi.fn(),
		};

		firestoreTransaction.get.mockResolvedValue({
			exists: () => true,
			data: () => ({
				expireAt: Timestamp.fromDate(new Date()),
				data: {
					foo: 'foo',
					bar: 'bar',
					encrypted: { baz: 'baz', qux: 'qux' },
				},
			}),
		});

		(runTransaction as Mock).mockImplementation((docRef, fn) => {
			fn(firestoreTransaction);
			return Promise.resolve();
		});

		const ds = dataSource({
			prepareData: (data: any, updated: any) => ({
				...data,
				foo: updated.foo,
				baz: updated.baz,
			}),
			rawProps: ['foo', 'bar'],
		});

		ds.set((data: any) => ({
			...data,
			foo: 'foo-updated',
			baz: 'baz-updated',
			bar: 'bar-updated',
		}));

		firestoreTransaction.update.mockImplementation((docRef, data) =>
			expect(data).toStrictEqual({
				expireAt: Timestamp.fromDate(new Date()),
				data: {
					foo: 'foo-updated',
					bar: 'bar',
					encrypted: { baz: 'baz-updated', qux: 'qux' },
				},
			})
		);
	});

	it('receives data with extractor', async () => {
		getDoc.mockResolvedValue({
			exists: () => true,
			data: () => ({
				expireAt: Timestamp.fromDate(new Date()),
				data: {
					foo: 'foo',
					bar: 'bar',
					encrypted: { baz: 'baz', qux: 'qux' },
				},
			}),
		});

		const ds = dataSource({
			extractData: (data: any) => data.baz,
			prepareData: vi.fn(),
			rawProps: ['foo', 'bar'],
		});

		const data = await ds.get();
		expect(data).toBe('baz');
	});

	it('non nullable ds doesnt send null to subscribers if theres no record', () => {
		const ds = dataSource({}, false);

		const subscriberCallback = vi.fn();
		ds.subscribe(subscriberCallback);

		mockedOnSnapshotCallback({
			exists: () => false,
		});

		expect(subscriberCallback).not.toHaveBeenCalled();
	});

	it('nullable ds sends null to subscribers if theres no record', () => {
		const ds = dataSource({}, true);

		const subscriberCallback = vi.fn();
		ds.subscribe(subscriberCallback);

		mockedOnSnapshotCallback({
			exists: () => false,
		});

		expect(subscriberCallback).toHaveBeenCalledWith(null);
	});

	it('non-nullable ds doesnt send null to subscribers if theres null record', () => {
		const ds = dataSource({}, false);

		const subscriberCallback = vi.fn();
		ds.subscribe(subscriberCallback);

		mockedOnSnapshotCallback({
			exists: () => true,
			data: () => null,
		});

		expect(subscriberCallback).not.toHaveBeenCalled();
	});

	it('nullable ds sends null to subscribers if theres null record', () => {
		const ds = dataSource({}, true);

		const subscriberCallback = vi.fn();
		ds.subscribe(subscriberCallback);

		mockedOnSnapshotCallback({
			exists: () => true,
			data: () => null,
		});

		expect(subscriberCallback).toHaveBeenCalledWith(null);
	});

	it('subscribes to data', async () => {
		const ds = dataSource({ rawProps: ['x'] });

		const subscriberCallback = vi.fn();
		ds.subscribe(subscriberCallback);

		mockedOnSnapshotCallback({
			exists: () => true,
			data: () => ({ data: { x: 'foo' } }),
		});

		mockedOnSnapshotCallback({
			exists: () => true,
			data: () => ({ data: { x: 'foo' } }),
		});

		mockedOnSnapshotCallback({
			exists: () => true,
			data: () => ({ data: { x: 'bar' } }),
		});

		expect(subscriberCallback).toHaveBeenCalledTimes(3);
		expect(subscriberCallback).toHaveBeenNthCalledWith(1, { x: 'foo' });
		expect(subscriberCallback).toHaveBeenNthCalledWith(2, { x: 'foo' });
		expect(subscriberCallback).toHaveBeenNthCalledWith(3, { x: 'bar' });
	});

	it('filters repeated data', async () => {
		const ds = dataSource({ rawProps: ['x'] });

		const subscriberCallback = vi.fn();
		ds.subscribe(subscriberCallback, true);

		mockedOnSnapshotCallback({
			exists: () => true,
			data: () => ({ data: { x: 'foo' } }),
		});

		mockedOnSnapshotCallback({
			exists: () => true,
			data: () => ({ data: { x: 'foo' } }),
		});

		mockedOnSnapshotCallback({
			exists: () => true,
			data: () => ({ data: { x: 'bar' } }),
		});

		expect(subscriberCallback).toHaveBeenCalledTimes(2);
		expect(subscriberCallback).toHaveBeenNthCalledWith(1, { x: 'foo' });
		expect(subscriberCallback).toHaveBeenNthCalledWith(2, { x: 'bar' });
	});
});
