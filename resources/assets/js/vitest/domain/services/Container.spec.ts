import { afterEach, beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { bootContainer, useContainer, useServiceProvider } from '@/domain/services/Container';

const makeAdder = (base: number) => ({
	whoAmI: () => 'adder to ' + base,
	calculate: (x: number) => x + base,
});

const makeMultiplier = () => ({
	whoAmI: () => 'multiplier',
	calculate: (x: number, y: number) => x * y,
});

vi.mock('@/domain/services/VueHooks', () => ({
	vueHookFactories: {
		foo: () => vi.fn(() => 'foo loaded'),
		bar: () => vi.fn(() => 'bar loaded'),
	},
}));

describe('Container', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('boots and retrieves dependencies', () => {
		const dependencies = bootContainer({
			add2: () => makeAdder(2),
			add5: () => makeAdder(5),
			multiply: () => makeMultiplier(),
		});

		const { add2, add5, multiply } = useContainer<{
			add2: ReturnType<typeof makeAdder>;
			add5: ReturnType<typeof makeAdder>;
			multiply: ReturnType<typeof makeMultiplier>;
		}>();

		expect(add2.whoAmI()).toBe('adder to 2');
		expect(add2.calculate(3)).toBe(5);

		expect(add5.whoAmI()).toBe('adder to 5');
		expect(add5.calculate(3)).toBe(8);

		expect(multiply.whoAmI()).toBe('multiplier');
		expect(multiply.calculate(3, 5)).toBe(15);

		expect(dependencies.add2).toBe(add2);
		expect(dependencies.add5).toBe(add5);
		expect(dependencies.multiply).toBe(multiply);
	});

	it('exposes service provider', () => {
		const serviceProvider = useServiceProvider({
			multiply: () => makeMultiplier(),
		});

		serviceProvider.boot();

		const { multiply } = serviceProvider.use();

		expect(multiply.whoAmI()).toBe('multiplier');
		expect(multiply.calculate(3, 5)).toBe(15);
	});

	it('cant reassign dependencies', () => {
		bootContainer({
			add2: () => {},
		});

		const container = useContainer<{
			add2: () => void;
		}>();

		try {
			container.add2 = () => {};
		} catch (e: any) {
			expect(e).toBeInstanceOf(TypeError);
			expect(e.message).toBe("'set' on proxy: trap returned falsish for property 'add2'");
		}
	});

	it('includes vue hooks from vueHookFactories without booting', () => {
		const container = useContainer<{
			foo: Mock<() => string>;
			bar: Mock<() => string>;
		}>();

		expect(container.foo()).toBe('foo loaded');
		expect(container.bar()).toBe('bar loaded');
	});

	it('includes vue hooks after booting', () => {
		bootContainer({
			load: () => vi.fn(() => 'loaded'),
		});

		const { load, foo, bar } = useContainer<{
			foo: Mock<() => string>;
			bar: Mock<() => string>;
			load: Mock<() => string>;
		}>();

		expect(load()).toBe('loaded');
		expect(foo()).toBe('foo loaded');
		expect(bar()).toBe('bar loaded');
	});
});
