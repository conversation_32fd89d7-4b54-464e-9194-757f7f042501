import { describe, expect, it } from 'vitest';
import { encrypter, jsonEncrypter, selectiveEncrypter } from '@/domain/services/Encrypter';

describe('Encrypter', () => {
	it('decrypts laravel string', () => {
		const cryptogramFromLaravel =
			// eslint-disable-next-line max-len
			'eyJpdiI6IlRoYXdYMzRWaEMvQjVNVEJBUldiY2c9PSIsInZhbHVlIjoibWNLN2loRXZjNGZmZXFDMVdYZWZMQT09IiwibWFjIjoiOWMyYTU3OWFmYzBkODdkYWQ4OGJjZDIzODNjMjc2Nzk5OTk2MTFkNjY3NjVkYmU0MDE5YzZkZmM3Mjc1MTRmMiIsInRhZyI6IiJ9';

		expect(encrypter('0123456789ABCDEF').decrypt(cryptogramFromLaravel)).toBe('abc');
	});

	it('decrypts laravel object', () => {
		const cryptogramFromLaravel =
			// eslint-disable-next-line max-len
			'eyJpdiI6IldZSEM4NHdZa3Iydk1rRzVzWGREL1E9PSIsInZhbHVlIjoiTUdFaDdXc3hCR2FkMFg0eHhVMkZOQT09IiwibWFjIjoiYmZmNGQxNmIwOTRkYzJkOGQwNDI3OGM1NzQwZDA1MzJjNjU1ZmUzMTA0M2RiOTZiNmI0MDAwODNmYTU4NjZjNSIsInRhZyI6IiJ9';

		expect(jsonEncrypter('0123456789ABCDEF').decrypt(cryptogramFromLaravel)).toStrictEqual({ foo: 'bar' });
	});

	it('encrypts strings', () => {
		const e = encrypter('0123456789ABCDEF');

		const cryptogram = e.encrypt('abc');
		expect(e.decrypt(cryptogram)).toBe('abc');
	});

	it('encrypts objects', () => {
		const e = jsonEncrypter('0123456789ABCDEF');

		expect(e.decrypt(e.encrypt('abc'))).toBe('abc');
		expect(e.decrypt(e.encrypt({ foo: 'bar' }))).toStrictEqual({ foo: 'bar' });
	});

	it('fails on shork passpharse', () => {
		expect(() => encrypter('0123456789A')).toThrowError('Passphrase must be at least 12 characters long');
		expect(() => encrypter('0123456789AB')).not.toThrowError();
	});

	it('encrypts / decrypts objects selectively', () => {
		const e = selectiveEncrypter(jsonEncrypter('0123456789ABCDEF'), ['foo']);

		const obj = { foo: 'bar', baz: 'qux', quux: 'corge' };
		const encrypted = e.encrypt(obj);

		expect(Object.keys(encrypted)).toStrictEqual(['foo', 'encrypted']);
		expect(encrypted.foo).toBe('bar');
		expect(encrypted.encrypted).not.toBeUndefined();

		expect(e.decrypt(encrypted)).toStrictEqual(obj);
		expect(e.decrypt({ foo: 'foo-1', bar: 'bar-1' })).toStrictEqual({ foo: 'foo-1' });

		expect(() => e.decrypt({ foo: 'foo-1', encrypted: 'bar-1' })).toThrowError();
	});

	it('decrypts should not throw an error if object is undefined', () => {
		const e = selectiveEncrypter(jsonEncrypter('0123456789ABCDEF'), null);

		const obj = undefined;

		expect(() => e.decrypt(obj)).not.toThrowError();
		expect(e.decrypt(obj)).toStrictEqual({});
	});
});
