import * as LocalEventBus from '@/domain/services/LocalEventBus';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

enum Events {
	Event1 = 'event-1',
	Event2 = 'event-2',
}

type EventPayloads = {
	[Events.Event1]: string;
	[Events.Event2]: string;
};

describe('LocalEventBus', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('one time subscriber', () => {
		const callback1 = vi.fn();
		const callback2 = vi.fn();

		LocalEventBus.on<EventPayloads, Events.Event1>(Events.Event1, callback1, true);
		LocalEventBus.on<EventPayloads, Events.Event1>(Events.Event1, callback2);

		LocalEventBus.emit<EventPayloads, Events.Event1>(Events.Event1, 'foo');
		LocalEventBus.emit<EventPayloads, Events.Event1>(Events.Event1, 'bar');

		expect(callback1).toHaveBeenCalledTimes(1);
		expect(callback2).toHaveBeenCalledTimes(2);
	});

	it('subscribes and unsubscribes', () => {
		const callback1 = vi.fn();
		const callback2 = vi.fn();

		LocalEventBus.on<EventPayloads, Events.Event1>(Events.Event1, callback1);
		LocalEventBus.on<EventPayloads, Events.Event1>(Events.Event1, callback2);
		const id3 = LocalEventBus.on<EventPayloads, Events.Event1>(Events.Event1, callback2);

		LocalEventBus.emit<EventPayloads, Events.Event1>(Events.Event1, 'foo');

		LocalEventBus.off<Events.Event1>(Events.Event1, id3);

		LocalEventBus.emit<EventPayloads, Events.Event1>(Events.Event1, 'foo');

		expect(callback1).toHaveBeenCalledTimes(2);
		expect(callback2).toHaveBeenCalledTimes(3);
	});

	it('passes id around', () => {
		const callback1 = vi.fn();
		const callback2 = vi.fn();

		const id1 = LocalEventBus.on<EventPayloads, Events.Event1>(Events.Event1, callback1);
		const id2 = LocalEventBus.on<EventPayloads, Events.Event1>(Events.Event1, callback2);
		const id3 = LocalEventBus.on<EventPayloads, Events.Event1>(Events.Event1, callback2);

		LocalEventBus.emit<EventPayloads, Events.Event1>(Events.Event1, 'foo');

		expect(callback1).toHaveBeenCalledOnce();
		expect(callback1).toHaveBeenCalledWith('foo', id1);

		expect(callback2).toHaveBeenCalledTimes(2);
		expect(callback2).toHaveBeenCalledWith('foo', id2);
		expect(callback2).toHaveBeenCalledWith('foo', id3);
	});

	it('handles different events', () => {
		const callback1 = vi.fn();
		const callback2 = vi.fn();
		const callback3 = vi.fn();

		const id1 = LocalEventBus.on<EventPayloads, Events.Event1>(Events.Event1, callback1);
		const id2 = LocalEventBus.on<EventPayloads, Events.Event2>(Events.Event2, callback2);
		const id3 = LocalEventBus.on<EventPayloads, Events.Event2>(Events.Event2, callback3);

		LocalEventBus.emit<EventPayloads, Events.Event1>(Events.Event1, 'first');
		LocalEventBus.emit<EventPayloads, Events.Event2>(Events.Event2, 'second');

		expect(callback1).toHaveBeenCalledOnce();
		expect(callback1).toHaveBeenCalledWith('first', id1);

		expect(callback2).toHaveBeenCalledOnce();
		expect(callback2).toHaveBeenCalledWith('second', id2);

		expect(callback3).toHaveBeenCalledOnce();
		expect(callback3).toHaveBeenCalledWith('second', id3);
	});
});
