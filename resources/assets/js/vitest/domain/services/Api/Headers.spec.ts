import { headersFactory } from '@/domain/services/Api/Headers';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		CSRF_TOKEN: 'csrfToken-value',
	},
}));

describe('API Headers', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('produces headers', () => {
		const headers = headersFactory({
			'X-Test': 'test-value',
		});

		expect(headers).toStrictEqual({
			'X-CSRF-TOKEN': 'csrfToken-value',
			'X-Requested-With': 'XMLHttpRequest',
			Accept: 'application/json',
			'X-Test': 'test-value',
		});
	});
});
