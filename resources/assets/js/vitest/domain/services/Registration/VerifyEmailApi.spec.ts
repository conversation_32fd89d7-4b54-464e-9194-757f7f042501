import { postVerifyEmail } from '@/domain/services/Registration/VerifyEmailApi';
import { describe, it, vi } from 'vitest';

describe('postVerifyEmail', () => {
	beforeEach(() => {
		vi.mock('@/domain/services/Api/Endpoint', () => {
			const actual = vi.importActual('@/domain/services/Api/Endpoint');
			return {
				...actual,
				PostingEndpoint: vi
					.fn()
					.mockImplementation((url: string) => () => (data) => Promise.resolve({ url: url, data: data })),
			};
		});
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	it('should call postVerifyEmail with correct data and URL', async () => {
		const email = '<EMAIL>';
		const data = { email };
		postVerifyEmail(data).then((response: unknown) => {
			expect(response).toMatchObject({
				data: data,
				url: '/register/verify-email',
			});
		});
	});
});
