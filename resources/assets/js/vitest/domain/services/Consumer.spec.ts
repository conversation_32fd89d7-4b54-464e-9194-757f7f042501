import { AuthenticationStore } from '@/lib/store/modules/authentication';
import { Consumer } from '@/domain/models/Consumer';
import { consumerService } from '@/domain/services/ConsumerService';
import { describe, expect, it, vi } from 'vitest';

const authStoreMock = {
	state: {
		authentication: {
			consumer: 'test-consumer' as unknown as Consumer,
		},
	},
	commit: vi.fn(),
} as unknown as AuthenticationStore;

const consumer = consumerService(authStoreMock);

describe('Consumer service', () => {
	it('retrieves consumer out from AuthenticationStore', () => {
		expect(consumer.consumer).toBe('test-consumer');
	});

	it('updates consumer image', () => {
		consumer.updateImage('test-image');
		expect(authStoreMock.commit).toHaveBeenCalledWith('authentication/updateConsumerImage', 'test-image');
	});
});
