import { vueData } from '@/domain/services/VueData';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { VueData, VueDataProvider } from '@/domain/services/VueDataProvider';

vi.mock('@/domain/services/VueDataProvider', () => ({
	VueDataProvider: {
		getGlobal: vi.fn(),
		getGlobalData: vi.fn(),
		getAllGlobalData: vi.fn(),
	},
}));

describe('VueData facade', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('exposes expected props', () => {
		expect(Object.keys(vueData)).toStrictEqual([
			'config',
			'variables',
			'consumer',
			'CSRF_TOKEN',
			'features',
			'globals',
			'links',
			'routes',
			'settings',
			'translations',
			'language',
			'defaultLanguage',
			'supportedLanguages',
		]);
	});

	it('exposes config', () => {
		vi.spyOn(VueDataProvider, 'getGlobalData').mockReturnValue('test-config');
		expect(vueData.config).toBe('test-config');
		expect(VueDataProvider.getGlobalData).toHaveBeenCalledWith('config');
	});

	it('exposes csrf', () => {
		vi.spyOn(VueDataProvider, 'getGlobalData').mockReturnValue('test-csrf');
		expect(vueData.CSRF_TOKEN).toBe('test-csrf');
		expect(VueDataProvider.getGlobalData).toHaveBeenCalledWith('CSRF_TOKEN', null);
	});

	it('exposes features', () => {
		vi.spyOn(VueDataProvider, 'getGlobalData').mockReturnValue([
			{
				feature: 'foo',
				enabled: true,
			},
			{
				feature: 'bar',
				enabled: false,
			},
		]);
		expect(vueData.features).toStrictEqual({ foo: true, bar: false });
		expect(VueDataProvider.getGlobalData).toHaveBeenCalledWith('features', []);
	});

	it('exposes globals', () => {
		vi.spyOn(VueDataProvider, 'getAllGlobalData').mockReturnValue('test-data' as unknown as VueData);
		expect(vueData.globals).toBe('test-data');
		expect(VueDataProvider.getAllGlobalData).toHaveBeenCalledOnce();
	});

	it('exposes links', () => {
		vi.spyOn(VueDataProvider, 'getGlobalData').mockReturnValue('test-links');
		expect(vueData.links).toBe('test-links');
		expect(VueDataProvider.getGlobalData).toHaveBeenCalledWith('links');
	});

	it('exposes routes', () => {
		vi.spyOn(VueDataProvider, 'getGlobalData').mockReturnValue('test-routes');
		expect(vueData.routes).toBe('test-routes');
		expect(VueDataProvider.getGlobalData).toHaveBeenCalledWith('routes');
	});

	it('exposes settings', () => {
		vi.spyOn(VueDataProvider, 'getGlobalData').mockReturnValue('test-settings');
		expect(vueData.settings).toBe('test-settings');
		expect(VueDataProvider.getGlobalData).toHaveBeenCalledWith('settings', undefined);
	});

	it('exposes translations', () => {
		vi.spyOn(VueDataProvider, 'getGlobalData').mockReturnValue('test-translations');
		expect(vueData.translations).toBe('test-translations');
		expect(VueDataProvider.getGlobalData).toHaveBeenCalledWith('translations');
	});

	it('exposes language', () => {
		vi.spyOn(VueDataProvider, 'getGlobalData').mockReturnValue('test-language');
		expect(vueData.language).toBe('test-language');
		expect(VueDataProvider.getGlobalData).toHaveBeenCalledWith('language');
	});

	it('exposes defaultLanguage', () => {
		vi.spyOn(VueDataProvider, 'getGlobal').mockReturnValue('test-defaultLanguage');
		expect(vueData.defaultLanguage).toBe('test-defaultLanguage');
		expect(VueDataProvider.getGlobal).toHaveBeenCalledWith('defaultLanguage');
	});

	it('exposes supportedLanguages', () => {
		vi.spyOn(VueDataProvider, 'getGlobal').mockReturnValue('test-supportedLanguages');
		expect(vueData.supportedLanguages).toBe('test-supportedLanguages');
		expect(VueDataProvider.getGlobal).toHaveBeenCalledWith('supportedLanguages', true);
	});

	it('exposes variables', () => {
		vi.spyOn(VueDataProvider, 'getGlobalData').mockReturnValue('test-variables');
		expect(vueData.variables).toBe('test-variables');
		expect(VueDataProvider.getGlobalData).toHaveBeenCalledWith('variables');
	});

	it('exposes consumer', () => {
		vi.spyOn(VueDataProvider, 'getGlobal').mockReturnValue('test-consumer');
		expect(vueData.consumer).toBe('test-consumer');
		expect(VueDataProvider.getGlobal).toHaveBeenCalledWith('consumer', true, true);
	});
});
