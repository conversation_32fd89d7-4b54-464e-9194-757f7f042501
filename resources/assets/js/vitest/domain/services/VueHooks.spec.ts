import { vueHookFactories } from '@/domain/services/VueHooks';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { onBeforeUnmount, onMounted, provide } from 'vue';

vi.mock('vue', () => ({
	provide: vi.fn(),
	onMounted: vi.fn(),
	onBeforeUnmount: vi.fn(),
}));

describe('default Container factories', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('exposes expected factories', () => {
		expect(Object.keys(vueHookFactories)).toEqual([
			'onBeforeMount',
			'onMounted',
			'onBeforeUnmount',
			'provide',
			'onBeforeAppUnload',
			'onWatch',
		]);
	});

	it('exposes expected vue hooks', () => {
		expect(vueHookFactories.onMounted()).toBe(onMounted);
		expect(vueHookFactories.onBeforeUnmount()).toBe(onBeforeUnmount);
		expect(vueHookFactories.provide()).toBe(provide);
	});
});
