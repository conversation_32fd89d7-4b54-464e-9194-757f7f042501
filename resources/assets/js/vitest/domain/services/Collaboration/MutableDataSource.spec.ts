import { User } from '@/domain/models/User';
import { valueDataSource } from '@/domain/services/Collaboration/MutableDataSource';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import dataSources, { Callback, removeCacheDataSource } from '@/domain/services/Rt/DataSource';

vi.mock('underscore', () => ({
	default: {
		debounce: (fn: unknown) => fn,
	},
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		consumer: 'consumer' as unknown as User,
		variables: {
			submittableToken: 'submittable-token',
		},
	},
}));

vi.mock('@/domain/services/Rt/DataSource', () => ({
	default: {
		lockables: vi.fn(),
	},
	removeCacheDataSource: vi.fn(),
}));

vi.mock('@/domain/services/Encrypter', () => ({
	jsonEncrypter: vi.fn(),
}));

describe('Mutable values datasource', () => {
	const lockables = {
		subscribe: vi.fn(),
		get: vi.fn(),
		set: vi.fn(),
		destroy: vi.fn(),
		remove: vi.fn(),
	};

	beforeEach(() => {
		vi.resetAllMocks();
		vi.spyOn(dataSources, 'lockables').mockReturnValue(lockables);
	});

	it('should create stream', () => {
		const datasource = valueDataSource('formSlug', 'submittableSlug', 'lockableId');

		expect(dataSources.lockables).toHaveBeenCalledWith('formSlug', 'submittableSlug', 'lockableId', 'consumer');
		expect(datasource).haveOwnProperty('get');
		expect(datasource).haveOwnProperty('set');
		expect(datasource).haveOwnProperty('subscribe');
		expect(datasource).haveOwnProperty('destroy');
		expect(datasource).haveOwnProperty('remove');
	});

	it('should remove streams', () => {
		valueDataSource('formSlug', 'submittableSlug', 'lockableId').remove();
		expect(lockables.remove).toHaveBeenCalledOnce();
	});

	it('should destroy streams', () => {
		valueDataSource('formSlug', 'submittableSlug', 'lockableId').destroy();
		expect(lockables.destroy).toHaveBeenCalledOnce();
		expect(removeCacheDataSource).toHaveBeenCalledOnce();
	});

	it('should set data to cout', () => {
		valueDataSource('formSlug', 'submittableSlug', 'lockableId').set('value');
		expect(lockables.set).toHaveBeenCalledWith('value');
	});

	it('should subscribe to cin', () => {
		valueDataSource('formSlug', 'submittableSlug', 'lockableId').subscribe('subscriber' as unknown as Callback<any>);
		expect(lockables.subscribe).toHaveBeenCalledWith('subscriber');
	});
});
