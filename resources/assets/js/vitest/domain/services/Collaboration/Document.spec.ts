import dataSources from '@/domain/services/Rt/DataSource';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
	collectionServiceFactory,
	documentServiceFactory,
	emptyDocumentService,
} from '@/domain/services/Collaboration/Document';

vi.mock('underscore', () => ({
	default: {
		debounce: (fn: unknown) => fn,
	},
}));

vi.mock('@/domain/services/Rt/DataSource');

describe('Shared document', () => {
	const collectionService = collectionServiceFactory('submittableSlug', 'formSlug');
	const documentService = documentServiceFactory('submittableSlug', 'formSlug');
	const mockSet = vi.fn();
	const document = {
		subscribe: vi.fn(),
		set: mockSet,
		destroy: vi.fn(),
	};

	const collection = {
		subscribe: vi.fn(),
		set: vi.fn(),
		destroy: vi.fn(),
	};

	beforeEach(() => {
		vi.resetAllMocks();
		vi.useRealTimers();
		vi.spyOn(dataSources, 'collection').mockReturnValue(collection);
		vi.spyOn(dataSources, 'document').mockReturnValue(document);
	});

	it('should make a collectionService', () => {
		const doc = collectionService('document-1');
		expect(dataSources.collection).toHaveBeenCalledWith('formSlug', 'submittableSlug', 'document-1');
		expect(doc).haveOwnProperty('subscribe');
		expect(doc).haveOwnProperty('set');
	});

	it('collectionService gets destroyed', () => {
		collectionService('document-1').destroy();
		expect(collection.destroy).toHaveBeenCalledOnce();
	});

	it('should make a documentService', () => {
		const doc = documentService('document-1');
		expect(dataSources.document).toHaveBeenCalledWith('formSlug', 'submittableSlug', 'document-1');
		expect(doc).haveOwnProperty('subscribe');
		expect(doc).haveOwnProperty('set');
	});

	it('documentService gets destroyed', () => {
		documentService('document-1').destroy();
		expect(document.destroy).toHaveBeenCalledOnce();
	});

	it('retrieves empty service', () => {
		const doc = emptyDocumentService();
		expect(doc).haveOwnProperty('destroy');
		expect(doc).haveOwnProperty('subscribe');
		expect(doc).haveOwnProperty('set');

		const subscriber = vi.fn();
		doc.subscribe(subscriber);

		doc.set('foo');
		expect(subscriber).toHaveBeenCalledWith('foo');
	});

	it('should call document.set with forceSet parameter', () => {
		const doc = documentService('document-1');
		vi.useFakeTimers();

		doc.set({ foo: 'bar' }, false);
		vi.advanceTimersByTime(60);
		expect(mockSet).toHaveBeenCalledWith({ foo: 'bar' }, false);

		doc.set({ foo: 'bar' }, true);
		vi.advanceTimersByTime(60);
		expect(mockSet).toHaveBeenCalledWith({ foo: 'bar' }, true);

		// By default, forceSet should be false if not provided
		doc.set({ foo: 'bar' });
		vi.advanceTimersByTime(60);
		expect(mockSet).toHaveBeenCalledWith({ foo: 'bar' }, false);
	});
});
