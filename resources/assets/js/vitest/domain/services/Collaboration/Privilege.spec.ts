import { vueData } from '@/domain/services/VueData';
import { beforeEach, describe, expect, it, test, vi } from 'vitest';
import { Collaborator, collaboratorAccess, CollaboratorPrivilege } from '@/domain/models/Collaborator';

vi.mock('@/domain/services/VueData');

describe('Field locking logic', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	test('viewer should cant access anything', async () => {
		const collaborator: Collaborator = {
			user: 'consumerSlug',
			privilege: CollaboratorPrivilege.VIEWER,
		};

		const actions = collaboratorAccess(collaborator);
		expect(actions.canEdit).toBe(false);
		expect(actions.canSubmit).toBe(false);
		expect(actions.canSave).toBe(false);
	});

	test('editor should can edit and save only', async () => {
		const collaborator: Collaborator = {
			user: 'consumerSlug',
			privilege: CollaboratorPrivilege.EDITOR,
		};

		const actions = collaboratorAccess(collaborator);

		expect(actions.canEdit).toBe(true);
		expect(actions.canSubmit).toBe(false);
		expect(actions.canSave).toBe(true);
	});

	test('submitter should can do all actions', async () => {
		const collaborator: Collaborator = {
			user: 'consumerSlug',
			privilege: CollaboratorPrivilege.SUBMITTER,
		};

		const actions = collaboratorAccess(collaborator);

		expect(actions.canEdit).toBe(true);
		expect(actions.canSubmit).toBe(true);
		expect(actions.canSave).toBe(true);
	});

	test('collaboration owner should have all access', async () => {
		const collaborator: Collaborator = {
			user: 'consumerSlug',
			privilege: CollaboratorPrivilege.VIEWER,
			owner: true,
		};

		const actions = collaboratorAccess(collaborator);

		expect(actions.canEdit).toBe(true);
		expect(actions.canSubmit).toBe(true);
		expect(actions.canSave).toBe(true);
	});

	test('manager should have a full access in manage workspace', async () => {
		vi.spyOn(vueData, 'variables', 'get').mockReturnValue({ selectedContext: 'manage' });

		const collaborator: Collaborator = {
			user: 'consumerSlug',
			privilege: CollaboratorPrivilege.VIEWER,
			manager: true,
		};

		const actions = collaboratorAccess(collaborator);

		expect(actions.canEdit).toBe(true);
		expect(actions.canSubmit).toBe(true);
		expect(actions.canSave).toBe(true);
	});

	test('manager should not have a full access in the enter workspace entry if their privilege is "VIEWER" only', async () => {
		vi.spyOn(vueData, 'variables', 'get').mockReturnValue({ selectedContext: 'enter' });

		const collaborator: Collaborator = {
			user: 'consumerSlug',
			privilege: CollaboratorPrivilege.VIEWER,
			manager: true,
		};

		const actions = collaboratorAccess(collaborator);

		expect(actions.canEdit).toBe(false);
		expect(actions.canSubmit).toBe(false);
		expect(actions.canSave).toBe(false);
	});

	test('manager should can edit an entry in enter workspace if their privilege is "EDITOR"', async () => {
		vi.spyOn(vueData, 'variables', 'get').mockReturnValue({ selectedContext: 'enter' });

		const collaborator: Collaborator = {
			user: 'consumerSlug',
			privilege: CollaboratorPrivilege.EDITOR,
			manager: true,
		};

		const actions = collaboratorAccess(collaborator);

		expect(actions.canEdit).toBe(true);
		expect(actions.canSubmit).toBe(false);
		expect(actions.canSave).toBe(true);
	});

	test('manager should can edit and submit an entry in enter workspace if their privilege is "SUBMITTER"', async () => {
		vi.spyOn(vueData, 'variables', 'get').mockReturnValue({ selectedContext: 'enter' });

		const collaborator: Collaborator = {
			user: 'consumerSlug',
			privilege: CollaboratorPrivilege.SUBMITTER,
			manager: true,
		};

		const actions = collaboratorAccess(collaborator);

		expect(actions.canEdit).toBe(true);
		expect(actions.canSubmit).toBe(true);
		expect(actions.canSave).toBe(true);
	});

	it('should can do all actions by default', async () => {
		const actions = collaboratorAccess();

		expect(actions.canEdit).toBe(true);
		expect(actions.canSubmit).toBe(true);
		expect(actions.canSave).toBe(true);
	});
});
