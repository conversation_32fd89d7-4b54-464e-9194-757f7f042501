import { Collaborator } from '@/domain/models/Collaborator';
import { collaboratorsService } from '@/domain/services/Collaboration/Collaborators';
import dataSources from '@/domain/services/Rt/DataSource';
import { vueData } from '@/domain/services/VueData';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { collaborationUIBus, CollaborationUISignals } from '@/domain/signals/Collaboration';
import { InviteCollaboratorsForm, useApi } from '@/domain/services/Collaboration/Api';

vi.mock('@/domain/services/Collaboration/Lockable', () => ({
	lockableServiceFactory: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Api', () => ({
	useApi: vi.fn(),
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		consumer: {
			slug: 'myself',
		},
		variables: {
			collaborators: [
				{ slug: 'myself-slug', user: 'myself', privilege: 'viewer' },
				{ slug: 'foo-slug', user: 'foo' },
				{ slug: 'bar-slug', user: 'bar' },
				{ slug: 'owner-slug', user: 'owner', owner: true },
			],
			form: { slug: 'form-slug' },
			submittable: { slug: 'submittable-slug' },
		},
	},
}));

const collaborators = [{ user: 'myself' }, { user: 'foo' }, { user: 'bar' }] as Collaborator[];

describe('Collaborators service', () => {
	const destroyer = vi.fn();
	const subscribe = vi.fn();
	const set = vi.fn();
	const get = () => Promise.resolve(collaborators);
	const getCollaborators = vi.fn();
	const inviteCollaborators = vi.fn();
	const removeCollaborator = vi.fn();
	const setOwner = vi.fn();
	const clearCache = vi.fn();

	beforeEach(() => {
		vi.resetAllMocks();
		subscribe.mockReturnValue(destroyer);
		vi.spyOn(dataSources, 'collaborators').mockReturnValue({
			subscribe,
			set,
			get,
		} as unknown as DataSource<unknown>);

		(useApi as Mock).mockReturnValue({
			getCollaborators,
			inviteCollaborators,
			removeCollaborator,
			setOwner,
			clearCache,
		});

		getCollaborators.mockReturnValue({
			then: (cb: (collaborators: Collaborator[]) => void) => cb(collaborators),
		});
	});

	it('connects to firebase', () => {
		collaboratorsService(true)('formSlug', 'submittableSlug');
		expect(dataSources.collaborators).toHaveBeenCalledWith('formSlug', 'submittableSlug');
		expect(useApi).toHaveBeenCalledWith('formSlug', 'submittableSlug');
	});

	it('uses cached instance', () => {
		collaboratorsService()('formSlug', 'submittableSlug');
		expect(dataSources.collaborators).not.toHaveBeenCalled();
	});

	it('clears service cache', () => {
		collaborationUIBus.emit(CollaborationUISignals.RELOAD, undefined);
		collaboratorsService()('formSlug', 'submittableSlug');
		expect(dataSources.collaborators).toHaveBeenCalled();
	});

	it('data gets saved to firebase', () => {
		const service = collaboratorsService(true)('formSlug', 'submittableSlug');
		expect(set).toHaveBeenCalledWith(collaborators);
		service.update([{ user: 'myself-1' }, { user: 'foo-1' }, { user: 'bar-1' }] as unknown as Collaborator[]);
		expect(set).toHaveBeenCalledWith([{ user: 'myself-1' }, { user: 'foo-1' }, { user: 'bar-1' }]);
	});

	it('invites collaborators', () => {
		collaboratorsService(true)('formSlug', 'submittableSlug').invite('some-data' as unknown as InviteCollaboratorsForm);
		expect(inviteCollaborators).toHaveBeenCalledWith('some-data');
	});

	it('removes collaborator', () => {
		collaboratorsService(true)('formSlug', 'submittableSlug').remove('some-slug');
		expect(removeCollaborator).toHaveBeenCalledWith('some-slug');
	});

	it('sets owner', () => {
		collaboratorsService(true)('formSlug', 'submittableSlug').setOwner('some-slug');
		expect(setOwner).toHaveBeenCalledWith('some-slug');
	});

	it('gets collaborators from firebase', () => {
		expect(collaboratorsService(true)('formSlug', 'submittableSlug').getAll()).toStrictEqual([]);

		subscribe.mockImplementation((cb) => {
			(cb as (p: Collaborator[]) => void)([
				{ user: 'myself-1' },
				{ user: 'foo-1' },
				{ user: 'bar-1' },
			] as unknown as Collaborator[]);
		});

		expect(collaboratorsService(true)('formSlug', 'submittableSlug').getAll()).toStrictEqual([
			{ user: 'myself-1' },
			{ user: 'foo-1' },
			{ user: 'bar-1' },
		]);
	});

	it('gets myself from firebase', () => {
		expect(collaboratorsService(true)('formSlug', 'submittableSlug').getAll()).toStrictEqual([]);

		subscribe.mockImplementation((cb) => {
			(cb as (p: Collaborator[]) => void)(vueData.variables.collaborators as Collaborator[]);
		});

		const me = collaboratorsService(true)('formSlug', 'submittableSlug').getMyself();
		expect(me?.user).toBe('myself');
	});

	it('gets particular collaborator from firebase', () => {
		expect(collaboratorsService(true)('formSlug', 'submittableSlug').getAll()).toStrictEqual([]);

		subscribe.mockImplementation((cb) => {
			(cb as (p: Collaborator[]) => void)(vueData.variables.collaborators as Collaborator[]);
		});

		const them = collaboratorsService(true)('formSlug', 'submittableSlug').getBySlug('foo-slug');
		expect(them?.user).toBe('foo');
	});

	it('update hook is called upon update from firebase', () => {
		let onValue: (p: Collaborator[]) => void = () => {};

		subscribe.mockImplementation((cb) => {
			onValue = cb as (p: Collaborator[]) => void;
		});

		const service = collaboratorsService(true)('formSlug', 'submittableSlug');
		const onUpdateCallback = vi.fn();
		service.onUpdate(onUpdateCallback);

		onValue([] as unknown as Collaborator[]);

		expect(onUpdateCallback).toHaveBeenCalled();
	});

	it('gets owner from firebase', () => {
		expect(collaboratorsService(true)('formSlug', 'submittableSlug').getAll()).toStrictEqual([]);

		subscribe.mockImplementation((cb) => {
			(cb as (p: Collaborator[]) => void)(vueData.variables.collaborators as Collaborator[]);
		});

		const me = collaboratorsService(true)('formSlug', 'submittableSlug').owner();

		expect(me?.user).toBe('owner');
		expect(me?.owner).toBe(true);
	});

	it('should call clearCache', () => {
		subscribe.mockImplementation((cb) => {
			(cb as (p: Collaborator[]) => void)([
				{ user: 'myself-1' },
				{ user: 'foo-1' },
				{ user: 'bar-1' },
			] as unknown as Collaborator[]);
		});
		collaboratorsService(true)('formSlug', 'submittableSlug');

		expect(clearCache).toHaveBeenCalledOnce();
	});
});
