import { Collaborator } from '@/domain/models/Collaborator';
import { SubmittableTokens } from '@/domain/services/Rt/SubmittableTokens';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { DataRequestMethod, DataResponse, dataSource } from '@/domain/services/Api/DataSource';
import { InviteCollaboratorsForm, useApi } from '@/domain/services/Collaboration/Api';

type CollaboratorsResponse = DataResponse<{ collaborators: Collaborator[] | [] }>;

vi.mock('@/domain/services/Api/Headers', async () => ({
	headersFactory: () => ({}),
}));

describe('Collaborators api', () => {
	beforeEach(() => {
		vi.resetAllMocks();
		vi.spyOn(dataSource, 'request');
	});

	it('requests for collaborators', async () => {
		const collaborators = [{ user: 'myself' }, { user: 'foo' }, { user: 'bar' }];
		const api = useApi('form-slug', 'submittable-slug');

		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: { collaborators } } as CollaboratorsResponse);

		const response = await api.getCollaborators();
		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/collaborators/form-slug/submittable-slug',
			method: DataRequestMethod.GET,
			headers: {},
			data: undefined,
		});
		expect(response).toStrictEqual(collaborators);
	});

	it('gets cached collaborators', async () => {
		const collaborators = [{ user: 'myself' }, { user: 'foo' }, { user: 'bar' }];
		const api = useApi('form-slug', 'submittable-slug');
		const response = await api.getCollaborators();
		expect(dataSource.request).not.toHaveBeenCalled();
		expect(response).toStrictEqual(collaborators);
	});

	it('gets a fresh collaborators data if the fresh parameter is true', async () => {
		const api = useApi('form-slug', 'submittable-slug');
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: { collaborators: [] } } as CollaboratorsResponse);

		const response = await api.getCollaborators(true);
		expect(dataSource.request).toHaveBeenCalled();
		expect(response).toStrictEqual([]);
	});

	it('gets cached collaborators for another resource', async () => {
		const api = useApi('form-slug-1', 'submittable-slug');
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: { collaborators: [] } } as CollaboratorsResponse);
		const response = await api.getCollaborators();
		expect(dataSource.request).toHaveBeenCalled();
		expect(response).toStrictEqual([]);
	});

	it('invites collaborators', async () => {
		const collaborators = [{ user: 'foo' }, { user: 'bar' }];
		const api = useApi('form-slug', 'submittable-slug');

		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: { collaborators } } as CollaboratorsResponse);
		const form = 'some-data' as unknown as InviteCollaboratorsForm;
		const response = await api.inviteCollaborators(form);
		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/collaborators/form-slug/submittable-slug/invite',
			method: DataRequestMethod.POST,
			headers: {},
			data: form,
		});
		expect(response).toStrictEqual(collaborators);
	});

	it('removes collaborator', async () => {
		const collaborators = [{ slug: 'foo' }, { slug: 'bar' }];
		const api = useApi('form-slug', 'submittable-slug');

		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: { collaborators } } as CollaboratorsResponse);
		const response = await api.removeCollaborator('foo');
		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/collaborators/foo',
			method: DataRequestMethod.DELETE,
			headers: {},
			data: undefined,
		});
		expect(response).toStrictEqual(collaborators);
	});

	it('transfers ownership', async () => {
		const collaborators = [{ user: 'foo' }, { user: 'bar' }];
		const api = useApi('form-slug', 'submittable-slug');

		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: { collaborators } } as CollaboratorsResponse);
		const response = await api.setOwner('foo');
		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/collaborators/form-slug/submittable-slug/transfer-ownership/foo',
			method: DataRequestMethod.POST,
			headers: {},
			data: undefined,
		});
		expect(response).toStrictEqual(collaborators);
	});

	it('get submittable tokens', async () => {
		const tokens: SubmittableTokens = { authToken: 'auth-token', submittableToken: 'submittable-token' };
		const api = useApi('form-slug', 'submittable-slug');

		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: tokens as SubmittableTokens });
		const response = await api.getSubmittableTokens();
		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/entry/entrant/form-slug/submittable-slug/tokens',
			method: DataRequestMethod.GET,
			headers: {},
			data: undefined,
		});

		expect(response).toStrictEqual(tokens);
	});
});
