import { valueDataSource } from '@/domain/services/Collaboration/MutableDataSource';
import { valueServiceFactory } from '@/domain/services/Collaboration/MutableValueService';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';

vi.mock('underscore', () => ({
	default: {
		debounce: (fn: unknown) => fn,
	},
}));

vi.mock('@/domain/services/Collaboration/MutableDataSource', () => ({
	valueDataSource: vi.fn(),
}));

describe('Mutable values service', () => {
	const values = {
		set: vi.fn(),
		subscribe: vi.fn(),
		destroy: vi.fn(),
	};

	beforeEach(() => {
		vi.resetAllMocks();
		(valueDataSource as Mock).mockReturnValueOnce(values);
	});

	it('should create streams', () => {
		const service = valueServiceFactory('submittableSlug', 'formSlug')('lockableId');
		expect(valueDataSource).toHaveBeenCalledWith('formSlug', 'submittableSlug', 'lockableId');

		expect(service).haveOwnProperty('set');
		expect(service).haveOwnProperty('subscribe');
		expect(service).haveOwnProperty('destroy');
	});

	it('sets unique value', () => {
		let onValue: (value: any) => void = () => {};

		(values.subscribe as Mock).mockImplementation((cb) => {
			onValue = cb as (value: any) => void;
		});

		const service = valueServiceFactory('submittableSlug', 'formSlug')('lockableId');
		onValue({ foo: 'bar-1' });

		service.set({ foo: 'bar-2' });
		expect(values.set).toHaveBeenCalledWith({ foo: 'bar-2' });
	});
});
