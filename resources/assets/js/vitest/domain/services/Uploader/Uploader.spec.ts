import { UploaderOptions } from '@/domain/services/Uploader/UploadOptions';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Plupload, Uploader } from '@/domain/services/Uploader/Plupload';
import { UploaderHooks, useUploader } from '@/domain/services/Uploader/Uploader';

const init = vi.fn();
const addFile = vi.fn();
const start = vi.fn();
const bind = vi.fn();
const unbindAll = vi.fn();
const destroy = vi.fn();

vi.mock('@/domain/services/Uploader/Plupload', async () => ({
	Plupload: {
		addFileFilter: vi.fn(),
		Uploader: vi.fn(),
	},
}));

describe('Uploader service', () => {
	beforeEach(() => {
		vi.resetAllMocks();
		vi
			.spyOn(Plupload, 'Uploader')
			.mockReturnValue({ init, addFile, start, bind, unbindAll, destroy } as unknown as Uploader);
	});

	it('binds all hooks', async () => {
		const uploaderHooks = {
			FilesAdded: vi.fn(),
			BeforeUpload: vi.fn(),
			BeforeChunkUpload: vi.fn(),
			UploadProgress: vi.fn(),
			FileUploaded: vi.fn(),
			Error: vi.fn(),
			PostInit: vi.fn(),
		} as UploaderHooks;

		useUploader({} as UploaderOptions, null, uploaderHooks);

		Object.keys(uploaderHooks).forEach((key) => {
			if (uploaderHooks[key as keyof typeof uploaderHooks]) {
				expect(bind).toHaveBeenCalledWith(key, uploaderHooks[key as keyof typeof uploaderHooks]);
			}
		});
	});

	it('uploads a blob', async () => {
		const uploader = useUploader({} as UploaderOptions, null);
		const blob = {} as Blob;
		uploader.upload(blob);
		expect(addFile).toHaveBeenCalledWith(blob);
		expect(start).toHaveBeenCalledOnce();
	});

	it('suicides', async () => {
		const uploader = useUploader({} as UploaderOptions, null);
		uploader.destroy();
		expect(unbindAll).toHaveBeenCalledOnce();
		expect(destroy).toHaveBeenCalledOnce();
	});
});
