import { arrAccessor, createDao, objAccessor } from '@/domain/utils/DataAccessor';
import { describe, expect, it } from 'vitest';

const things: Record<string, string> = {
	'foo.bar1': 'foo-bar-1',
	'foo.bar2': 'foo-bar-2',
	foo1: 'foo1-val',
	foo2: 'foo2-val',
};

const getter = (thing: string) => things[thing];

describe('Accessors factory', () => {
	it('makes an object', () => {
		const result = objAccessor<string>(getter)(['foo.bar1', 'foo.bar2', 'yeti']);
		expect(Object.keys(result)).toStrictEqual(['fooBar1', 'fooBar2', 'yeti']);
		expect(result.fooBar1).toBe('foo-bar-1');
		expect(result.fooBar2).toBe('foo-bar-2');
		expect(result.yeti).toBe(undefined);
	});

	it('makes an array', () => {
		const result = arrAccessor<string>(getter)(['foo.bar1', 'foo.bar2', 'yeti']);
		expect(result).toHaveLength(3);
		expect(result[0]).toBe('foo-bar-1');
		expect(result[1]).toBe('foo-bar-2');
		expect(result[2]).toBe(undefined);
	});

	it('makes a dao', () => {
		const dao = createDao<
			{
				foo: {
					bar1: string;
					bar2: string;
				};
				yeti?: string;
			} & Record<string, string>
		>(things);

		expect(dao.foo.bar1).toBe('foo-bar-1');
		expect(dao.foo.bar2).toBe('foo-bar-2');
		expect(dao.yeti).toBe(undefined);
		expect(dao.foo).toStrictEqual({ bar1: 'foo-bar-1', bar2: 'foo-bar-2' });
		expect(dao['foo.bar2']).toBe('foo-bar-2');
	});

	it('dao decomposition', () => {
		const { foo } = createDao<{
			foo: {
				bar1: string;
				bar2: string;
			};
		}>(things);

		expect(foo.bar1).toBe('foo-bar-1');
		expect(foo.bar2).toBe('foo-bar-2');
	});

	it('flat dao', () => {
		const dao = createDao<
			{
				foo: {
					bar1: string;
					bar2: string;
				};
				yeti?: string;
				foo1: string;
				foo2: string;
			} & Record<string, string>
		>(things, false);

		expect(dao.foo1).toBe('foo1-val');
		expect(dao.foo2).toBe('foo2-val');
		expect(dao['foo.bar1']).toBe('foo-bar-1');
		expect(dao['foo.bar2']).toBe('foo-bar-2');
		expect(dao.yeti).toBe(undefined);
		expect(dao.foo).toBe(undefined);
	});
});
