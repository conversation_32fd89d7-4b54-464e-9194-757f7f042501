import { describe, expect, it } from 'vitest';
import { ignoreRepeatedCalls, isChanged } from '@/domain/utils/Cache';

describe('Cache utils', () => {
	it('detects known value', () => {
		const isItKnown = isChanged({ test1: 1, test2: 2 });

		expect(isItKnown({ test2: 2, test1: 1 })).toBe(true);

		expect(isItKnown('foo')).toBe(false);
		expect(isItKnown('foo')).toBe(true);

		expect(isItKnown({ foo: 'bar' })).toBe(false);
		expect(isItKnown({ foo: 'bar' })).toBe(true);
	});

	it('ignores repeated calls known value', () => {
		const log: number[] = [];
		const callbackWithoutRepetitions = ignoreRepeatedCalls(
			(value: {
				foo: {
					test01: number;
					test02: number;
				};
				bar: string;
			}) => {
				log.push(value.foo.test01);
			}
		);

		callbackWithoutRepetitions({
			foo: {
				test01: 1,
				test02: 2,
			},
			bar: 'bar',
		});

		callbackWithoutRepetitions({
			bar: 'bar',
			foo: {
				test02: 2,
				test01: 1,
			},
		});

		callbackWithoutRepetitions({
			foo: {
				test01: 2,
				test02: 2,
			},
			bar: 'bar',
		});

		callbackWithoutRepetitions({
			bar: 'bar',
			foo: {
				test02: 2,
				test01: 2,
			},
		});

		callbackWithoutRepetitions({
			foo: {
				test01: 3,
				test02: 2,
			},
			bar: 'bar',
		});

		callbackWithoutRepetitions({
			foo: {
				test01: 1,
				test02: 2,
			},
			bar: 'bar',
		});

		expect(log).toEqual([1, 2, 3, 1]);
	});
});
