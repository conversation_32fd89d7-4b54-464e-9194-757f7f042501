import { describe, expect, it } from 'vitest';
import {
	isArray,
	isBoolean,
	isFunction,
	isNull,
	isNullOrUndefined,
	isNumber,
	isObject,
	isPrimitive,
	isString,
	isUndefined,
} from '@/domain/utils/TypePredicates';

const tests: Record<string, unknown> = {
	str: 'foo',
	num: 123,
	bool: true,
	obj: { foo: 'bar', num: 1 },
	arr: [1, 'aaa', {}],
	nul: null,
	fun: () => 123,
};

const testPredicate = (predicate: (v: unknown) => boolean, map: Record<string, boolean>) => {
	[...Object.keys(tests), 'undef'].forEach((key) => {
		expect(predicate(tests[key] as unknown)).toBe(map[key] === true);
	});
};

describe('Type predicates', () => {
	it('detects string', () => {
		testPredicate(isString, { str: true });
	});

	it('detects number', () => {
		testPredicate(isNumber, { num: true });
	});

	it('detects boolean', () => {
		testPredicate(isBoolean, { bool: true });
	});

	it('detects primitive', () => {
		testPredicate(isPrimitive, { str: true, num: true, bool: true });
	});

	it('detects object', () => {
		testPredicate(isObject, { obj: true });
	});

	it('detects array', () => {
		testPredicate(isArray, { arr: true });
	});

	it('detects null', () => {
		testPredicate(isNull, { nul: true });
	});

	it('detects undefined', () => {
		testPredicate(isUndefined, { udef: true });
	});

	it('detects null or undefined', () => {
		testPredicate(isNullOrUndefined, { nul: true, udef: true });
	});

	it('detects function', () => {
		testPredicate(isFunction, { fun: true });
	});
});
