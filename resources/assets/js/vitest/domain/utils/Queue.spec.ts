import { promiseQueue } from '@/domain/utils/Queue';
import { describe, expect, it } from 'vitest';

type Item = {
	id: number;
	failCount: number;
};

describe('Queue utils', () => {
	it('operates promise queue', async () => {
		const promiseLog: {
			id: Item['id'];
			success: boolean;
		}[] = [];

		const executorFactory = (item: Item) =>
			new Promise((resolve, reject) => {
				if (item.failCount === 0) {
					promiseLog.push({ id: item.id, success: true });
					resolve(item.id);
					return;
				}

				item.failCount--;
				promiseLog.push({ id: item.id, success: false });
				const e = new Error('Failed');
				e.retry = item.retry;
				reject(e);
			});

		const queue = promiseQueue(executorFactory);

		queue.push({ id: 1, failCount: 0, retry: true });
		queue.push({ id: 2, failCount: 1, retry: true });
		queue.push({ id: 3, failCount: 2, retry: true });
		queue.push({ id: 4, failCount: 99, retry: false });

		await new Promise((resolve) => setTimeout(resolve, 10));

		expect(promiseLog).toStrictEqual([
			{ id: 1, success: true },
			{ id: 2, success: false },
			{ id: 2, success: true },
			{ id: 3, success: false },
			{ id: 3, success: false },
			{ id: 3, success: true },
			{ id: 4, success: false },
		]);
	});
});
