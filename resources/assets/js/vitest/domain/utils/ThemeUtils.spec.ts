import { describe, expect, it, vi } from 'vitest';
import { getThemeVariableValue, parseThemeVars } from '@/domain/utils/ThemeUtils';

const mockTheme = {
	'theme-var-primary-button': '#ff0000',
};

vi.mock('@/lib/utils', () => ({
	getGlobal: vi.fn(() => 'awardforce'),
}));

vi.mock('@scss/awardforce.module.scss', () => ({
	default: mockTheme,
}));

describe('ThemeUtils', () => {
	it('should retrieve the correct theme variable value', async () => {
		expect(await getThemeVariableValue('theme-var-primary-button')).toEqual('ff0000');
	});

	it('should parse theme vars properly', () => {
		expect(
			parseThemeVars({
				'theme-var-a': '#123',
				'theme-var-b': '#112233',
			})
		).toStrictEqual({
			'theme-var-a': '112233',
			'theme-var-b': '112233',
		});
	});
});
