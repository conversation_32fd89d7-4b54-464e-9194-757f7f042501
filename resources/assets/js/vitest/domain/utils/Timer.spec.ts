import { useTimer } from '@/domain/utils/Timer';
import { afterEach, beforeEach, describe, expect, it, Mock, vi } from 'vitest';

describe('Timer', () => {
	const setTimeoutOrg = global.setTimeout;
	const clearTimeoutOrg = global.clearTimeout;

	beforeEach(() => {
		global.setTimeout = vi.fn((cb: () => boolean) => cb()) as unknown as typeof setTimeout;
		global.clearTimeout = vi.fn() as unknown as typeof clearTimeout;
	});

	afterEach(() => {
		global.setTimeout = setTimeoutOrg;
		global.clearTimeout = clearTimeoutOrg;
	});

	it('should run once', () => {
		const callback = vi.fn(() => false);
		const timer = useTimer(callback);
		timer.start(123);

		expect(global.setTimeout).toHaveBeenCalledWith(expect.any(Function), 123);
		expect(callback).toHaveBeenCalledOnce();
	});

	it('should cycle', () => {
		let count = 10;

		const callback = vi.fn(() => count-- > 0);
		const timer = useTimer(callback);
		timer.start(123);

		expect(global.setTimeout).toHaveBeenCalledWith(expect.any(Function), 123);
		expect(callback).toHaveBeenCalledTimes(11);
	});

	it('should stop if started', () => {
		const timer = useTimer();
		(global.setTimeout as unknown as Mock).mockReturnValue(11);

		timer.start(123);
		timer.stop();

		expect(global.setTimeout).toHaveBeenCalledWith(expect.any(Function), 123);
		expect(global.clearTimeout).toHaveBeenCalledWith(11);
	});

	it('should do nothing on stop if not started', () => {
		const timer = useTimer();
		(global.setTimeout as unknown as Mock).mockReturnValue(11);
		timer.stop();
		expect(global.setTimeout).not.toHaveBeenCalled();
		expect(global.clearTimeout).not.toHaveBeenCalled();
	});
});
