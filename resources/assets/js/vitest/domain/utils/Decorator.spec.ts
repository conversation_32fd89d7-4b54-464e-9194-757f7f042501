import { runUnique } from '@/domain/utils/Decorator';
import { describe, expect, it } from 'vitest';

describe('Decorators utils', () => {
	describe('runUnique utility', () => {
		it('should not run the callback function if the value didnt change', () => {
			const fake = runUnique((val) => val, '1');

			expect(fake(2222)).toBe(2222);
			expect(fake(2222)).toBe(undefined);
		});

		it('should run the callback function if the value changed', () => {
			const fake = runUnique((val) => val, '1');

			expect(fake(2222)).toBe(2222);
			expect(fake(3333)).toBe(3333);
		});
	});
});
