import { assert } from '@/domain/utils/Assertions';
import { describe, expect, it } from 'vitest';

describe('Assertions', () => {
	it('check if object has properties', () => {
		const hasFoo = assert.hasProps(['foo']);
		const hasFooBar = assert.hasProps(['foo-bar']);

		expect(() => hasFoo({ foo: 'bar' })).not.toThrowError();
		expect(() => hasFooBar({ foo: 'bar' })).toThrowError('Missing props: foo-bar');
		expect(() => hasFooBar(123)).toThrowError('Not an object object: 123');
	});

	it('check if array has legal elements', () => {
		expect(() => assert.hasAllowedElements(['foo', 'bar'])(['foo', 'bar'])).not.toThrowError();
		expect(() => assert.hasAllowedElements(['foo'])(['foo', 'bar'])).toThrowError('Illegal values found: bar');
		expect(() => assert.hasAllowedElements(['foo-bar'])(123)).toThrowError('Not an array: 123');
	});

	it('checks if string fits a pattern', () => {
		const isEmail = assert.satisfiesRegexp(/^[^\s@]+@[^\s@]+\.[^\s@]+$/i);

		expect(() => isEmail('<EMAIL>')).not.toThrowError();
		expect(() => isEmail('foo')).toThrowError();
		expect(() => isEmail(123)).toThrowError();
	});
});
