import { Parse } from '@/domain/utils/DataParsers';
import { describe, expect, it } from 'vitest';

const { array, bool, enabled, fileSize, obj, str } = Parse;

describe('Data parsers', () => {
	it('parses boolean', () => {
		expect(bool()('true')).toBe(true);
		expect(bool()(' true')).toBe(true);
		expect(bool()(true)).toBe(true);
		expect(bool()('1 ')).toBe(true);
		expect(bool()(1)).toBe(true);

		expect(bool()('false')).toBe(false);
		expect(bool()(false)).toBe(false);
		expect(bool()('0')).toBe(false);
		expect(bool()('   ')).toBe(false);
		expect(bool()(0)).toBe(false);

		expect(() => bool()('foo')).toThrowError();
	});

	it('parses enabled', () => {
		expect(enabled()('true')).toBe(true);
		expect(enabled()(' true')).toBe(true);
		expect(enabled()(true)).toBe(true);
		expect(enabled()('1 ')).toBe(true);
		expect(enabled()(1)).toBe(true);
		expect(enabled()('enabled')).toBe(true);

		expect(enabled()('false')).toBe(false);
		expect(enabled()(false)).toBe(false);
		expect(enabled()('0')).toBe(false);
		expect(enabled()('   ')).toBe(false);
		expect(enabled()(0)).toBe(false);
		expect(enabled()('disabled')).toBe(false);

		expect(() => enabled()('foo')).toThrowError();
	});

	it('parses fileSize', () => {
		expect(fileSize()('10M')).toStrictEqual({
			raw: '10M',
			size: 10,
			unit: 'Mb',
			bytes: 10 * 1024 * 1024,
		});

		expect(fileSize()('10K')).toStrictEqual({
			raw: '10K',
			size: 10,
			unit: 'Kb',
			bytes: 10 * 1024,
		});

		expect(fileSize()('10G')).toStrictEqual({
			raw: '10G',
			size: 10,
			unit: 'Gb',
			bytes: 10 * 1024 * 1024 * 1024,
		});

		expect(fileSize()('10T')).toStrictEqual({
			raw: '10T',
			size: 10,
			unit: 'Tb',
			bytes: 10 * 1024 * 1024 * 1024 * 1024,
		});

		expect(fileSize()('10')).toStrictEqual({
			raw: '10',
			size: 10,
			unit: 'b',
			bytes: 10,
		});

		expect(fileSize()('10b')).toStrictEqual({
			raw: '10b',
			size: 10,
			unit: 'b',
			bytes: 10,
		});

		expect(fileSize()('10b ')).toStrictEqual({
			raw: '10b',
			size: 10,
			unit: 'b',
			bytes: 10,
		});

		expect(fileSize()('10 b')).toStrictEqual({
			raw: '10 b',
			size: 10,
			unit: 'b',
			bytes: 10,
		});

		expect(fileSize()('10xxx')).toStrictEqual({
			raw: '10xxx',
			size: 10,
			unit: 'b',
			bytes: 10,
		});

		expect(() => fileSize()('foo')).toThrowError();
		expect(() => fileSize((v) => v.bytes < 1024 * 1024)('10M')).toThrowError();
	});

	it('parses array', () => {
		expect(array()('')).toStrictEqual([]);
		expect(array()('[]')).toStrictEqual([]);
		expect(array()('[ ]')).toStrictEqual([]);
		expect(array()('[1,2,3]')).toStrictEqual(['1', '2', '3']);
		expect(array()('1,2,3')).toStrictEqual(['1', '2', '3']);
		expect(array()('["1","2","3"]')).toStrictEqual(['1', '2', '3']);
		expect(array()("['1','2','3']")).toStrictEqual(['1', '2', '3']);

		expect(() => array((arr) => arr.length > 3)("['1','2','3']")).toThrowError();
	});

	it('parses string', () => {
		expect(str()('')).toBe('');
		expect(str()('foo')).toBe('foo');
		expect(str((v) => v.charAt(0) === 'f')('foo')).toBe('foo');
		expect(() => str((v) => v.charAt(0) === 'a')('foo')).toThrowError();
	});

	it('parses object', () => {
		expect(obj()('{}')).toStrictEqual({});
		expect(obj()('{"x":1}')).toStrictEqual({ x: 1 });
		expect(obj()('{"x":"foo"}')).toStrictEqual({ x: 'foo' });
		expect(obj()('{"x":["foo", "bar", 2]}')).toStrictEqual({ x: ['foo', 'bar', 2] });

		expect(() => obj()('abc')).toThrowError();
		expect(() => obj()("{'x':1}")).toThrowError();
		expect(() => obj<{ x: number }>((v) => v.x > 1)('{"x":1}')).toThrowError();
	});
});
