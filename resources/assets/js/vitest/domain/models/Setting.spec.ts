import { DataParser } from '@/domain/utils/DataParsers';
import { SettingParsers } from '@/domain/models/Setting';
import { describe, expect, it } from 'vitest';

const testBooleanParser = (parser: DataParser) => {
	expect(parser(1)).toBe(true);
	expect(parser(0)).toBe(false);
	expect(parser('1')).toBe(true);
	expect(parser('0')).toBe(false);
	expect(parser('true')).toBe(true);
	expect(parser('false')).toBe(false);
	expect(parser('')).toBe(false);
	expect(() => parser('foo')).toThrowError();
	expect(() => parser(null)).toThrowError();
};

describe('Setting model', () => {
	[
		'enable-saml',
		'disable-login-form',
		'app-site-registration-open',
		'enable-mobile-registrations',
		'enable-3rd-party-authentication',
		'mobile-required',
		'email-required',
		'require-agreement-to-terms',
		'require-consent-to-notifications-and-broadcasts',
	].forEach((setting) => {
		it(setting, () => {
			testBooleanParser(SettingParsers[setting]);
		});
	});

	it('app-file-max_size', () => {
		expect(SettingParsers['app-file-max_size']('100M')).toStrictEqual({
			bytes: 104857600,
			raw: '100M',
			size: 100,
			unit: 'Mb',
		});
		expect(() => SettingParsers['app-file-max_size']('foo')).toThrowError();
	});

	it('social-authentication', () => {
		expect(SettingParsers['social-authentication']('facebook,google')).toStrictEqual(['facebook', 'google']);
		expect(() => SettingParsers['social-authentication']('facebook,google,foo')).toThrowError();
	});

	it('payment-identifier', () => {
		expect(SettingParsers['payment-identifier']('foo')).toBe('foo');
	});

	it('elevio', () => {
		testBooleanParser(SettingParsers['elevio']);
		expect(SettingParsers['elevio']('enabled')).toBe(true);
		expect(SettingParsers['elevio']('disabled')).toBe(false);
	});

	it('attachment-allowed-file-types', () => {
		expect(
			SettingParsers['attachment-allowed-file-types']({
				documents: ['pdf', 'doc'],
				images: ['gif', 'jpeg', 'jpg', 'png'],
				audio: ['mp3'],
			})
		).toStrictEqual({
			documents: ['pdf', 'doc'],
			images: ['gif', 'jpeg', 'jpg', 'png'],
			audio: ['mp3'],
		});

		expect(() => SettingParsers['attachment-allowed-file-types']({ foo: 'bar' })).toThrowError();
	});
});
