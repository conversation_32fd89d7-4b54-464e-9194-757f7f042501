import { color, HexColor } from '@/domain/models/Color';
import { describe, expect, it } from 'vitest';

describe('Color model', () => {
	it('validates color value', () => {
		expect(() => color('#abc')).not.toThrowError();
		expect(() => color('abc' as unknown as HexColor)).not.toThrowError();
		expect(() => color('#abx')).toThrowError('Invalid hex color: #abx');
		expect(() => color('ab' as unknown as HexColor)).toThrowError('Invalid hex color: ab');
	});

	it('calculates dark color', () => {
		const c = color('#223344');
		expect(c.contrast).toBe('#ffffff');
		expect(c.rgb).toEqual({ r: 34, g: 51, b: 68 });
		expect(c.value).toBe('#223344');
	});

	it('calculates light color', () => {
		const c = color('#ddeeff');
		expect(c.contrast).toBe('#000000');
		expect(c.rgb).toEqual({ r: 221, g: 238, b: 255 });
		expect(c.value).toBe('#ddeeff');
	});

	it('calculates for short format', () => {
		const c = color('#def');
		expect(c.value).toBe('#ddeeff');
	});
});
