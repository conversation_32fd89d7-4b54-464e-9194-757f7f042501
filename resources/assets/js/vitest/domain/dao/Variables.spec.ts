import { useVariablesDao } from '@/domain/dao/Variables';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		variables: {
			foo: 'foo-val',
			bar: 'bar-val',
			anything: 'anything else',
		},
	},
}));

describe('Variables DAO facade', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('exposes a dao', () => {
		const { foo, bar } = useVariablesDao<{
			foo: string;
			bar: string;
		}>();
		expect(foo).toBe('foo-val');
		expect(bar).toBe('bar-val');
	});
});
