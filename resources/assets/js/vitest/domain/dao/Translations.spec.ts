import { useTranslationsDao } from '@/domain/dao/Translations';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		translations: {
			'en_GB.buttons': {
				approve: 'val-buttons-approve',
				cancel: 'val-buttons-cancel',
			},
			'en_GB.entries': {
				titles: {
					entrant: 'val-entries-titles-entrant',
					qualify: 'val-entries-titles-qualify',
				},
			},
		},
		language: {
			locale: 'en_GB',
			fallback: 'en_GB',
		},
	},
}));

describe('Translations DAO facade', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('exposes a dao', () => {
		const dao = useTranslationsDao<{
			buttons: {
				approve: () => string;
				cancel: () => string;
			};
			entries: {
				titles: Record<string, () => string>;
			};
		}>();

		expect(Object.keys(dao.buttons)).toStrictEqual(['approve', 'cancel']);
		expect(dao.buttons.approve()).toBe('val-buttons-approve');
		expect(dao.buttons.cancel()).toBe('val-buttons-cancel');

		expect(Object.keys(dao.entries.titles)).toStrictEqual(['entrant', 'qualify']);
		expect(dao.entries.titles.entrant()).toBe('val-entries-titles-entrant');
	});
});
