import { useLinksDao } from '@/domain/dao/Links';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		links: {
			foo: 'foo-val',
			bar: 'bar-val',
			anything: 'anything else',
		},
	},
}));

describe('Links DAO facade', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('exposes a dao', () => {
		const dao = useLinksDao<{
			foo: string;
			bar: string;
		}>();
		expect(dao.foo).toBe('foo-val');
		expect(dao.bar).toBe('bar-val');
	});
});
