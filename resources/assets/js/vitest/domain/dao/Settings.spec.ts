import { SettingParsers } from '@/domain/models/Setting';
import { vueData } from '@/domain/services/VueData';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { settingEnabled, settings, useSettingsDao } from '@/domain/dao/Settings';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		settings: {
			array: '1,2,3',
			boolean: '1',
			togglable: 'enabled',
			object: "{ 'foo': true, 'bar': [1,2,3] }",
			fileSize: '100M',
			string: 'foo-bar',
			unknown: 'unknown',
		},
	},
}));

vi.mock('@/domain/models/Setting', () => ({
	SettingParsers: {
		array: (p: string) => `array( ${p} )`,
		boolean: (p: string) => `boolean( ${p} )`,
		togglable: (p: string) => `togglable( ${p} )`,
		object: (p: string) => `object( ${p} )`,
		fileSize: (p: string) => `fileSize( ${p} )`,
		string: (p: string) => `string( ${p} )`,
	},
}));

describe('Settings DAO facade', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('exposes a dao and accessor', () => {
		const dao = useSettingsDao();

		for (const [key, value] of Object.entries(vueData.settings)) {
			if (key !== 'unknown') {
				expect(dao[key as keyof typeof dao] as string).toStrictEqual(`${key}( ${value} )`);
				expect(settings(key as keyof typeof dao)).toStrictEqual(`${key}( ${value} )`);
			} else {
				expect(dao[key as keyof typeof dao]).toStrictEqual('unknown');
				expect(settings(key as keyof typeof dao)).toStrictEqual('unknown');
			}
		}

		expect(dao['error' as keyof typeof dao]).toBe(undefined);
		expect(() => settings('error' as keyof typeof dao)).toThrowError('Setting error not found');
	});

	/**
	 * explicit any is turned obn in this function
	 * to turn off typescript type guards
	 * that would otherwise prevent us from testing
	 * illegally typed parsers
	 */
	/* eslint-disable @typescript-eslint/no-explicit-any */
	it('boolean getter works', () => {
		vi.spyOn(SettingParsers, 'boolean').mockImplementation(() => true);
		expect(settingEnabled('boolean' as any)).toBeTruthy();

		vi.spyOn(SettingParsers, 'boolean').mockImplementation(() => false);
		expect(settingEnabled('boolean' as any)).toBeFalsy();

		vi.spyOn(SettingParsers, 'boolean').mockImplementation(() => 'invalid');
		expect(() => settingEnabled('boolean' as any)).toThrowError('Setting boolean is not boolean');

		expect(() => settingEnabled('error' as any)).toThrowError('Setting error not found');
		expect(settingEnabled('error' as any, true)).toBeTruthy();
		expect(settingEnabled('error' as any, false)).toBeFalsy();
	});
	/* eslint-enable @typescript-eslint/no-explicit-any */
});
