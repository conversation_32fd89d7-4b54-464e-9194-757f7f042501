import { Primitive } from '@/domain/utils/UtilityTypes';
import { useGlobalDataDao } from '@/domain/dao/GlobalData';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		globals: {
			v1: 123,
			v2: {
				foo: 'bar',
				bar: 1,
			},
			v3: [1, 'foo', false],
		},
	},
}));

describe('GlobalData DAO facade', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('exposes a dao', () => {
		const dao = useGlobalDataDao<{
			v1: number;
			v2: {
				foo: string;
				bar: number;
			};
			v3: Primitive[];
			yeti: string;
		}>();
		expect(dao.v1).toBe(123);
		expect(dao.v2).toStrictEqual({ foo: 'bar', bar: 1 });
		expect(dao.v3).toStrictEqual([1, 'foo', false]);
		expect(dao.v2.bar).toBe(1);
		expect(dao.yeti).toBe(undefined);
	});
});
