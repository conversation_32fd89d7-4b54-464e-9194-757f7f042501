import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { route, useRoutesDao } from '@/domain/dao/Routes';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		routes: {
			'forms.edit': 'forms/{form}/edit',
			'forms.redirect': 'forms.redirect',
			'entry.autocomplete': 'entry/autocomplete',
			xxx: 'a/{x}/b/{y}/c',
			yyy: 'a/{x}/b/{y}/c',
			zzz: 'a/{x}/b/{y}/c?foo=bar&foo1=bar1',
		},
	},
}));

describe('Routes DAO facade', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('exposes a dao', () => {
		const dao = useRoutesDao<{
			forms: {
				edit: (p?: { form: number }) => string;
				redirect: () => string;
			};
			entry: {
				autocomplete: (p?: { name: string }) => string;
			};
			xxx: (p?: { x: string; y: string; z: string }) => string;
			yyy: (p?: string[]) => string;
			zzz: (p?: { x: string; y: string; z?: number }) => string;
		}>();

		expect(Object.keys(dao)).toStrictEqual(['forms.edit', 'forms.redirect', 'entry.autocomplete', 'xxx', 'yyy', 'zzz']);
		expect(Object.keys(dao.forms)).toStrictEqual(['edit', 'redirect']);

		expect(dao.forms.edit({ form: 123 })).toBe('/forms/123/edit');
		expect(dao.forms.redirect()).toBe('/forms.redirect');

		expect(dao.entry.autocomplete({ name: 'foo-bar' })).toBe('/entry/autocomplete?name=foo-bar');

		expect(dao.xxx({ x: 'xx', y: 'yy', z: 'zz' })).toBe('/a/xx/b/yy/c?z=zz');
		expect(dao.yyy(['x1', 'y1'])).toBe('/a/x1/b/y1/c');

		expect(dao.zzz({ x: 'x1', y: 'y1' })).toBe('/a/x1/b/y1/c?foo=bar&foo1=bar1');
		expect(dao.zzz({ x: 'x1', y: 'y1', z: 123 })).toBe('/a/x1/b/y1/c?foo=bar&foo1=bar1&z=123');
	});

	it('exposes shortcut function', () => {
		expect(route('forms.edit', { form: 123 })).toBe('/forms/123/edit');
		expect(route('forms.redirect')).toBe('/forms.redirect');
		expect(route('entry.autocomplete', { name: 'foo-bar' })).toBe('/entry/autocomplete?name=foo-bar');
		expect(route('xxx', { x: 'xx', y: 'yy', z: 'zz' })).toBe('/a/xx/b/yy/c?z=zz');
		expect(route('yyy', ['x1', 'y1'])).toBe('/a/x1/b/y1/c');
		expect(route('zzz', { x: 'x1', y: 'y1' })).toBe('/a/x1/b/y1/c?foo=bar&foo1=bar1');
		expect(route('zzz', { x: 'x1', y: 'y1', z: 123 })).toBe('/a/x1/b/y1/c?foo=bar&foo1=bar1&z=123');
	});
});
