import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { featureEnabled, useFeaturesDao } from '@/domain/dao/Features';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		features: {
			api: true,
			saml: false,
		},
	},
}));

describe('Features DAO facade', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('exposes a dao', () => {
		const dao = useFeaturesDao();
		expect(dao.api).toBe(true);
		expect(dao.saml).toBe(false);
		expect(dao.multiform).toBe(undefined);

		// this will rise tsc error
		// expect(dao.foo).toBe(undefined);
	});

	it('checks if feature is enabled', () => {
		expect(featureEnabled('api')).toBe(true);
		expect(featureEnabled('saml')).toBe(false);
		expect(() => featureEnabled('multiform')).toThrowError('Feature multiform is not defined');

		// this will rise tsc error
		// expect(() => featureEnabled('foo')).toThrowError('Feature foo is not defined');
	});
});
