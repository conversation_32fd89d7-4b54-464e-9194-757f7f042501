import { equals } from '@/domain/utils/DataParsers';
import { truthy } from '@/legacy/Equality';
import { describe, expect, it } from 'vitest';

describe('Equality', () => {
	it('checks if values included in list', () => {
		const equality = equals([2, 21, 'aaa', true, null]);

		expect(equality(21)).toBeTruthy();
		expect(equality('aaa')).toBeTruthy();
		expect(equality(true)).toBeTruthy();
		expect(equality(null)).toBeTruthy();

		expect(equality(22)).toBeFalsy();
		expect(equality('21')).toBeFalsy();
		expect(equality('aaa ')).toBeFalsy();
		expect(equality('true')).toBeFalsy();
		expect(equality('null')).toBeFalsy();
		expect(equality(0)).toBeFalsy();
		expect(equality(1)).toBeFalsy();
		expect(equality(false)).toBeFalsy();
	});

	it('checks truthy', () => {
		expect(truthy(1)).toBeTruthy();
		expect(truthy('1')).toBeTruthy();
		expect(truthy(true)).toBeTruthy();
		expect(truthy('true')).toBeTruthy();

		expect(truthy(0)).toBeFalsy();
		expect(truthy(123)).toBeFalsy();
		expect(truthy('01')).toBeFalsy();
		expect(truthy('anything')).toBeFalsy();
		expect(truthy('false')).toBeFalsy();
		expect(truthy('null')).toBeFalsy();
		expect(truthy(0)).toBeFalsy();
		expect(truthy(false)).toBeFalsy();
	});
});
