import { GlobalComponentsResolver } from '../../src/legacy/GlobalComponentsResolver';
import GlobalData from '../_common/GlobalData';
import { routes } from '../../src/routes';
import { VueConstructor } from 'vue';
import { VueModule } from '../../src/domain/services/VueModule';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/routes', () => ({
	routes: {
		testA: vi.fn(),
		testB: vi.fn(),
	},
}));

vi.mock('@/legacy/OldApi', () => ({
	Api: {
		setup: vi.fn(),
	},
}));

vi.mock('@/global-components', () => ({
	default: {
		GlobalComponent1: 'GlobalComponent1',
		GlobalComponent2: 'GlobalComponent2',
		GlobalComponent3: 'GlobalComponent3',
	},
}));

vi.mock('@/domain/services/VueModule', () => ({
	VueModule: {
		create: vi.fn(),
		destroy: vi.fn(),
	},
}));

const vueContext = {
	component: vi.fn(),
	use: vi.fn(),
};

vi.mock('@/domain/services/VueDataProvider', () => ({
	VueDataProvider: {
		bootGlobalData: vi.fn(),
	},
}));

vi.mock('@/plugins/vuex', () => ({
	default: vi.fn(),
}));

describe('GlobalComponentsResolver', () => {
	const globalData = GlobalData();

	beforeEach(() => {
		globalData.window = { App: { foo: 'bar' } };
		globalData.Route = 'test';
		vi.resetAllMocks();

		vi.spyOn(routes, 'testA').mockResolvedValue({
			default: vi.fn(),
		});

		vi.spyOn(routes, 'testB').mockResolvedValue({
			default: vi.fn(),
		});

		GlobalComponentsResolver.boot(vueContext as unknown as VueConstructor);
	});

	afterEach(() => {
		vi.clearAllMocks();
		globalData.reset();
	});

	it('boots global components', async () => {
		await import('@/global-components');
		expect(vueContext.component).toHaveBeenCalledTimes(3);
		expect(vueContext.component).toHaveBeenCalledWith('GlobalComponent1', 'GlobalComponent1');
		expect(vueContext.component).toHaveBeenCalledWith('GlobalComponent2', 'GlobalComponent2');
		expect(vueContext.component).toHaveBeenCalledWith('GlobalComponent3', 'GlobalComponent3');
	});

	it('does not mount global component if they are under inline template', async () => {
		const parent = '<div inline-template></div>';
		const elements = [
			{
				tagName: 'global-component-1',
				outerHTML: '<global-component-1></global-component-1>',
				closest: () => vi.fn(() => parent),
			},
			{
				tagName: 'global-component-2',
				outerHTML: '<global-component-2></global-component-2>',
				closest: () => vi.fn(() => parent),
			},
			{
				tagName: 'global-component-3',
				outerHTML: '<global-component-3></global-component-3>',
				closest: () => null,
			},
		];

		globalData.document = {
			getElementsByTagName: (tagName: string) => elements.filter((el) => el.tagName === tagName),
		};

		await GlobalComponentsResolver.loadGlobalComponentsModules();
		expect(VueModule.create).toHaveBeenCalledTimes(1);
	});
});
