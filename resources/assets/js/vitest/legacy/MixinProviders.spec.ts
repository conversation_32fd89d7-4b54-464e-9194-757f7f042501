import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { applicationLinks, featuresService, lang } from '@/legacy/MixinProviders';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		features: {
			foo: true,
			bar: false,
		},
		links: {
			foo: 'foo',
			bar: 'bar',
		},
		translations: {
			'en_GB.foo': {
				first: 'en-val-foo-first',
				second: 'en-val-foo-second',
			},
			'pl_PL.foo': {
				first: 'pl-val-foo-first',
				second: 'pl-val-foo-second',
			},
		},
		language: {
			locale: 'en_GB',
			fallback: 'en_GB',
		},
	},
}));

describe('Legacy mixin providers', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('featuresService', () => {
		const service = featuresService();
		expect(service.features).toStrictEqual({
			foo: true,
			bar: false,
		});
		expect(service.enabled('foo')).toBe(true);
		expect(service.enabled('bar')).toBe(false);
	});

	it('applicationLinks', () => {
		const service = applicationLinks();
		expect(service.links).toStrictEqual({
			foo: 'foo',
			bar: 'bar',
		});
		expect(service.get('foo')).toBe('foo');
		expect(service.get('bar')).toBe('bar');
	});

	it('lang', () => {
		const service = lang();
		expect(service.get('foo.first')).toBe('en-val-foo-first');
		expect(service.get('foo.second')).toBe('en-val-foo-second');
	});
});
