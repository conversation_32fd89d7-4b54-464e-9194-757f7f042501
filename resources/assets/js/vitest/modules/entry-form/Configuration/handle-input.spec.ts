import { handleInput } from '@/modules/entry-form/Configuration/handle-input';
import { afterEach, describe, expect, it, vi } from 'vitest';

describe('handleInput', () => {
	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should emit the input event with the correct payload', () => {
		const emit = vi.fn();

		const property = 'name';
		const value = '<PERSON>';

		handleInput(emit, property, value);

		expect(emit).toHaveBeenCalledWith('input', {
			name: '<PERSON>',
		});
	});
});
