import toInt from '@/modules/entry-form/Configuration/to-int';
import { describe, expect, it } from 'vitest';

describe('toInt', () => {
	it('should convert a valid number string to an integer', () => {
		const value = '42';
		const result = toInt(value);
		expect(result).toBe(42);
	});

	it('should convert a valid number to an integer', () => {
		const value = 42;
		const result = toInt(value);
		expect(result).toBe(42);
	});

	it('should convert a boolean value to null', () => {
		const value = true;
		const result = toInt(value);
		expect(result).toBe(null);
	});

	it('should convert an object to null', () => {
		const value = { foo: 'bar' };
		const result = toInt(value);
		expect(result).toBe(null);
	});

	it('should convert undefined to null', () => {
		const value = undefined;
		const result = toInt(value);
		expect(result).toBe(null);
	});

	it('should convert null to null', () => {
		const value = null;
		const result = toInt(value);
		expect(result).toBe(null);
	});

	it('should return null for an invalid number string', () => {
		const value = 'abc';
		const result = toInt(value);
		expect(result).toBe(null);
	});
});
