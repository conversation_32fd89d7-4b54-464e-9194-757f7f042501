import { handleInput } from '@/modules/entry-form/Configuration/handle-input';
import { afterEach, describe, expect, it, vi } from 'vitest';
import {
	categoryConfiguratorGeneralController,
	Props,
} from '@/modules/entry-form/Configuration/CategoryConfigurator/CategoryConfiguratorGeneral.controller';

vi.mock('@/lib/store', () => ({
	default: {
		getters: {
			'entryForm/categoriesIndex': [{ id: 1 }, { id: 2 }, { id: 3 }],
		},
	},
}));

vi.mock('@/modules/entry-form/Configuration/handle-input', () => ({
	handleInput: vi.fn(),
}));

const emit = vi.fn();

const props: Props = {
	category: {
		id: 1,
		promoted: false,
		parentId: 0,
		name: 'test',
		slug: 'test',
		translated: {},
	},
	categories: [],
};
describe('categoryConfiguratorGeneralController', () => {
	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should return the parents category filtered from the store if the category props are empty', () => {
		const controller = categoryConfiguratorGeneralController(props, emit);

		expect(controller.parents.value).toEqual([{ id: 2 }, { id: 3 }]);
	});

	it('it should prioritize the parents category obtained from the props, rather than from the store', () => {
		const categories = [
			{
				id: 4,
				name: 'test-4',
				slug: 'test-4',
				promoted: false,
				parentId: 0,
				translated: {},
			},
			{
				id: 5,
				name: 'test-5',
				slug: 'test-5',
				promoted: false,
				parentId: 0,
				translated: {},
			},
		];

		const propsWithCategories: Props = { ...props, categories };

		const controller = categoryConfiguratorGeneralController(propsWithCategories, emit);

		expect(controller.parents.value).toEqual(categories);
	});

	it('should call handleInput with the correct arguments on changeParent', () => {
		const propsWithCategories: Props = {
			...props,
			categories: [
				{
					id: 6,
					name: 'test-6',
					slug: 'test-6',
					promoted: false,
					parentId: 0,
					translated: {},
				},
				{
					id: 7,
					name: 'test-7',
					slug: 'test-7',
					promoted: false,
					parentId: 0,
					translated: {},
				},
			],
		};

		const controller = categoryConfiguratorGeneralController(propsWithCategories, emit);

		const event = { target: { value: 'test-7' } };
		controller.changeParent(event);

		expect(handleInput).toHaveBeenCalledWith(emit, 'parentId', 7);
		expect(controller.selectedCategory.value.parentId).toEqual(7);
	});

	it('should correctly update selectedCategory value with provided translations', () => {
		const controller = categoryConfiguratorGeneralController(props, emit);

		const translations = {
			en_GB: {
				name: 'test',
				description: 'test',
			},
			pt_BR: {
				name: 'test',
				description: 'test',
			},
		};

		controller.onTranslatedInputChange(translations);

		expect(controller.selectedCategory.value.translated).toEqual(translations);
	});

	it('should respect category active setting', () => {
		const propsWithCategories: Props = {
			...props,
			category: {
				id: 6,
				name: 'test-6',
				slug: 'test-6',
				promoted: false,
				parentId: 0,
				translated: {},
			},
		};

		const controller = categoryConfiguratorGeneralController(propsWithCategories, emit);

		expect(controller.categoryIsActive.value).toEqual(true);
	});

	it('should correctly determine if a parent is selected', () => {
		const propsWithSelectedCategory: Props = {
			...props,
			category: {
				...props.category,
				parentId: 1,
			},
		};

		const controller = categoryConfiguratorGeneralController(propsWithSelectedCategory, emit);

		expect(controller.isParentSelected(1)).toEqual(true);
		expect(controller.isParentSelected('1')).toEqual(true);
		expect(controller.isParentSelected(2)).toEqual(false);
		expect(controller.isParentSelected('2')).toEqual(false);
	});

	it('should call handleInput with string arguments on onPromotedChange', () => {
		const controller = categoryConfiguratorGeneralController(props, emit);

		const event = { target: { value: '1' } };
		controller.onPromotedChange(event);

		expect(handleInput).toHaveBeenCalledWith(emit, 'promoted', true);
		expect(controller.selectedCategory.value.promoted).toEqual(true);

		event.target.value = '0';
		controller.onPromotedChange(event);

		expect(handleInput).toHaveBeenCalledWith(emit, 'promoted', false);
		expect(controller.selectedCategory.value.promoted).toEqual(false);

		event.target.value = '';

		controller.onPromotedChange(event);
		expect(handleInput).toHaveBeenCalledWith(emit, 'promoted', null);
	});
});
