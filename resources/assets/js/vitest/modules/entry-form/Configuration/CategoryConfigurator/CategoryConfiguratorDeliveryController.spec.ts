import { afterEach, describe, expect, it, vi } from 'vitest';
import {
	categoryConfiguratorDeliveryController,
	Props,
} from '@/modules/entry-form/Configuration/CategoryConfigurator/CategoryConfiguratorDelivery.controller';
import { handleInput, HandleInputEvent } from '@/modules/entry-form/Configuration/handle-input';
import store, { getOptions } from '@/lib/store';

const props: Props = {
	category: {
		packingSlip: true,
	},
	mergeFields: [],
};

const emit = vi.fn();

vi.mock('@/lib/store', () => ({
	default: {
		state: {
			entryForm: {
				categoryMergeFields: ['field3', 'field4'],
			},
		},
	},
}));

vi.mock('@/lib/utils', () => ({
	getGlobalData: () => ({ locale: 'en_GB', fallback: 'en_GB' }),
}));

vi.mock('@/modules/entry-form/Configuration/handle-input', () => ({
	handleInput: vi.fn(),
}));

describe('categoryConfiguratorDeliveryController', () => {
	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should return the merge fields from the store if props are empty', () => {
		const controller = categoryConfiguratorDeliveryController(props, emit);

		expect(controller.availableMergeFields.value).toEqual(['field3', 'field4']);
	});

	it('should return the merge fields from the props if props are not empty', () => {
		const propsWithMergeFields: Props = { ...props, mergeFields: ['field1', 'field2'] };

		const controller = categoryConfiguratorDeliveryController(propsWithMergeFields, emit);

		expect(controller.availableMergeFields.value).toEqual(['field1', 'field2']);
	});

	it('should call handleInput and set the packing slip value when the packing slip checkbox is clicked', () => {
		const controller = categoryConfiguratorDeliveryController(props, emit);

		const event: HandleInputEvent = {
			target: {
				value: undefined,
				checked: false,
			},
		};

		controller.onPackingSlipChange(event);

		expect(handleInput).toHaveBeenCalledWith(emit, 'packingSlip', false);
		expect(controller.isPackingSlipChecked.value).toEqual(false);

		const event2: HandleInputEvent = {
			target: {
				value: undefined,
				checked: true,
			},
		};
		controller.onPackingSlipChange(event2);

		expect(handleInput).toHaveBeenCalledWith(emit, 'packingSlip', true);
		expect(controller.isPackingSlipChecked.value).toEqual(true);
	});
});
