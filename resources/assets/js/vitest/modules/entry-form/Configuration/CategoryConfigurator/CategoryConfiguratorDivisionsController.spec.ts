import { categoryConfiguratorDivisionsController } from '@/modules/entry-form/Configuration/CategoryConfigurator/CategoryConfiguratorDivisions.controller';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { handleInput, HandleInputEvent } from '@/modules/entry-form/Configuration/handle-input';

vi.mock('@/lib/utils', () => ({
	getGlobalData: () => ({ locale: 'en_GB', fallback: 'en_GB' }),
}));

vi.mock('@/modules/entry-form/Configuration/handle-input', () => ({
	handleInput: vi.fn(),
}));

const props = {
	category: {
		divisions: 2,
		reassign: false,
	},
};
const emit = vi.fn();

describe('categoryConfiguratorDivisionsController', () => {
	afterEach(() => {
		vi.resetAllMocks();
	});

	it('should call handleInput with the correct arguments on setDivisions', () => {
		const controller = categoryConfiguratorDivisionsController(props, emit);

		const event = { target: { value: '3' } };
		controller.setDivisions(event);

		expect(handleInput).toHaveBeenCalledWith(emit, 'divisions', 3);
		expect(controller.divisions.value).toEqual(3);
	});

	it('should set divisions to 1 if setDivisionsOnBlur receives an empty value', () => {
		const controller = categoryConfiguratorDivisionsController(props, emit);

		const event = { target: { value: '' } };
		controller.setDivisionsOnBlur(event);

		expect(handleInput).toHaveBeenCalledWith(emit, 'divisions', 1);
		expect(controller.divisions.value).toEqual(1);
	});

	it('should set divisions to 1 if setDivisionsOnBlur receives a value less than 1', () => {
		const controller = categoryConfiguratorDivisionsController(props, emit);

		const event = { target: { value: '0' } };
		controller.setDivisionsOnBlur(event);

		expect(handleInput).toHaveBeenCalledWith(emit, 'divisions', 1);
		expect(controller.divisions.value).toEqual(1);
	});

	it('should call handleInput with the correct arguments on setReassign', () => {
		const controller = categoryConfiguratorDivisionsController(props, emit);

		const event: HandleInputEvent = { target: { value: undefined, checked: true } };
		controller.setReassign(event);

		expect(handleInput).toHaveBeenCalledWith(emit, 'reassign', true);
		expect(controller.reassign.value).toEqual(true);
	});
});
