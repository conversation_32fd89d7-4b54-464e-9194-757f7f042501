import { handleInput } from '@/modules/entry-form/Configuration/handle-input';
import { afterEach, describe, expect, it, vi } from 'vitest';
import {
	categoryConfiguratorJudgingController,
	Props,
} from '@/modules/entry-form/Configuration/CategoryConfigurator/CategoryConfiguratorJudging.controller';

vi.mock('@/lib/utils', () => ({
	getGlobalData: () => ({ locale: 'en_GB', fallback: 'en_GB' }),
}));

vi.mock('@/modules/entry-form/Configuration/handle-input', () => ({
	handleInput: vi.fn(),
}));

vi.mock('@/lib/store', () => ({
	default: {
		state: {
			entryForm: {
				scoreSets: [
					{ id: 1, name: 'set1' },
					{ id: 2, name: 'set2' },
				],
				attachmentTypes: {
					1: ['type1', 'type2'],
					2: ['type3', 'type4'],
				},
			},
		},
	},
}));

const emit = vi.fn();

describe('categoryConfiguratorJudgingController', () => {
	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should return store values if props are empty', () => {
		const props: Props = {
			scoreSets: [],
			attachmentTypes: null,
		};

		const controller = categoryConfiguratorJudgingController(props, emit);

		expect(controller.scoreSetsComputed.value).toEqual([
			{ id: 1, name: 'set1' },
			{ id: 2, name: 'set2' },
		]);

		expect(controller.attachmentTypesComputed.value).toEqual({
			1: ['type1', 'type2'],
			2: ['type3', 'type4'],
		});
	});

	it('should return props values if they are not empty', () => {
		const props: Props = {
			scoreSets: [
				{ id: 3, name: 'set3' },
				{ id: 4, name: 'set4' },
			],
			attachmentTypes: {
				3: ['type3', 'type4'],
				4: ['type5', 'type6'],
			},
		};

		const controller = categoryConfiguratorJudgingController(props, emit);

		expect(controller.scoreSetsComputed.value).toEqual([
			{ id: 3, name: 'set3' },
			{ id: 4, name: 'set4' },
		]);
		expect(controller.attachmentTypesComputed.value).toEqual({
			3: ['type3', 'type4'],
			4: ['type5', 'type6'],
		});
	});

	it('should call handleInput with correct arguments', () => {
		const props: Props = {
			scoreSets: [
				{ id: 5, name: 'set5' },
				{ id: 6, name: 'set6' },
			],
			attachmentTypes: {
				5: ['type5', 'type6'],
				6: ['type6', 'type7'],
			},
		};

		const controller = categoryConfiguratorJudgingController(props, emit);

		controller.setAttachmentTypes({ 7: ['type7', 'type8'] });

		expect(handleInput).toHaveBeenCalledWith(emit, 'attachmentTypes', { 7: ['type7', 'type8'] });
	});
});
