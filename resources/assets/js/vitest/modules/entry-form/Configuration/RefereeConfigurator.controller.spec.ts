import store from '@/lib/store';
import { afterEach, beforeEach, vi } from 'vitest';
import { refereeConfiguratorController, View } from '@/modules/entry-form/Configuration/RefereeConfigurator.controller';

vi.mock('@/lib/store', () => ({
	default: {
		getters: {
			'entryForm/refereeFields': () => [],
		},
		state: {
			entryFormConfiguration: {
				configurationMode: true,
			},
			entryForm: {
				refereeReviewStages: [],
			},
		},
	},
}));

vi.mock('@/domain/services/Collaboration/Collaborators', () => ({
	collaboratorsService: () => () => ({
		myAccess: vi.fn(),
	}),
}));

const props = { tab: { id: 1 } };

describe('Referee Configurator Controller', () => {
	let refereeFieldsMock;

	beforeEach(() => {
		vi.resetAllMocks();
		refereeFieldsMock = vi.spyOn(store.getters, 'entryForm/refereeFields');
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('hasFields returns false when there are not referee fields', () => {
		const view = refereeConfiguratorController(props) as View;

		expect(view.hasFields).toBe(false);
	});

	it('hasFields returns true when there are referee fields', () => {
		refereeFieldsMock.mockImplementation(() => [{ field1: 'value1' }]);
		const view = refereeConfiguratorController(props) as View;

		expect(view.hasFields).toBe(true);
	});

	it('returns the referee fields', () => {
		refereeFieldsMock.mockImplementation(() => [
			{ id: 1, name: 'fieldName' },
			{ id: 2, name: 'fieldName2' },
		]);

		const view = refereeConfiguratorController(props) as View;

		expect(view.refereeFields.value.length).toBe(2);
		expect(view.refereeFields.value[0].name).toBe('fieldName');
	});
});
