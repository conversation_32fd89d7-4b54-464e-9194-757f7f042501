import { beforeEach, vi } from 'vitest';
import {
	tabConfiguratorRefereesController,
	View,
} from '@/modules/entry-form/Configuration/TabConfigurator/TabConfiguratorReferees.controller';

vi.mock('@/lib/store', () => ({
	default: {
		getters: {
			'entryForm/refereeFields': () => [],
		},
		state: {
			entryFormConfiguration: {
				configurationMode: true,
			},
			entryForm: {
				refereeReviewStages: [
					{ id: 1, name: 'stage1' },
					{ id: 2, name: 'stage2' },
				],
			},
		},
	},
}));

vi.mock('@/domain/services/Collaboration/Collaborators', () => ({
	collaboratorsService: () => () => ({
		myAccess: vi.fn(),
	}),
}));

describe('TabConfiguratorRefereesController', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	it('retrieves the referee review stages', () => {
		const view = tabConfiguratorRefereesController() as View;

		expect(view.reviewStages.length).toBe(2);
	});
});
