import { collectionServiceFactory } from '@/domain/services/Collaboration/Document';
import { RefereeProps } from '@/modules/entry-form/RefereeTypes';
import { refereeService } from '@/modules/entry-form/RefereeService';
import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';
import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';
import { useFieldService } from '@/modules/entry-form/Collaboration/services/Field.service';
import { beforeEach, expect, Mock, vi } from 'vitest';

vi.stubGlobal('window', {
	addEventListener: vi.fn(),
});

vi.mock('underscore', () => ({
	underscore: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
	getGlobal: vi.fn(),
	getGlobalData: vi.fn(),
}));

vi.mock('@/modules/entry-form/Collaboration/services/CollaborativeSubmittable', () => ({
	useCollaborativeSubmittable: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Document', () => ({
	documentServiceFactory: vi.fn(),
	nullDocumentService: vi.fn(),
	collectionServiceFactory: vi.fn(),
}));

vi.mock('@/domain/services/Rt/Rtdb', () => ({
	getDataSource: vi.fn(),
}));

vi.mock('@/modules/entry-form/EntryFormProvider', () => ({
	useEntryFormContainer: vi.fn(),
}));

vi.mock('@/modules/entry-form/Collaboration/services/Field.service', () => ({
	useFieldService: vi.fn(),
}));

let fakeErrorBag = [];

const props: RefereeProps = {
	referee: {
		id: 1,
		email: '',
		name: '',
	},
};

const baseReferee = { id: 1, email: '', name: '' };

describe('Referee Service', () => {
	// eslint-disable-next-line @typescript-eslint/naming-convention
	const Submittable = {
		fieldHasError: () => false,
		isCollaborative: () => false,
		isGrantReport: () => false,
		getSubmittable: () => ({ slug: 'submittable-slug' }),
		getType: () => 'entrant',
		getForm: () => ({ slug: 'form-slug' }),
		isUpdatable: () => true,
		getFormType: () => 'form-type',
		updateRefereeField: vi.fn(),
		removeFromErrorBag: (error) => {
			fakeErrorBag = fakeErrorBag.filter((obj) => obj.fieldSlug !== error.fieldSlug);
		},
		addToErrorBag: (error) => {
			fakeErrorBag.push(error);
		},
		autoSave: vi.fn(),
	};

	const values = {
		set: vi.fn(),
		subscribe: vi.fn(),
		destroy: vi.fn(),
	};

	const documentFactory = vi.fn();

	beforeEach(() => {
		vi.clearAllMocks();
		(useEntryFormContainer as Mock).mockReturnValue({
			Submittable,
			Collaborators: vi.fn().mockReturnValue({}),
			Consumer: {
				consumer: 'consumer-data',
			},
		});

		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: false,
			api: {
				createReferee: 'createReferee',
			},
		});

		(useFieldService as Mock).mockReturnValue({
			values,
		});

		(collectionServiceFactory as Mock).mockReturnValue(documentFactory);

		fakeErrorBag = [];
	});

	it('validates empty name and email should add error to bag', () => {
		const service = refereeService(props);

		service.validate({ ...baseReferee, id: 'emailValidation' }, 'email');
		service.validate({ ...baseReferee, id: 'nameValidation' }, 'name');

		expect(fakeErrorBag.length).toBe(2);
		expect(fakeErrorBag.find((obj) => obj.resourceId === 'emailValidation')).to.exist;
		expect(fakeErrorBag.find((obj) => obj.resourceId === 'nameValidation')).to.exist;
	});

	it('validates email field should add error to bag when it is not an email ', () => {
		const service = refereeService(props);
		service.validate({ ...baseReferee, email: 'fake@email' }, 'email');

		expect(fakeErrorBag.length).toBe(1);
		expect(fakeErrorBag.find((obj) => obj.fieldSlug === 'email')).to.exist;
	});

	it('validates email field should remove error from bag when it is an email ', () => {
		const service = refereeService(props);

		service.validate({ ...baseReferee, email: 'invalidemail' }, 'email');
		expect(fakeErrorBag.length).toBe(1);
		expect(fakeErrorBag.find((obj) => obj.fieldSlug === 'email')).to.exist;

		service.validate({ ...baseReferee, email: '<EMAIL>' }, 'email');

		expect(fakeErrorBag.length).toBe(0);
		expect(fakeErrorBag.find((obj) => obj.fieldSlug === 'email')).to.not.exist;
	});

	it('validates name field should remove error from bag when it is not empty', () => {
		const service = refereeService(props);
		service.validate({ ...baseReferee, name: '' }, 'name');

		expect(fakeErrorBag.length).toBe(1);
		expect(fakeErrorBag.find((obj) => obj.fieldSlug === 'name')).to.exist;

		service.validate({ ...baseReferee, name: 'fake name' }, 'name');

		expect(fakeErrorBag.length).toBe(0);
		expect(fakeErrorBag.find((obj) => obj.fieldSlug === 'name')).to.not.exist;
	});

	it('should call fieldService.values.set when onInput was called', () => {
		const service = refereeService(props);
		service.onInput('foo', { ...baseReferee }, 'bar-2');

		expect(values.set).toHaveBeenCalledWith(baseReferee, true);
	});

	it('should call updateRefereeField when onInput was called', () => {
		const service = refereeService(props);
		service.onInput('foo', { ...baseReferee }, 'bar-2');

		expect(Submittable.updateRefereeField).toHaveBeenCalledWith({ referee: baseReferee, field: 'foo', value: 'bar-2' });
	});

	it('should call the autosave if the submittable is not collaborative', () => {
		Submittable.isCollaborative = () => false;

		(useEntryFormContainer as Mock).mockReturnValue({
			Submittable,
			Collaborators: vi.fn().mockReturnValue({}),
			Consumer: {
				consumer: 'consumer-data',
			},
		});

		const service = refereeService(props);
		service.autosave();

		expect(Submittable.autoSave).toHaveBeenCalled();
	});

	it('should not call the autosave if the submittable is collaborative', () => {
		Submittable.isCollaborative = () => true;

		(useEntryFormContainer as Mock).mockReturnValue({
			Submittable,
			Collaborators: vi.fn().mockReturnValue({}),
			Consumer: {
				consumer: 'consumer-data',
			},
		});

		const service = refereeService(props);
		service.autosave();

		expect(Submittable.autoSave).not.toHaveBeenCalled();
	});
});
