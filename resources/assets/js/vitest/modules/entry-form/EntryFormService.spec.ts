import * as featureExports from '@/domain/dao/Features';
import { Entry } from '@/domain/models/Entry';
import { entryFormService } from '@/modules/entry-form/EntryForm.service';
import { EntryFormStore } from '@/lib/store/modules/entry-form';
import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { collaborationUIBus, CollaborationUISignals } from '@/domain/signals/Collaboration';
import { submittableFormBus, SubmittableFormSignal } from '@/modules/entry-form/Signals';

vi.mock('@/domain/services/Rt/DataSource', () => ({
	getDataSource: () => ({
		subscribe: vi.fn(),
		set: vi.fn(),
	}),
}));

vi.mock('@/modules/entry-form/EntryFormProvider', () => ({
	useEntryFormContainer: vi.fn(),
}));

describe('EntryForm service', () => {
	beforeEach(() => {
		vi.resetAllMocks();
		(useEntryFormContainer as Mock).mockReturnValue({
			Consumer: {
				consumer: { slug: 'consumer-slug' },
			},
		});
	});

	it('gets existing entry', () => {
		const service = entryFormService({
			state: {
				entryForm: {
					entry: { slug: 'foo-bar' },
					form: { slug: 'foo-bar-slug' },
					isManager: true,
				},
			},
			getters: {
				'entryForm/formType': 'manager',
			},
		} as unknown as EntryFormStore);

		expect(service.getSubmittable().slug).toBe('foo-bar');
		expect(service.getForm().slug).toBe('foo-bar-slug');
		expect(service.getFormType()).toBe('manager');
		expect(service.isSaved()).toBeTruthy();
		expect(service.getType()).toBe('manager');
	});

	it('gets unsaved status', () => {
		const service = entryFormService({
			state: {
				entryForm: {
					entry: null as unknown as Entry,
				},
			},
		} as unknown as EntryFormStore);

		expect(service.isSaved()).toBeFalsy();
	});

	it('checks if form is colaborative and feature is on', () => {
		const makeService = (collaborative: boolean) =>
			entryFormService({
				state: {
					entryForm: {
						entry: { slug: 'foo-bar' },
						form: {
							slug: 'foo-bar-slug',
							settings: {
								collaborative,
							},
						},
					},
				},
			} as unknown as EntryFormStore);

		vi.spyOn(featureExports, 'featureEnabled').mockReturnValue(false);
		expect(makeService(true).isCollaborative()).toBeFalsy();
		expect(makeService(false).isCollaborative()).toBeFalsy();

		vi.spyOn(featureExports, 'featureEnabled').mockReturnValue(true);
		expect(makeService(true).isCollaborative()).toBeTruthy();
		expect(makeService(false).isCollaborative()).toBeFalsy();
	});

	it('checks supports real time updates with different form and feature settings', () => {
		const makeService = (collaborative: boolean, allowApiUpdates: boolean) => {
			vi.spyOn(featureExports, 'featureEnabled').mockImplementation((feature) => {
				if (feature === 'collaboration') return collaborative;
				if (feature === 'api_submission_updates') return allowApiUpdates;
				return false;
			});

			return entryFormService({
				state: {
					entryForm: {
						entry: { slug: 'foo-bar' },
						form: {
							slug: 'foo-bar-slug',
							settings: {
								collaborative,
								allowApiUpdates,
							},
						},
					},
				},
			} as unknown as EntryFormStore);
		};

		expect(makeService(false, false).supportsRealTimeUpdates()).toBeFalsy();
		expect(makeService(true, false).supportsRealTimeUpdates()).toBeTruthy();
		expect(makeService(false, true).supportsRealTimeUpdates()).toBeTruthy();
		expect(makeService(true, true).supportsRealTimeUpdates()).toBeTruthy();
	});

	it('toggles colaboration', () => {
		const service = entryFormService({
			state: {
				entryForm: {
					entry: { slug: 'foo-bar' },
					form: {
						slug: 'foo-bar-slug',
						settings: {
							collaborative: true,
						},
					},
				},
			},
		} as unknown as EntryFormStore);

		vi.spyOn(featureExports, 'featureEnabled').mockReturnValue(true);
		expect(service.isCollaborative()).toBeTruthy();

		vi.resetAllMocks();
		service.toggleCollaborative(false);
		vi.spyOn(featureExports, 'featureEnabled').mockReturnValue(true);
		expect(service.isCollaborative()).toBeFalsy();
	});

	it('handles submission event', () => {
		const onSignal = vi.spyOn(submittableFormBus, 'on');

		let callback = () => {};

		onSignal.mockImplementation((signal, cb) => {
			callback = cb;
		});

		const commit = vi.fn();
		entryFormService({ commit } as unknown as EntryFormStore);

		expect(onSignal).toHaveBeenCalledWith(SubmittableFormSignal.SUBMITTED, expect.any(Function));

		// eslint-disable-next-line
		callback({ submittedUserSlug: 'another-slug' });
		expect(commit).toHaveBeenCalledWith('entryForm/setReadOnly', true);

		// eslint-disable-next-line
		callback({ submittedUserSlug: 'consumer-slug' });
		expect(commit).toHaveBeenCalledWith('entryForm/setReadOnly', false);
	});

	it('should call removeErrorFromBag mutation', () => {
		const commit = vi.fn();
		const service = entryFormService({ commit } as unknown as EntryFormStore);
		service.removeFromErrorBag({ type: 'fake-error' });

		expect(commit).toHaveBeenCalledWith('entryFormApi/removeFromErrorBag', { type: 'fake-error' });
	});

	it('should call addToErrorBag mutation', () => {
		const commit = vi.fn();
		const service = entryFormService({ commit } as unknown as EntryFormStore);
		service.addToErrorBag({ type: 'fake-errors' });

		expect(commit).toHaveBeenCalledWith('entryFormApi/addToErrorBag', { type: 'fake-errors' });
	});

	it('should call updateRefereeField mutation', () => {
		const commit = vi.fn();
		const service = entryFormService({ commit } as unknown as EntryFormStore);
		service.updateRefereeField({ value: 'fake-value' });

		expect(commit).toHaveBeenCalledWith('entryForm/updateRefereeField', { value: 'fake-value' });
	});

	it('should call updateReferee mutation', () => {
		const commit = vi.fn();
		const service = entryFormService({ commit } as unknown as EntryFormStore);
		service.updateReferee({ id: '000' });

		expect(commit).toHaveBeenCalledWith('entryForm/updateReferee', { id: '000' });
	});

	it('should update the entry timestamp when UPDATED_FIELD signal is emitted', () => {
		const onSignal = vi.spyOn(collaborationUIBus, 'on');

		let callback = (payload) => {
			// eslint-disable-next-line chai-friendly/no-unused-expressions
			payload;
		};

		onSignal.mockImplementation((signal, cb) => {
			callback = cb;
		});

		const commit = vi.fn();
		entryFormService({ commit } as unknown as EntryFormStore);

		expect(onSignal).toHaveBeenCalledWith(CollaborationUISignals.UPDATED_FIELD, expect.any(Function));

		// eslint-disable-next-line
		callback({ updatedAt: '2025-01-01 11:11:11' });

		expect(commit).toHaveBeenCalledWith('entryForm/setTimestamp', '2025-01-01 11:11:11');
	});
});
