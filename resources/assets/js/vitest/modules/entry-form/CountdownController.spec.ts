import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { countdownControllerFactory, Props, View } from '@/modules/entry-form/Countdown.controller';

vi.mock('@/lib/utils', () => ({
	getGlobalData: () => ({ locale: 'en_GB', fallback: 'en_GB' }),
}));

const dependenciesMock = {
	onMountedHook: vi.fn(),
};

const executeEveryMinute = (func: () => void) => setInterval(func, 1000 * 60);

const countdownController = (props: Props): View => countdownControllerFactory(dependenciesMock)(props);

describe('Countdown Controller', () => {
	beforeEach(() => {
		vi.useFakeTimers();
	});

	afterEach(() => {
		vi.clearAllMocks();
		vi.useRealTimers();
	});

	it('calculates countdown values', () => {
		const props: Props = {
			date: '2500-01-01 18:30',
			title: 'Test Countdown',
			timezone: 'Europe/London',
		};

		const controller: View = countdownController(props);

		const now = controller.now.value;
		const minutes = controller.minutes.value;
		const hours = controller.hours.value;
		const days = controller.days.value;

		expect(controller.seconds.value).toBeGreaterThanOrEqual(0);
		expect(controller.minutes.value).toBeGreaterThanOrEqual(0);
		expect(controller.hours.value).toBeGreaterThanOrEqual(0);
		expect(controller.days.value).toBeGreaterThan(0);
		expect(dependenciesMock.onMountedHook).toHaveBeenCalledOnce();

		executeEveryMinute(controller.updateNow);

		vi.advanceTimersByTime(1000 * 60); // 1 minute
		expect(controller.now.value.diff(now, 'seconds')).toBe(60);
		expect(controller.minutes.value).not.toBe(minutes);

		vi.advanceTimersByTime(1000 * 60 * 60); // 1 hour
		expect(controller.hours.value).not.toBe(hours);

		vi.advanceTimersByTime(1000 * 60 * 60 * 24); // 1 day
		expect(controller.days.value).not.toBe(days);
	});
});
