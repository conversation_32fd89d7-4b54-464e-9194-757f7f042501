import { Collaborator } from '@/domain/models/Collaborator';
import { emptyCtx } from '../../_common/helpers';
import { getFirebase } from '@/domain/services/Rt/Firebase';
import { PostingEndpoint } from '@/domain/services/Api/Endpoint';
import { RefereesEvents } from '@/lib/components/Collaboration/Referees.events';
import { SetupContext } from 'vue';
import { transparentEncrypter } from '@/domain/services/Encrypter';
import { useCollaborationRefereesService } from '@/modules/entry-form/Collaboration/services/CollaborationReferees.service';
import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';
import { useFieldService } from '@/modules/entry-form/Collaboration/services/Field.service';
import { beforeEach, expect, Mock, vi } from 'vitest';
import { refereeController, View } from '@/modules/entry-form/Referee.controller';

vi.stubGlobal('window', {
	addEventListener: vi.fn(),
});

vi.mock('@/lib/utils', () => ({
	getGlobal: vi.fn(),
	getGlobalData: vi.fn(),
}));

vi.mock('@/modules/entry-form/Configuration/handle-input', () => ({
	handleInput: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Collaborators', () => ({
	collaboratorsService: () => () => ({
		myAccess: vi.fn(),
	}),
}));

vi.mock('@/lib/store', () => ({
	default: {
		state: {
			entryFormApi: {
				errorBag: [{ fieldSlug: 'field', resourceId: 1 }],
			},
			entryFormConfiguration: {
				configurationMode: false,
			},
			entryForm: {
				refereeReviewStages: [],
			},
		},
		getters: {
			'entryForm/entrySlug': 'entry-slug',
			'entryForm/formType': 'form-type',
			'entryFormApi/fieldErrors': [],
		},
		dispatch: vi.fn(),
		commit: vi.fn(),
	},
}));

vi.mock('@/domain/services/Api/Endpoint', () => {
	const actual = vi.importActual('@/domain/services/Api/Endpoint');
	return {
		...actual,
		PostingEndpoint: vi
			.fn()
			.mockImplementation((url: string) => () => (data) => Promise.resolve({ url: url, data: data })),
	};
});

vi.mock('@/modules/entry-form/EntryFormProvider', () => ({
	useEntryFormContainer: vi.fn(),
}));

vi.mock('@/domain/services/Rt/Firebase', () => ({
	bootFirebase: vi.fn(),
	getFirebase: vi.fn(),
}));

vi.mock('firebase/firestore', () => ({
	doc: vi.fn(),
	getDoc: vi.fn(),
	onSnapshot: vi.fn(),
	runTransaction: vi.fn(),
	setDoc: vi.fn(),
	Timestamp: {
		fromDate: vi.fn(),
	},
}));

vi.mock('@/modules/entry-form/Collaboration/services/Field.service', () => ({
	useFieldService: vi.fn(),
}));

vi.mock('@/modules/entry-form/Collaboration/services/CollaborationReferees.service', () => ({
	useCollaborationRefereesService: vi.fn(),
}));

const updateReferee = vi.fn();

const autoSave = vi.fn();

const emit = vi.fn();

describe('Referee Controller', () => {
	const collaboratorsInstance = {
		getAll: vi.fn<Collaborator[]>(),
		onUpdate: vi.fn(),
		getMyself: vi.fn<Collaborator>(),
	};

	// eslint-disable-next-line @typescript-eslint/naming-convention
	const Collaborators = () => collaboratorsInstance;

	// eslint-disable-next-line @typescript-eslint/naming-convention
	const Submittable = {
		fieldHasError: () => true,
		isCollaborative: () => false,
		isGrantReport: () => false,
		getSubmittable: () => ({ slug: 'submittable-slug' }),
		getType: () => 'entrant',
		getForm: () => ({ slug: 'form-slug' }),
		isUpdatable: () => true,
		getFormType: () => 'form-type',
		autoSave,
		updateRefereeField: vi.fn(),
		removeFromErrorBag: vi.fn(),
		addToErrorBag: vi.fn(),
		ajaxInProgress: vi.fn(),
	};
	// eslint-disable-next-line @typescript-eslint/naming-convention
	const Consumer = {
		consumer: 'consumer-data',
	};

	beforeEach(() => {
		vi.clearAllMocks(); // Resets the state of all mocks before each test
		(useEntryFormContainer as Mock).mockReturnValue({ Collaborators, Submittable, Consumer });
		(getFirebase as Mock).mockImplementation(() => ({
			getFirestore: vi.fn(() => 'db'),
			getEncrypter: vi.fn(transparentEncrypter),
			isReadonly: vi.fn(() => false),
		}));

		(useFieldService as Mock).mockReturnValue({
			locks: {
				set: vi.fn(),
				subscribe: vi.fn(),
			},
			values: { subscribe: vi.fn() },
			myAccess: () => Promise.resolve({ canEdit: true }),
		});

		(useCollaborationRefereesService as Mock).mockReturnValue({
			service: vi.fn(),
			api: {
				updateReferee: updateReferee,
			},
		});
	});

	it('canResendRequest should return false if request has been completed', () => {
		const props = { referee: { requestSent: true, requestCompleted: true } };
		const view = refereeController(props, emptyCtx) as View;

		expect(view.canResendRequest.value).toBe(false);
	});

	it('canSendRequest should return true if request has not been sent', () => {
		const props = { referee: { requestSent: false, requestCompleted: false } };
		const view = refereeController(props, emptyCtx) as View;

		expect(view.canSendRequest.value).toBe(true);
	});

	it('canSendRequest should return false if request has been sent', () => {
		const props = { referee: { requestSent: true, requestCompleted: false } };
		const view = refereeController(props, emptyCtx);

		expect(view.canSendRequest.value).toBe(false);
	});

	it('canResendRequest should return true if request has been sent and was not completed', () => {
		const props = { referee: { requestSent: true, requestCompleted: false } };
		const view = refereeController(props, emptyCtx);

		expect(view.canResendRequest.value).toBe(true);
	});

	it('returns true when referee request is completed', () => {
		const props = { referee: { requestSent: true, requestCompleted: true } };
		const view = refereeController(props, emptyCtx);

		expect(view.completed.value).toBe(true);
	});

	it('returns true when referee request is sent', () => {
		const propsTrue = { referee: { requestSent: true, requestCompleted: false } };
		const propsFalse = { referee: { requestSent: false, requestCompleted: false } };
		const viewTrue = refereeController(propsTrue, emptyCtx);
		const viewFalse = refereeController(propsFalse, emptyCtx);

		expect(viewTrue.requestSent.value).toBe(true);
		expect(viewFalse.requestSent.value).toBe(false);
	});

	it('returns true when field has error', () => {
		const props = { referee: { requestSent: true, requestCompleted: true, id: 1 } };
		const view = refereeController(props, emptyCtx) as View;

		expect(view.hasError('field')).toBe(true);
	});

	it('should send request to the correct formType', async () => {
		Submittable.fieldHasError = () => false;
		(useEntryFormContainer as Mock).mockReturnValue({ Collaborators, Submittable, Consumer });

		const props = { referee: { requestSent: true, requestCompleted: true, id: 1, reviewTask: 'fakeReviewTask' } };

		const view = refereeController(props, { emit } as unknown as SetupContext) as View;

		await view.initiateReviewStage();
		expect(PostingEndpoint).toHaveBeenCalledWith('/entry-form/form-type/referee/review-stage/submittable-slug');
		vi.clearAllMocks();
		view.resend();
		expect(PostingEndpoint).toHaveBeenCalledWith('/entry-form/form-type/referee/resend/review-task/fakeReviewTask');
	});

	it('should emit RefereesEvents.InitiatedReviewStage when initiateReviewStage was called', async () => {
		const referee = { requestSent: true, requestCompleted: true, id: 1, reviewTask: 'fakeReviewTask' };
		const props = { referee };

		const view = refereeController(props, { emit } as unknown as SetupContext) as View;

		await view.initiateReviewStage();

		expect(emit).toHaveBeenCalledWith(RefereesEvents.InitiatedReviewStage, referee);
	});

	it('should emit RefereesEvents.DeleteReferee when deleteReferee was called', async () => {
		const referee = { requestSent: true, requestCompleted: true, id: 1, reviewTask: 'fakeReviewTask' };
		const props = { referee };

		const view = refereeController(props, { emit } as unknown as SetupContext) as View;

		await view.deleteReferee(referee);

		expect(emit).toHaveBeenCalledWith(RefereesEvents.DeleteReferee, referee);
	});

	test('disabledOrCollaborationLocked should true if has no privilege to edit', async () => {
		const referee = { requestSent: false };
		const props = { referee, eligibleReadonly: false };

		const view = refereeController(props, { emit } as unknown as SetupContext) as View;
		view.access.value = { canEdit: false };

		expect(view.disabledOrCollaborationLocked.value).to.equal(true);
	});

	test('disabledOrCollaborationLocked should true if the request is already sent', async () => {
		const referee = { requestSent: true };
		const props = { referee, eligibleReadonly: false };

		const view = refereeController(props, { emit } as unknown as SetupContext) as View;

		expect(view.disabledOrCollaborationLocked.value).to.equal(true);
	});

	test('disabledOrCollaborationLocked should true if the eligibleReadonly is true', async () => {
		const referee = { requestSent: false };
		const props = { referee, eligibleReadonly: true };

		const view = refereeController(props, { emit } as unknown as SetupContext) as View;

		expect(view.disabledOrCollaborationLocked.value).to.equal(true);
	});

	test('disabledRequestButton should true if disabledOrCollaborationLocked is true', async () => {
		const referee = { requestSent: true };
		const props = { referee, eligibleReadonly: false };

		const view = refereeController(props, { emit } as unknown as SetupContext) as View;

		expect(view.disabledRequestButton.value).to.equal(true);
	});

	test('disabledResendRequestButton should be false if requestCompleted is false', async () => {
		const referee = { requestSent: true, requestCompleted: false };
		const props = { referee };

		const view = refereeController(props, { emit } as unknown as SetupContext) as View;

		expect(view.disabledResendRequestButton.value).to.equal(false);
	});

	test('disabledRequestButton should be true if the requestCompleted is true', async () => {
		const referee = { requestSent: true, requestCompleted: true };
		const props = { referee };

		const view = refereeController(props, { emit } as unknown as SetupContext) as View;

		expect(view.disabledResendRequestButton.value).to.equal(true);
	});

	it('should call updateReferee if the form is collaborative', async () => {
		(useEntryFormContainer as Mock).mockReturnValue({
			Consumer,
			Collaborators,
			Submittable: {
				...Submittable,
				isCollaborative: () => true,
			},
		});

		const referee = { requestSent: true, requestCompleted: true, id: 1, reviewTask: 'fakeReviewTask' };
		const props = { referee };

		const view = refereeController(props, { emit } as unknown as SetupContext) as View;

		await view.initiateReviewStage();

		expect(autoSave).not.toHaveBeenCalled();
		expect(updateReferee).toHaveBeenCalledWith(referee);
	});

	it('should call autoSave if the form is not collaborative', async () => {
		(useEntryFormContainer as Mock).mockReturnValue({
			Consumer,
			Collaborators,
			Submittable: {
				...Submittable,
				isCollaborative: () => false,
			},
		});

		const referee = { requestSent: true, requestCompleted: true, id: 1, reviewTask: 'fakeReviewTask' };
		const props = { referee };

		const view = refereeController(props, { emit } as unknown as SetupContext) as View;

		await view.initiateReviewStage();

		expect(autoSave).toHaveBeenCalled();
		expect(updateReferee).not.toHaveBeenCalledWith(referee);
	});
});
