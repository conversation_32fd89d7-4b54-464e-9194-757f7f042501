import * as eventBus from '@/domain/services/LocalEventBus';
import { CollaborationUISignals } from '@/domain/signals/Collaboration';
import { Collaborator } from '@/domain/models/Collaborator';
import { emptyCtx } from '@/../vitest/_common/helpers';
import { Form } from '@/domain/models/Form';
import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';
import { vueData } from '@/domain/services/VueData';
import { beforeEach, describe, expect, it, Mock, test, vi } from 'vitest';
import {
	collaboratorsControlController,
	Props,
	View,
} from '@/modules/entry-form/Collaboration/CollaboratorsControl.controller';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		translations: {},
		language: { locale: 'en_GB' },
		consumer: {
			isManager: false,
		},
	},
}));

vi.mock('@/domain/services/LocalEventBus', () => ({
	emit: vi.fn(),
}));

vi.mock('@/domain/utils/ThemeUtils', () => ({
	getThemeVariableValue: (key: string) => key,
}));

vi.mock('@/modules/entry-form/EntryFormProvider', () => ({
	useEntryFormContainer: vi.fn(),
}));

describe('CollaboratorsControl controller', () => {
	const form = { collaborative: true } as unknown as Form;

	const collaboratorsInstance = {
		getAll: vi.fn<Collaborator[]>(),
		onUpdate: vi.fn(),
		getMyself: vi.fn<Collaborator>(),
	};

	// eslint-disable-next-line @typescript-eslint/naming-convention
	const Collaborators = () => collaboratorsInstance;

	// eslint-disable-next-line @typescript-eslint/naming-convention
	const Submittable = {
		isSaved: vi.fn(),
		getForm: () => ({ slug: 'form-slug' }),
		getSubmittable: () => ({ slug: 'submittable-slug' }),
		isCollaborative: () => true,
	};

	beforeEach(() => {
		vi.resetAllMocks();
		(useEntryFormContainer as Mock).mockReturnValue({ Collaborators, Submittable });
		vueData.consumer.isManager = false;
	});

	it('shows if feature enabled and form is collaborative', () => {
		const view = collaboratorsControlController({ form } as Props, emptyCtx) as View;
		expect(view.visible).toBeTruthy();
	});

	it('hides if form not collaborative', () => {
		vueData.consumer.isManager = true;
		const myself = {
			owner: true,
		} as unknown as Collaborator;
		collaboratorsInstance.getMyself.mockImplementation(() => myself);
		collaboratorsInstance.onUpdate.mockImplementation((cb) => cb());

		(useEntryFormContainer as Mock).mockReturnValue({
			Collaborators,
			Submittable: {
				...Submittable,
				isCollaborative: () => false,
			},
		});
		const view = collaboratorsControlController({ form } as Props, emptyCtx) as View;
		expect(view.visible.value).toBeFalsy();
	});

	it('creates style', () => {
		const view = collaboratorsControlController({ form } as Props, emptyCtx) as View;
		expect(view.collaboratorsLinkStyle.value).toStrictEqual({
			'--color': 'primary-button',
			'--background-color': 'primary-button-text',
			'--color-hover': 'primary-button-hover-text',
			'--background-color-hover': 'primary-button-hover',
			'--outline-hover': `2px solid ${'primary-button-hover'}`,
			'--color-focus-active': 'primary-button-hover',
			'--outline-focus-active': `2px solid ${'primary-button-hover'}`,
			'--outline-offset-focus-active': '2px',
		});
	});

	it('gets reactive collaborators', () => {
		const collaborators = ['first', 'second'];
		collaboratorsInstance.getAll.mockReturnValue(collaborators);

		const view = collaboratorsControlController({ form } as Props, emptyCtx) as View;

		expect(view.collaborators.value).toStrictEqual(['first', 'second']);
		collaborators.push('third');
		expect(view.collaborators.value).toStrictEqual(['first', 'second', 'third']);
	});

	it('gets enabled', () => {
		vueData.consumer.isManager = true;
		Submittable.isSaved.mockImplementation(() => true);
		const view = collaboratorsControlController({ form } as Props, emptyCtx) as View;
		expect(view.enabled.value).toStrictEqual(true);
	});

	it('opens collaborators list modal', () => {
		vueData.consumer.isManager = true;
		Submittable.isSaved.mockImplementation(() => true);
		const view = collaboratorsControlController({ form } as Props, emptyCtx) as View;
		view.openCollaboratorsModal();
		expect(eventBus.emit).toHaveBeenCalledWith(CollaborationUISignals.OPEN_COLLABORATORS_LIST, {
			formSlug: 'form-slug',
			submittableSlug: 'submittable-slug',
			rebootFirebase: false,
		});
	});

	test('the "enabled" should be false if its a non-owner and not a manager', () => {
		vueData.consumer.isManager = false;
		const myself = {
			owner: false,
		} as unknown as Collaborator;
		collaboratorsInstance.getMyself.mockImplementation(() => myself);
		collaboratorsInstance.onUpdate.mockImplementation((cb) => cb());

		const view = collaboratorsControlController({ form } as Props, emptyCtx) as View;

		expect(view.enabled.value).toBeFalsy();
		expect(view.visible.value).toBeTruthy();
	});

	test('the "enabled" should be true if its an owner', () => {
		vueData.consumer.isManager = false;
		Submittable.isSaved.mockImplementation(() => true);

		const myself = {
			owner: true,
		} as unknown as Collaborator;
		collaboratorsInstance.getMyself.mockImplementation(() => myself);
		collaboratorsInstance.onUpdate.mockImplementation((cb) => cb());

		const view = collaboratorsControlController({ form } as Props, emptyCtx) as View;

		expect(view.enabled.value).toBeTruthy();
		expect(view.visible.value).toBeTruthy();
	});

	test('the "enabled" should be true if its manager', () => {
		vueData.consumer.isManager = true;
		Submittable.isSaved.mockImplementation(() => true);

		const myself = {
			owner: false,
		} as unknown as Collaborator;
		collaboratorsInstance.getMyself.mockImplementation(() => myself);
		collaboratorsInstance.onUpdate.mockImplementation((cb) => cb());

		const view = collaboratorsControlController({ form } as Props, emptyCtx) as View;

		expect(view.visible.value).toBeTruthy();
		expect(view.enabled.value).toBeTruthy();
	});

	test('the "enabled" should be true if user is not an owner or a manager', () => {
		vueData.consumer.isManager = false;
		Submittable.isSaved.mockImplementation(() => true);

		const myself = {
			owner: false,
		} as unknown as Collaborator;
		collaboratorsInstance.getMyself.mockImplementation(() => myself);
		collaboratorsInstance.onUpdate.mockImplementation((cb) => cb());

		const view = collaboratorsControlController({ form } as Props, emptyCtx) as View;

		expect(view.enabled.value).toBeTruthy();
	});
});
