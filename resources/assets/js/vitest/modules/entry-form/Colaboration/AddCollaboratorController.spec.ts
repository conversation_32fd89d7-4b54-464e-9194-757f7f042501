import * as eventBus from '@/domain/services/LocalEventBus';
import { CollaborationUISignals } from '@/domain/signals/Collaboration';
import { Collaborator } from '@/domain/models/Collaborator';
import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';
import { addCollaboratorController, View } from '@/modules/entry-form/Collaboration/AddCollaborator.controller';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { emptyCtx, emptyProps } from '@/../vitest/_common/helpers';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		translations: {},
		language: { locale: 'en_GB' },
	},
}));

vi.mock('@/domain/services/LocalEventBus', () => ({
	emit: vi.fn(),
}));

vi.mock('@/domain/utils/ThemeUtils', () => ({
	getThemeVariableValue: (key: string) => key,
}));

vi.mock('@/modules/entry-form/EntryFormProvider', () => ({
	useEntryFormContainer: vi.fn(),
}));

describe('CollaborarorsControl modal Controller', () => {
	const collaboratorsInstance = {
		getMyself: vi.fn<never, Collaborator>(),
		onUpdate: vi.fn(),
	};

	// eslint-disable-next-line @typescript-eslint/naming-convention
	const Collaborators = () => collaboratorsInstance;

	// eslint-disable-next-line @typescript-eslint/naming-convention
	const Submittable = {
		isSaved: vi.fn(),
		getForm: () => ({ slug: 'form-slug' }),
		getSubmittable: () => ({ slug: 'submittable-slug' }),
	};

	const onMounted = vi.fn();

	beforeEach(() => {
		vi.resetAllMocks();
		(useEntryFormContainer as Mock).mockReturnValue({ Collaborators, Submittable, onMounted });
	});

	it('shows if if user is owner or manager', () => {
		const myself = {
			owner: true,
			manager: false,
		} as unknown as Collaborator;

		collaboratorsInstance.getMyself.mockImplementation(() => myself);
		collaboratorsInstance.onUpdate.mockImplementation((cb) => cb());

		expect((addCollaboratorController(emptyProps, emptyCtx) as View).visible.value).toBeTruthy();

		myself.owner = false;
		expect((addCollaboratorController(emptyProps, emptyCtx) as View).visible.value).toBeFalsy();

		myself.manager = true;
		expect((addCollaboratorController(emptyProps, emptyCtx) as View).visible.value).toBeTruthy();

		myself.manager = true;
		myself.owner = true;
		expect((addCollaboratorController(emptyProps, emptyCtx) as View).visible.value).toBeTruthy();
	});

	it('gets enabled', () => {
		Submittable.isSaved.mockImplementation(() => true);
		expect((addCollaboratorController(emptyProps, emptyCtx) as View).enabled.value).toBeTruthy();

		Submittable.isSaved.mockImplementation(() => false);
		expect((addCollaboratorController(emptyProps, emptyCtx) as View).enabled.value).toBeFalsy();
	});

	it('opens collaborator invite modal', () => {
		const view = addCollaboratorController(emptyProps, emptyCtx) as View;
		view.openInviteCollaboratorModal();
		expect(eventBus.emit).toHaveBeenCalledWith(CollaborationUISignals.OPEN_INVITER, collaboratorsInstance);
	});

	it('correctly sets the visibility on mount', () => {
		let setVisibility: () => void = () => {};

		(onMounted as Mock).mockImplementation((cb) => (setVisibility = cb));

		collaboratorsInstance.getMyself.mockImplementation(() => ({
			owner: false,
			manager: false,
		}));
		const controller: View = addCollaboratorController(emptyProps, emptyCtx);

		expect(controller.visible.value).toBeFalsy();

		collaboratorsInstance.getMyself.mockImplementation(() => ({
			owner: true,
			manager: false,
		}));
		setVisibility();
		expect(controller.visible.value).toBeTruthy();

		expect(onMounted).toHaveBeenCalledOnce();
	});

	it('correctly sets the visibility on update', () => {
		const myself = {
			owner: true,
			manager: false,
		} as unknown as Collaborator;

		collaboratorsInstance.getMyself.mockImplementation(() => myself);
		collaboratorsInstance.onUpdate.mockImplementation((cb) => cb());
		const controller: View = addCollaboratorController(emptyProps, emptyCtx);
		expect(controller.visible.value).toBeTruthy();

		expect(onMounted).toHaveBeenCalledOnce();
	});
});
