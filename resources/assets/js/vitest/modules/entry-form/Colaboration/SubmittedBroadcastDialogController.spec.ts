import { beforeEach, describe, expect, it, Mock, test, vi } from 'vitest';
import { submittableFormBus, SubmittableFormSignal } from '@/modules/entry-form/Signals';
import {
	submittedBroadcastDialogController,
	View,
} from '@/modules/entry-form/Collaboration/SubmittedBroadcastDialog.controller';
import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';

vi.mock('@/domain/dao/Translations', () => ({
	trans: () => ({
		get: (key: string) => `translation-${key}`,
	}),
}));

vi.mock('@/modules/entry-form/EntryFormProvider', () => ({
	useEntryFormContainer: vi.fn(),
}));

describe('SubmittedBroadcastDialog  Controller', () => {
	let entryFormSignalCallbacks: Record<string, Mock>;

	beforeEach(() => {
		vi.resetAllMocks();
		const onSignal = vi.spyOn(submittableFormBus, 'on');

		entryFormSignalCallbacks = {};
		onSignal.mockImplementation((signal, cb) => {
			entryFormSignalCallbacks[signal] = cb;
		});

		(useEntryFormContainer as Mock).mockReturnValue({
			Submittable: {
				isGrantReport: () => false,
			},
			Consumer: {
				consumer: { slug: 'consumer-slug' },
			},
		});
	});

	it('boots modal for EntryForm', () => {
		const view = submittedBroadcastDialogController() as View;
		expect(view.message).toBe('translation-collaboration.dialogs.submitted.entry-form');
		expect(view.visible.value).toBe(false);
		expect(view.lang.get).toBeInstanceOf(Function);
		expect(view.onClosed).toBeInstanceOf(Function);
	});

	it('boots modal for GrantReport', () => {
		(useEntryFormContainer as Mock).mockReturnValue({
			Submittable: {
				isGrantReport: () => true,
			},
			Consumer: {
				consumer: { slug: 'consumer-slug' },
			},
		});
		const view = submittedBroadcastDialogController() as View;
		expect(view.message).toBe('translation-collaboration.dialogs.submitted.grant-report');
	});

	it('shows modal on signal', () => {
		const view = submittedBroadcastDialogController() as View;
		entryFormSignalCallbacks[SubmittableFormSignal.SUBMITTED]({ submittedUserSlug: 'another-slug' });
		expect(view.visible.value).toBe(true);
	});

	it('doesnt shows modal on signal /w ignored', () => {
		const view = submittedBroadcastDialogController() as View;
		entryFormSignalCallbacks[SubmittableFormSignal.SUBMITTED]({ submittedUserSlug: 'consumer-slug' });
		expect(view.visible.value).toBe(false);
	});

	it('refreshes on close if required', () => {
		const reload = vi.fn();
		vi.stubGlobal('window', {
			location: { reload },
		});

		const view = submittedBroadcastDialogController() as View;
		entryFormSignalCallbacks[SubmittableFormSignal.SUBMITTED]({ submittedUserSlug: 'another-slug', refreshPage: true });
		view.onClosed();
		expect(reload).toHaveBeenCalled();
		expect(view.visible.value).toBe(false);
	});

	it('doesn\'t refresh on close if not required', () => {
		const reload = vi.fn();
		vi.stubGlobal('window', {
			location: { reload },
		});

		const view = submittedBroadcastDialogController() as View;
		entryFormSignalCallbacks[SubmittableFormSignal.SUBMITTED]({ submittedUserSlug: 'another-slug', refreshPage: false });
		view.onClosed();
		expect(reload).not.toHaveBeenCalled();
		expect(view.visible.value).toBe(false);
	});
});
