import * as featureExports from '@/domain/dao/Features';
import { emptyCtx } from '@/../vitest/_common/helpers';
import { Form } from '@/domain/models/Form';
import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import {
	collaboratorsControlDisabledController,
	Props,
	View,
} from '@/modules/entry-form/Collaboration/CollaboratorsControlDisabled.controller';

vi.mock('@/modules/entry-form/EntryFormProvider', () => ({
	useEntryFormContainer: vi.fn(),
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		consumer: {
			fullName: '<PERSON>',
			profilePhoto: {
				fullName: '<PERSON>',
				image: 'photo.jpg',
				color: '#000000',
				initials: 'JD',
			},
		},
	},
}));

describe('collaboratorsControlDisabled controller', () => {
	const form = {
		settings: {
			collaborative: true,
		},
	} as unknown as Form;

	// eslint-disable-next-line @typescript-eslint/naming-convention
	const Submittable = {
		isSaved: vi.fn(),
	};

	beforeEach(() => {
		vi.resetAllMocks();
		(useEntryFormContainer as Mock).mockReturnValue({ Submittable });
	});

	it('shows if entry is not saved, feature enabled and form is collaborative', () => {
		Submittable.isSaved.mockImplementation(() => false);
		const featureEnabled = vi.spyOn(featureExports, 'featureEnabled').mockReturnValue(true);
		const view = collaboratorsControlDisabledController({ form } as Props, emptyCtx) as View;
		expect(view.visible).toBeTruthy();
		expect(featureEnabled).toHaveBeenCalledWith('collaboration');
	});

	it('hides if entry is saved', () => {
		Submittable.isSaved.mockImplementation(() => true);
		const view = collaboratorsControlDisabledController({ form } as Props, emptyCtx) as View;
		expect(view.visible).toBeFalsy();
	});

	it('hides is feature disabled', () => {
		Submittable.isSaved.mockImplementation(() => false);
		const featureEnabled = vi.spyOn(featureExports, 'featureEnabled').mockReturnValue(false);
		const view = collaboratorsControlDisabledController({ form } as Props, emptyCtx) as View;
		expect(view.visible).toBeFalsy();
		expect(featureEnabled).toHaveBeenCalledWith('collaboration');
	});

	it('hides if form not collaborative', () => {
		Submittable.isSaved.mockImplementation(() => false);
		form.settings.collaborative = false;
		const featureEnabled = vi.spyOn(featureExports, 'featureEnabled').mockReturnValue(true);
		const view = collaboratorsControlDisabledController({ form } as Props, emptyCtx) as View;
		expect(view.visible).toBeFalsy();
		expect(featureEnabled).not.toHaveBeenCalled();
	});

	it('returns profile data', () => {
		const view = collaboratorsControlDisabledController({ form } as Props, emptyCtx) as View;
		expect(view.profilePhoto.value).toEqual({
			fullName: 'John Doe',
			image: 'photo.jpg',
			color: '#000000',
			initials: 'JD',
		});
	});
});
