import { UpdateFieldEndpoint } from '@/modules/entry-form/Collaboration/services/Api';
import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';
import { useContainer } from '@/domain/services/Container';
import { useFieldService } from '@/modules/entry-form/Collaboration/services/Field.service';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { emptyMutableServiceFactory, mutableServiceFactory } from '@/domain/services/Collaboration/Mutable';

vi.stubGlobal('window', {
	addEventListener: vi.fn(),
});

vi.mock('@/modules/entry-form/Collaboration/services/CollaborativeSubmittable', () => ({
	useCollaborativeSubmittable: vi.fn(),
}));

vi.mock('@/domain/services/Container', () => ({
	useContainer: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Mutable', async () => {
	const actual = await vi.importActual('@/domain/services/Collaboration/Mutable');
	return {
		...actual,
		mutableServiceFactory: vi.fn(),
		emptyMutableServiceFactory: vi.fn(),
	};
});

vi.mock('@/domain/services/Collaboration/Collaborators', () => ({
	collaboratorsService: () => () => ({
		myAccess: vi.fn(),
	}),
}));

describe('Field service', () => {
	const onBeforeAppUnload = vi.fn();

	beforeEach(() => {
		vi.resetAllMocks();

		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: true,
			consumer: { slug: '<<consumer-slug>>' },
		});

		(useContainer as Mock).mockReturnValue({ onBeforeAppUnload });
	});

	it('creates empty service for non collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: false,
		});

		(emptyMutableServiceFactory as Mock).mockReturnValue({ destroy: 'foo-bar' });

		useFieldService('<<lockable-id>>', 'endpoint' as unknown as UpdateFieldEndpoint);
		expect(emptyMutableServiceFactory).toHaveBeenCalled();
	});

	it('creates mutable for collaborative app', () => {
		const mutableFactory = vi.fn();
		mutableFactory.mockReturnValue({ destroy: 'foo-bar' });
		(mutableServiceFactory as Mock).mockReturnValue(mutableFactory);

		const service = useFieldService('<<lockable-id>>', (() => {}) as unknown as UpdateFieldEndpoint);
		expect(mutableServiceFactory).toHaveBeenCalledWith('<<submittable-slug>>', '<<form-slug>>');
		expect(mutableFactory).toHaveBeenCalledWith('<<lockable-id>>', expect.any(Function), expect.any(Function));
		expect(onBeforeAppUnload).toHaveBeenCalledWith('foo-bar');
		expect(service.lockableId).toEqual('<<lockable-id>>');
	});

	it('manages sender with data', () => {
		const mutableFactory = vi.fn();

		let mutator = () => {};

		let filter = () => {};

		const subscribe = (f: () => void) => {
			filter = f;
		};

		mutableFactory.mockImplementation((submittableSlug: string, formSlug: string, dataMutator: (d: unknown) => d) => {
			mutator = dataMutator;
			return {
				destroy: vi.fn(),
				locks: {},
				values: { subscribe },
			};
		});

		(mutableServiceFactory as Mock).mockReturnValue(mutableFactory);

		const service = useFieldService('<<lockable-id>>', 'endpoint' as unknown as UpdateFieldEndpoint);
		expect(mutator('foo-bar')).toEqual({ value: 'foo-bar', sender: '<<consumer-slug>>' });

		const subscriber = vi.fn();
		service.values.subscribe(subscriber);

		filter({ value: 'foo', sender: '<<another-collaborator>>' });
		expect(subscriber).toHaveBeenCalledWith('foo');

		vi.resetAllMocks();
		filter({ value: 'foo', sender: '<<consumer-slug>>' });
		expect(subscriber).not.toHaveBeenCalled();
	});
});
