import { FieldResource } from '@/domain/models/Field';
import { PuttingEndpoint } from '@/domain/services/Api/Endpoint';
import { SubmittableUserRole } from '@/domain/models/Submittable';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useEntryFormApi, useGrantReportApi } from '@/modules/entry-form/Collaboration/services/Api';

vi.mock('@/domain/services/Api/Endpoint', () => ({
	PuttingEndpoint: vi.fn(),
}));

const request = vi.fn();
const entryFormApi = useEntryFormApi('<<submittable-slug>>', '<<feature-role>>' as unknown as SubmittableUserRole);
const grantReportApi = useGrantReportApi('<<submittable-slug>>', '<<feature-role>>' as unknown as SubmittableUserRole);

const expectations = (expectedUri: string, expectedArg?: unknown) => {
	expect(PuttingEndpoint).toHaveBeenCalledWith(expectedUri);
	if (expectedArg !== undefined) {
		expect(request).toHaveBeenCalledWith(expectedArg);
		return;
	}

	expect(request).toHaveBeenCalledWith();
};

describe('Collaborative controlls api', () => {
	beforeEach(() => {
		vi.resetAllMocks();
		PuttingEndpoint.mockReturnValue(() => request);
	});

	it('sends entry-form create link request', () => {
		entryFormApi.createLink({ slug: '<<tab-slug>>' });
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/create-link/<<tab-slug>>');
	});

	it('sends grant-report create link request', () => {
		grantReportApi.createLink({ slug: '<<tab-slug>>' });
		expectations('/grant-report/<<feature-role>>/grant-report/<<submittable-slug>>/create-link/<<tab-slug>>');
	});

	it('sends delete link request', () => {
		entryFormApi.deleteLink({ id: '<<link-id>>' });
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/delete-link/<<link-id>>');
	});

	it('sends update link request', () => {
		entryFormApi.updateLink({ id: '<<link-id>>', foo: 'bar' });
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/update-link/<<link-id>>', {
			id: '<<link-id>>',
			foo: 'bar',
		});
	});

	it('sends create contributor request', () => {
		entryFormApi.createContributor({ slug: '<<tab-slug>>' });
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/create-contributor/<<tab-slug>>');
	});

	it('sends delete contributor request', () => {
		entryFormApi.deleteContributor({ id: '<<contributor-id>>' });
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/remove-contributor/<<contributor-id>>');
	});

	it('sends update title request', () => {
		entryFormApi.updateTitle('foo bar');
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/update-title', { title: 'foo bar' });
	});

	it('sends update category request', () => {
		entryFormApi.updateCategory(123);
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/update-category', { categoryId: 123 });
	});

	it('sends update chapter request', () => {
		entryFormApi.updateChapter(123);
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/update-chapter', { chapterId: 123 });
	});

	it('sends update entry field request', () => {
		entryFormApi.updateField({
			slug: '<<field-slug>>',
			resource: FieldResource.RESOURCE_FORMS,
		})(123);
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/update-field/<<field-slug>>', { value: 123 });
	});

	it('sends update attachment field request', () => {
		entryFormApi.updateField(
			{
				slug: '<<field-slug>>',
				resource: FieldResource.RESOURCE_ATTACHMENTS,
			},
			123
		)(456);

		expect(PuttingEndpoint).not.toHaveBeenCalledWith();
	});

	it('sends update contributor field request', () => {
		entryFormApi.updateField(
			{
				slug: '<<field-slug>>',
				resource: FieldResource.RESOURCE_CONTRIBUTORS,
			},
			123
		)(456);
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/update-contributor/123/<<field-slug>>', {
			value: 456,
		});
	});

	it('doesn`t send field update for unknown resource', () => {
		entryFormApi.updateField(
			{
				slug: '<<field-slug>>',
				resource: 'unknown-resource-type' as unknown as FieldResource,
			},
			123
		)(456);
		expect(PuttingEndpoint).not.toHaveBeenCalled();
	});

	it('sends update referee field request', () => {
		entryFormApi.updateField(
			{
				slug: '<<field-slug>>',
				resource: FieldResource.RESOURCE_REFEREES,
			},
			123
		)(456);
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/update-referee/123/<<field-slug>>', {
			value: 456,
		});
	});

	it('sends create referee request', () => {
		entryFormApi.createReferee({ slug: '<<tab-slug>>' });
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/create-referee/<<tab-slug>>');
	});

	it('sends update referee request', () => {
		entryFormApi.updateReferee({ id: 123 });
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/update-referee/123', { id: 123 });
	});

	it('sends delete referee request', () => {
		entryFormApi.deleteReferee({ id: 123 });
		expectations('/entry-form/<<feature-role>>/entry/<<submittable-slug>>/remove-referee/123', { id: 123 });
	});
});
