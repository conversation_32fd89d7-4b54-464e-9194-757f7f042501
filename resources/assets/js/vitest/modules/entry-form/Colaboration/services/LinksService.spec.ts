import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';
import { useLinksService } from '@/modules/entry-form/Collaboration/services/Links.service';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { documentServiceFactory, nullDocumentService } from '@/domain/services/Collaboration/Document';
import { emptyMutableServiceFactory, mutableServiceFactory } from '@/domain/services/Collaboration/Mutable';

vi.mock('@/modules/entry-form/Collaboration/services/CollaborativeSubmittable', () => ({
	useCollaborativeSubmittable: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Document', () => ({
	collectionServiceFactory: vi.fn(),
	nullDocumentService: vi.fn(),
	documentServiceFactory: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Mutable', () => ({
	mutableServiceFactory: vi.fn(),
	emptyMutableServiceFactory: vi.fn(),
	muteFrom: vi.fn(),
	signature: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Collaborators', () => ({
	collaboratorsService: () => () => ({
		myAccess: vi.fn(),
	}),
}));

describe('Links service', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('creates empty service for non collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: false,
			api: {
				createLink: 'createLink',
				deleteLink: 'deleteLink',
				updateLink: 'updateLink',
			},
		});

		const service = useLinksService('<<link-slug>>');
		expect(emptyMutableServiceFactory).toHaveBeenCalled();
		expect(nullDocumentService).toHaveBeenCalled();
		expect(service).toHaveProperty('api');
		expect(service).toHaveProperty('mutableService');
	});

	it('creates mutable for collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: true,
			api: {
				createLink: 'createLink',
				deleteLink: 'deleteLink',
				updateLink: 'updateLink',
			},
		});

		const documentFactory = vi.fn();
		(documentServiceFactory as Mock).mockReturnValue(documentFactory);

		const mutableFactory = vi.fn();
		(mutableFactory as Mock).mockReturnValue({
			values: { set: vi.fn() },
		});
		(mutableServiceFactory as Mock).mockReturnValue(mutableFactory);

		const service = useLinksService('<<link-slug>>');
		expect(mutableServiceFactory).toHaveBeenCalledWith('<<submittable-slug>>', '<<form-slug>>');
		expect(documentServiceFactory).toHaveBeenCalledWith('<<submittable-slug>>', '<<form-slug>>');
		expect(documentFactory).toHaveBeenCalledWith('links');
		expect(mutableFactory).toHaveBeenCalledWith('<<link-slug>>', null, undefined);
		expect(service.api).toStrictEqual({
			createLink: 'createLink',
			deleteLink: 'deleteLink',
			updateLink: 'updateLink',
		});
	});
});
