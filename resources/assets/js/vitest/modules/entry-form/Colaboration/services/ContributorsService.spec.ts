import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';
import { useContributorsService } from '@/modules/entry-form/Collaboration/services/Contributors.service';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { collectionServiceFactory, emptyDocumentService } from '@/domain/services/Collaboration/Document';

vi.mock('@/modules/entry-form/Collaboration/services/CollaborativeSubmittable', () => ({
	useCollaborativeSubmittable: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Document', () => ({
	collectionServiceFactory: vi.fn(),
	emptyDocumentService: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Collaborators', () => ({
	collaboratorsService: () => () => ({
		myAccess: vi.fn(),
	}),
}));

describe('Contributors service', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('creates empty service for non collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: false,
			api: {
				createContributor: 'createContributor',
				deleteContributor: 'deleteContributor',
			},
		});

		const service = useContributorsService();
		expect(emptyDocumentService).toHaveBeenCalled();
		expect(service).toHaveProperty('api');
		expect(service).toHaveProperty('service');
	});

	it('creates document for collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: true,
			api: {
				createContributor: 'createContributor',
				deleteContributor: 'deleteContributor',
			},
		});

		const documentFactory = vi.fn();
		(collectionServiceFactory as Mock).mockReturnValue(documentFactory);

		const service = useContributorsService(123);
		expect(collectionServiceFactory).toHaveBeenCalledWith('<<submittable-slug>>-123', '<<form-slug>>');
		expect(documentFactory).toHaveBeenCalledWith('contributors');
		expect(service).toHaveProperty('service');
		expect(service.api).toStrictEqual({
			createContributor: 'createContributor',
			deleteContributor: 'deleteContributor',
		});
	});
});
