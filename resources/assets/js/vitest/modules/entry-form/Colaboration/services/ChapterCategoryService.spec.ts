import { getDataSource } from '@/domain/services/Rt/Rtdb';
import { useChapterCategoryService } from '@/modules/entry-form/Collaboration/services/ChapterCategory.service';
import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { emptyMutableServiceFactory, mutableServiceFactory } from '@/domain/services/Collaboration/Mutable';

vi.mock('@/modules/entry-form/Collaboration/services/CollaborativeSubmittable', () => ({
	useCollaborativeSubmittable: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Mutable', () => ({
	mutableServiceFactory: vi.fn(),
	emptyMutableServiceFactory: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Collaborators', () => ({
	collaboratorsService: () => () => ({
		myAccess: vi.fn(),
	}),
}));

vi.mock('@/domain/services/Rt/Rtdb', () => ({
	getDataSource: vi.fn(),
}));

describe('ChapterCategory service', () => {
	const locks = {
		subscribe: vi.fn(),
		set: vi.fn(),
		destroy: vi.fn(),
	};

	const collaborators = {
		get: vi.fn(),
		subscribe: vi.fn(),
	};

	const getDS = (path: string) => {
		if (path.match(/collaboration\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+\/collaborators/i)) return collaborators;
		return locks;
	};

	beforeEach(() => {
		vi.resetAllMocks();
		(getDataSource as Mock).mockImplementation(getDS);
	});

	it('creates empty service for non collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: false,
		});

		const values = {
			set: vi.fn(),
		};

		(emptyMutableServiceFactory as Mock).mockReturnValue({
			locks: {},
			values,
			destroy: vi.fn(),
		});

		const service = useChapterCategoryService();
		expect(emptyMutableServiceFactory).toHaveBeenCalled();

		service.values.set({ category: '<<category>>', chapter: '<<chapter>>' });
		expect(values.set).toHaveBeenCalledWith({ category: '<<category>>', chapter: '<<chapter>>' });
	});

	it('creates mutable for collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: true,
		});

		const mutableFactory = vi.fn();
		const set = vi.fn();

		(mutableFactory as Mock).mockReturnValue({
			locks: {},
			values: { set },
			destroy: vi.fn(),
		});

		(mutableServiceFactory as Mock).mockReturnValue(mutableFactory);

		const service = useChapterCategoryService();
		expect(mutableServiceFactory).toHaveBeenCalledWith('<<submittable-slug>>', '<<form-slug>>');
		expect(mutableFactory).toHaveBeenCalledWith('chapter-category');

		service.values.set({ category: '<<category>>' });
		expect(set).toHaveBeenCalledWith({ category: '<<category>>' });
	});
});
