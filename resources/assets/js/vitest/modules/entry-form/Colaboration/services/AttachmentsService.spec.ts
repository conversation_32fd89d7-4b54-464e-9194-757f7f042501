import { getDataSource } from '@/domain/services/Rt/Rtdb';
import { useAttachmentsService } from '@/modules/entry-form/Collaboration/services/Attachments.service';
import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { documentServiceFactory, nullDocumentService } from '@/domain/services/Collaboration/Document';
vi.mock('@/modules/entry-form/Collaboration/services/CollaborativeSubmittable', () => ({
	useCollaborativeSubmittable: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Document', () => ({
	documentServiceFactory: vi.fn(),
	nullDocumentService: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Collaborators', () => ({
	collaboratorsService: () => () => ({
		myAccess: vi.fn(),
	}),
}));

vi.mock('@/domain/services/Rt/Rtdb', () => ({
	getDataSource: vi.fn(),
}));

describe('Attachments service', () => {
	const locks = {
		subscribe: vi.fn(),
		set: vi.fn(),
		destroy: vi.fn(),
	};

	const collaborators = {
		get: vi.fn(),
		subscribe: vi.fn(),
	};
	const getDS = (path: string) => {
		if (path.match(/collaboration\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+\/collaborators/i)) return collaborators;
		return locks;
	};

	beforeEach(() => {
		vi.resetAllMocks();
		(getDataSource as Mock).mockImplementation(getDS);
	});

	it('creates empty service for non collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: false,
		});

		useAttachmentsService();
		expect(nullDocumentService).toHaveBeenCalled();
	});

	it('creates document for collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: true,
		});

		const documentFactory = vi.fn();
		(documentServiceFactory as Mock).mockReturnValue(documentFactory);

		useAttachmentsService();
		expect(documentServiceFactory).toHaveBeenCalledWith('<<submittable-slug>>', '<<form-slug>>');
		expect(documentFactory).toHaveBeenCalledWith('attachments');
	});

	it('service set method convert the document array as an object', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: true,
		});

		const documentFactory = vi.fn().mockReturnValue({ set: vi.fn() });
		(documentServiceFactory as Mock).mockReturnValue(documentFactory);

		useAttachmentsService().set(['test_01', 'test_02']);
		expect(documentFactory().set).toHaveBeenCalledWith({ 0: 'test_01', 1: 'test_02' });

		useAttachmentsService().set(Object.values({ test01: 'test_11', test02: 'test_22' }));
		expect(documentFactory().set).toHaveBeenCalledWith({ 0: 'test_11', 1: 'test_22' });
	});
});
