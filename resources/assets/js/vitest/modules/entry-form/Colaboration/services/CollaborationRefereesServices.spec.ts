import { emptyApi } from '@/modules/entry-form/Collaboration/services/Api';
import { useCollaborationRefereesService } from '@/modules/entry-form/Collaboration/services/CollaborationReferees.service';
import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';
import { collectionServiceFactory, nullDocumentService } from '@/domain/services/Collaboration/Document';
import { describe, expect, it, Mock, vi } from 'vitest';

vi.mock('@/modules/entry-form/Collaboration/services/CollaborativeSubmittable', () => ({
	useCollaborativeSubmittable: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Document', () => ({
	collectionServiceFactory: vi.fn(),
	nullDocumentService: vi.fn(),
	documentServiceFactory: vi.fn(),
}));

describe('CollaborationReferees service', () => {
	it('creates empty service for non collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: false,
			api: emptyApi,
		});

		const service = useCollaborationRefereesService('<<link-slug>>');
		expect(nullDocumentService).toHaveBeenCalled();
		expect(service).toHaveProperty('service');
		expect(service).toHaveProperty('api');
	});

	it('creates document for collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: true,
			api: {
				createReferee: 'createReferee',
				updateReferee: 'updateReferee',
				deleteReferee: 'deleteReferee',
			},
		});

		const documentFactory = vi.fn();
		(collectionServiceFactory as Mock).mockReturnValue(documentFactory);

		const service = useCollaborationRefereesService(123);
		expect(collectionServiceFactory).toHaveBeenCalledWith('<<submittable-slug>>-123', '<<form-slug>>');
		expect(documentFactory).toHaveBeenCalledWith('referees');
		expect(service).toHaveProperty('service');
		expect(service.api).toStrictEqual({
			createReferee: 'createReferee',
			updateReferee: 'updateReferee',
			deleteReferee: 'deleteReferee',
		});
	});
});
