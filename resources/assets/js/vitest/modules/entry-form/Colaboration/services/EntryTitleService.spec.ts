import { getDataSource } from '@/domain/services/Rt/DataSource';
import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';
import { useContainer } from '@/domain/services/Container';
import { useEntryTitleService } from '@/modules/entry-form/Collaboration/services/EntryTitle.service';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { emptyMutableServiceFactory, mutableServiceFactory, signature } from '@/domain/services/Collaboration/Mutable';

vi.stubGlobal('window', {
	addEventListener: vi.fn(),
});

vi.mock('@/modules/entry-form/Collaboration/services/CollaborativeSubmittable', () => ({
	useCollaborativeSubmittable: vi.fn(),
}));

vi.mock('@/domain/services/Container', () => ({
	useContainer: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Mutable', () => ({
	mutableServiceFactory: vi.fn(),
	emptyMutableServiceFactory: vi.fn(),
	muteFrom: vi.fn(),
	signature: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/Collaborators', () => ({
	collaboratorsService: () => () => ({
		myAccess: vi.fn(),
	}),
}));

vi.mock('@/domain/services/Rt/DataSource', () => ({
	getDataSource: vi.fn(),
}));

const locks = {
	subscribe: vi.fn(),
	set: vi.fn(),
	destroy: vi.fn(),
};

const collaborators = {
	get: vi.fn(),
	subscribe: vi.fn(),
};
const getDS = (path: string) => {
	if (path.match(/collaboration\/[a-zA-Z0-9]+\/[a-zA-Z0-9]+\/collaborators/i)) return collaborators;
	return locks;
};

describe('EntryTitle service', () => {
	const mutableFactory = vi.fn();
	const onBeforeAppUnload = vi.fn();

	beforeEach(() => {
		vi.resetAllMocks();
		(getDataSource as Mock).mockImplementation(getDS);
		(useContainer as Mock).mockReturnValue({ onBeforeAppUnload });
	});

	it('creates empty service for non collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: false,
			api: null,
		});

		(emptyMutableServiceFactory as Mock).mockReturnValue({ destroy: 'foo-bar' });

		const mutableFactory = vi.fn();
		mutableFactory.mockReturnValue({
			destroy: vi.fn(),
			locks: {},
			values: {},
		});
		(mutableServiceFactory as Mock).mockReturnValue(mutableFactory);

		useEntryTitleService();
		expect(emptyMutableServiceFactory).toHaveBeenCalled();
	});

	it('creates mutable for collaborative app', () => {
		(useCollaborativeSubmittable as Mock).mockReturnValue({
			submittableSlug: '<<submittable-slug>>',
			formSlug: '<<form-slug>>',
			isCollaborative: true,
			api: { updateTitle: 'updateTitle' },
		});

		(mutableFactory as Mock).mockReturnValue({ destroy: 'foo-bar', values: 'values' });
		(mutableServiceFactory as Mock).mockReturnValue(mutableFactory);
		(signature as Mock).mockReturnValue('signature');

		useEntryTitleService();
		expect(mutableServiceFactory).toHaveBeenCalledWith('<<submittable-slug>>', '<<form-slug>>');
		expect(mutableFactory).toHaveBeenCalledWith('title', 'updateTitle', 'signature');
		expect(onBeforeAppUnload).toHaveBeenCalledWith('foo-bar');
	});
});
