import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { webhookFormControllerFactory } from '@/modules/webhooks/WebhookForm.controller';
import { WebhookFormProps } from '@/modules/webhooks/WebhookForm.types';

const baseProps: WebhookFormProps = {
	formsWithFields: [
		{
			id: 1,
			slug: 'form-1',
			name: 'Form 1',
			fields: [
				{ id: 101, slug: 'field-101', name: 'Field 101' },
				{ id: 102, slug: 'field-102', name: 'Field 102' },
				{ id: 103, slug: 'field-103', name: 'Field 103' },
			],
		},
		{
			id: 2,
			slug: 'form-2',
			name: 'Form 2',
			fields: [
				{ id: 201, slug: 'field-201', name: 'Field 201' },
				{ id: 202, slug: 'field-202', name: 'Field 202' },
			],
		},
	],
	fieldIds: [101, 103, 201],
	webhook: {
		formId: 1,
		subscriptionEvents: ['FieldValueUpdated'],
	},
};

const ctxMock = {
	emit: vi.fn(),
};

describe('WebhookForm controller test suite', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.resetAllMocks();
	});

	it('should initialize with correct values from props', () => {
		const view = webhookFormControllerFactory()(baseProps, ctxMock);

		expect(view.subscriptionEvents.value).toEqual(['FieldValueUpdated']);
		expect(view.selectedForm.value).toBe('form-1');
		expect(view.showResourceSelector.value).toBe(true);
	});

	it('should compute forms correctly', () => {
		const view = webhookFormControllerFactory()(baseProps, ctxMock);

		expect(view.forms.value).toEqual([
			{ slug: 'form-1', name: 'Form 1' },
			{ slug: 'form-2', name: 'Form 2' },
		]);
	});

	it('should compute formFields correctly based on selectedForm', () => {
		const view = webhookFormControllerFactory()(baseProps, ctxMock);

		// Initial form is form-1
		expect(view.formFields.value).toEqual([
			{ id: 101, slug: 'field-101', name: 'Field 101' },
			{ id: 102, slug: 'field-102', name: 'Field 102' },
			{ id: 103, slug: 'field-103', name: 'Field 103' },
		]);

		// Change the form
		view.onFormChange({} as Event, 'form-2');

		expect(view.formFields.value).toEqual([
			{ id: 201, slug: 'field-201', name: 'Field 201' },
			{ id: 202, slug: 'field-202', name: 'Field 202' },
		]);
	});

	it('should compute selectedFieldSlugs correctly', () => {
		const view = webhookFormControllerFactory()(baseProps, ctxMock);

		// For form-1, fieldIds 101 and 103 match
		expect(view.selectedFieldSlugs.value).toEqual(['field-101', 'field-103']);

		// Change to form-2, only fieldId 201 matches
		view.onFormChange({} as Event, 'form-2');
		expect(view.selectedFieldSlugs.value).toEqual(['field-201']);
	});

	it('should update selectedForm when onFormChange is called', () => {
		const view = webhookFormControllerFactory()(baseProps, ctxMock);

		expect(view.selectedForm.value).toBe('form-1');

		view.onFormChange({} as Event, 'form-2');

		expect(view.selectedForm.value).toBe('form-2');
	});

	it('should update subscriptionEvents when onSubscriptionEventsChange is called', () => {
		const view = webhookFormControllerFactory()(baseProps, ctxMock);

		expect(view.subscriptionEvents.value).toEqual(['FieldValueUpdated']);

		view.onSubscriptionEventsChange(['EntryCreated', 'EntryUpdated']);

		expect(view.subscriptionEvents.value).toEqual(['EntryCreated', 'EntryUpdated']);
		expect(view.showResourceSelector.value).toBe(false);
	});

	it('should handle empty subscriptionEvents', () => {
		const view = webhookFormControllerFactory()(baseProps, ctxMock);

		view.onSubscriptionEventsChange([]);

		expect(view.subscriptionEvents.value).toEqual([]);
		expect(view.showResourceSelector.value).toBe(false);
	});

	it('should handle null subscriptionEvents by using empty array', () => {
		const view = webhookFormControllerFactory()(baseProps, ctxMock);

		view.onSubscriptionEventsChange(null);

		expect(view.subscriptionEvents.value).toEqual([]);
		expect(view.showResourceSelector.value).toBe(false);
	});

	it('should find form by id correctly', () => {
		const view = webhookFormControllerFactory()(baseProps, ctxMock);

		const form1 = view.form(1);
		expect(form1).toEqual(baseProps.formsWithFields[0]);

		const form2 = view.form(2);
		expect(form2).toEqual(baseProps.formsWithFields[1]);

		const nonExistentForm = view.form(999);
		expect(nonExistentForm).toBeUndefined();
	});

	it('should handle undefined formId in form function', () => {
		const view = webhookFormControllerFactory()(baseProps, ctxMock);

		const result = view.form(undefined);
		expect(result).toBeUndefined();
	});

	it('should initialize with empty values when webhook is null', () => {
		const props = { ...baseProps, webhook: null };
		const view = webhookFormControllerFactory()(props, ctxMock);

		expect(view.subscriptionEvents.value).toEqual([]);
		expect(view.selectedForm.value).toBeUndefined();
		expect(view.showResourceSelector.value).toBe(false);
	});

	it('should handle empty formsWithFields', () => {
		const props = { ...baseProps, formsWithFields: [] };
		const view = webhookFormControllerFactory()(props, ctxMock);

		expect(view.forms.value).toEqual([]);
		expect(view.formFields.value).toEqual([]);
		expect(view.selectedFieldSlugs.value).toEqual([]);
	});
});
