import {
	categoryMultiselectControllerFactory,
	CategoryOption,
} from '@/modules/notifications/components/CategoryMultiselect.controller';
import { describe, expect, it, vi } from 'vitest';

vi.mock('@/lib/utils', () => ({
	getGlobalData: (key: string) => {
		switch (key) {
			case 'language':
				return {
					locale: 'en_GB',
					fallback: 'en_GB',
				};
		}
	},
}));

describe('category multiselect', () => {
	const apiService = {
		get: () => Promise.resolve({ data: ['c3', 'c4'] }),
	};

	it('reloads categories when form changes', async () => {
		const multiselect = categoryMultiselectControllerFactory(apiService)({
			selectedCategoryOption: CategoryOption.All,
			startingCategories: ['c1', 'c2'],
			selectedCategories: [],
			startingForm: 'f1',
			selectedForm: 'f2',
		});

		await multiselect.categories.value;

		expect(multiselect.categories.value).to.deep.equals(['c3', 'c4']);
	});

	it('does not reload categories if form has not changed', () => {
		const multiselect = categoryMultiselectControllerFactory(apiService)({
			selectedCategoryOption: CategoryOption.All,
			startingCategories: ['c1', 'c2'],
			selectedCategories: [],
			startingForm: 'f1',
			selectedForm: 'f1',
		});

		expect(multiselect.categories.value).to.deep.equals(['c1', 'c2']);
	});
});
