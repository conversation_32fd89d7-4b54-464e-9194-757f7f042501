import { formMultiselectController } from '@/modules/notifications/components/FormMultiselect.controller';
import { describe, expect, it, vi } from 'vitest';

vi.mock('@/lib/utils', () => ({
	getGlobalData: (key: string) => {
		switch (key) {
			case 'language':
				return {
					locale: 'en_GB',
					fallback: 'en_GB',
				};
		}
	},
}));

describe('form multiselect', () => {
	it('category selection available when only one form is selected', () => {
		const multiselect = formMultiselectController({
			trigger: 'entry.submitted',
			isMultiform: true,
			selectedFormOption: 'all',
			forms: [
				{ slug: 'f1', name: 'f1', type: 'entry' },
				{ slug: 'f2', name: 'f2', type: 'entry' },
			],
			selectedForms: ['f1', 'f2'],
			selectedCategoryOption: 'all',
			categories: ['c1', 'c2'],
			selectedCategories: [],
			selectedScoreSetOption: '',
			scoreSets: [],
			selectedScoreSets: [],
			selectedRecipientOption: '',
			recipients: '',
			fields: [],
			selectedFields: [],
		});

		expect(multiselect.categorySelectionEnabled.value).toBe(false);

		multiselect.formOption.value = 'select';
		expect(multiselect.categorySelectionEnabled.value).toBe(false);

		multiselect.formSelection.value = [];
		expect(multiselect.categorySelectionEnabled.value).toBe(false);

		multiselect.formSelection.value = ['f2'];
		expect(multiselect.categorySelectionEnabled.value).toBe(true);
	});

	it('category selection available by default if multiform disabled', () => {
		const multiselect = formMultiselectController({
			trigger: 'entry.submitted',
			isMultiform: false,
			selectedFormOption: 'all',
			forms: [{ slug: 'f1', name: 'f1', type: 'entry' }],
			selectedForms: ['f1'],
			selectedCategoryOption: 'all',
			categories: ['c1', 'c2'],
			selectedCategories: [],
			selectedScoreSetOption: '',
			scoreSets: [],
			selectedScoreSets: [],
			selectedRecipientOption: '',
			recipients: '',
			fields: [],
			selectedFields: [],
		});

		expect(multiselect.categorySelectionEnabled.value).toBe(true);
	});

	it('score-set selection available when only one for judging.completed trigger', () => {
		const multiselect = formMultiselectController({
			trigger: 'assignment.judging.completed',
			isMultiform: true,
			selectedFormOption: 'all',
			forms: [],
			selectedForms: [],
			selectedCategoryOption: 'all',
			categories: [],
			selectedCategories: [],
			selectedScoreSetOption: '',
			scoreSets: [],
			selectedScoreSets: [],
			selectedRecipientOption: '',
			recipients: '',
			fields: [],
			selectedFields: [],
		});

		expect(multiselect.scoreSetSelectionEnabled.value).toBe(true);
	});

	it('filters forms by type', () => {
		let multiselect = formMultiselectController({
			trigger: 'entry.submitted',
			isMultiform: true,
			selectedFormOption: 'some',
			forms: [
				{ slug: 'f1', name: 'entry form', type: 'entry' },
				{ slug: 'f2', name: 'grant report form', type: 'report' },
			],
			selectedForms: ['f1'],
			selectedCategoryOption: 'all',
			categories: [],
			selectedCategories: [],
			selectedScoreSetOption: '',
			scoreSets: [],
			selectedScoreSets: [],
			selectedRecipientOption: '',
			recipients: '',
			fields: [],
			selectedFields: [],
		});

		expect(multiselect.filteredForms.value).toHaveLength(1);
		expect(multiselect.filteredForms.value[0].name).toBe('entry form');

		multiselect = formMultiselectController({
			trigger: 'grant.report',
			isMultiform: true,
			selectedFormOption: 'some',
			forms: [
				{ slug: 'f1', name: 'entry form', type: 'entry' },
				{ slug: 'f2', name: 'grant report form', type: 'report' },
			],
			selectedForms: ['f1'],
			selectedCategoryOption: 'all',
			categories: [],
			selectedCategories: [],
			selectedScoreSetOption: '',
			scoreSets: [],
			selectedScoreSets: [],
			selectedRecipientOption: '',
			recipients: '',
			fields: [],
			selectedFields: [],
		});

		expect(multiselect.filteredForms.value).toHaveLength(1);
		expect(multiselect.filteredForms.value[0].name).toBe('grant report form');
	});

	it('shows form selector when resubmission required trigger is selected', () => {
		const multiselect = formMultiselectController({
			trigger: 'resubmission.required',
			isMultiform: true,
			selectedFormOption: 'some',
			forms: [
				{ slug: 'f1', name: 'entry form', type: 'entry' },
				{ slug: 'f2', name: 'grant report form', type: 'report' },
			],
			selectedForms: ['f1'],
			selectedCategoryOption: 'all',
			categories: [],
			selectedCategories: [],
			selectedScoreSetOption: '',
			scoreSets: [],
			selectedScoreSets: [],
			selectedRecipientOption: '',
			recipients: '',
			fields: [],
			selectedFields: [],
		});

		expect(multiselect.formSelectionEnabled.value).toBeTruthy();
	});
});
