import * as FeatureInterface from '@/services/global/features.interface';
import { featureEnabled } from '@/services/global/features.interface';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import {
	RecipientOption,
	recipientOptionControllerFactory,
} from '@/modules/notifications/components/RecipientOption.controller';

vi.mock('@/services/global/features.interface', () => ({
	featureEnabled: vi.fn(),
}));

vi.mock('@/lib/utils', () => ({
	getGlobalData: (key: string) => {
		switch (key) {
			case 'language':
				return {
					locale: 'en_GB',
					fallback: 'en_GB',
				};
		}
	},
}));

describe('recipient options', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	const apiService = {
		get: () => Promise.resolve({ data: ['f3', 'f4'] }),
	};

	it('reloads fields when form changes', async () => {
		const recipientOptions = recipientOptionControllerFactory(apiService)({
			trigger: 'entry.submitted',
			selectedRecipientOption: RecipientOption.Field,
			startingRecipients: '',
			startingFields: ['f1', 'f2'],
			selectedField: '',
			selectedForm: 'f2',
			startingForm: 'f1',
			oneFormSelected: true,
			notificationSettings: { sendToCollaborators: false },
		});

		await recipientOptions.fields.value;

		// expect(recipientOptions.fields.value).to.deep.equals(['f3', 'f4']);
	});

	it('customises recipient label for assignment triggers ', async () => {
		const props = {
			trigger: 'entry.submitted',
			selectedRecipientOption: RecipientOption.User,
			startingRecipients: '',
			startingFields: [],
			selectedField: '',
			selectedForm: 'f1',
			startingForm: 'f1',
			oneFormSelected: true,
		};

		// let recipientOptions = recipientOptionControllerFactory(apiService)(props);

		// expect(recipientOptions.recipientLabel.value()).toBe('notifications.form.recipient_options.options.user');

		props.trigger = 'assignment.judging.completed';
		// recipientOptions = recipientOptionControllerFactory(apiService)(props);

		// expect(recipientOptions.recipientLabel.value()).toBe('judging.table.columns.judge');
	});

	it('displays field recipient option for grant status changed trigger', async () => {
		const props = {
			trigger: 'grant.status.changed',
			selectedRecipientOption: RecipientOption.User,
			startingRecipients: '',
			startingFields: [],
			selectedField: '',
			selectedForm: 'f1',
			startingForm: 'f1',
			oneFormSelected: true,
			notificationSettings: { sendToCollaborators: false },
		};

		const recipientOptions = recipientOptionControllerFactory(apiService)(props);

		expect(recipientOptions.fieldOptionEnabled.value).toBe(true);
	});

	it('displays message for mail recipient only when sms feature is disabled', () => {
		vi.spyOn(FeatureInterface, 'featureEnabled').mockReturnValue(false);
		const props = {
			trigger: 'entry.submitted',
			selectedRecipientOption: RecipientOption.User,
			startingRecipients: '',
			startingFields: [],
			selectedField: '',
			selectedForm: 'f1',
			startingForm: 'f1',
			oneFormSelected: true,
			notificationSettings: { sendToCollaborators: false },
		};
		const recipientOptions = recipientOptionControllerFactory(apiService)(props);

		expect(recipientOptions.recipientsOptionLabel()).toBe('notifications.form.recipient_options.options.mails');
	});

	it('displays generic message recipients when sms feature is enabled', () => {
		vi.spyOn(FeatureInterface, 'featureEnabled').mockReturnValue(true);
		const props = {
			trigger: 'entry.submitted',
			selectedRecipientOption: RecipientOption.User,
			startingRecipients: '',
			startingFields: [],
			selectedField: '',
			selectedForm: 'f1',
			startingForm: 'f1',
			oneFormSelected: true,
			notificationSettings: { sendToCollaborators: false },
		};
		const recipientOptions = recipientOptionControllerFactory(apiService)(props);

		expect(recipientOptions.recipientsOptionLabel()).toBe('notifications.form.recipient_options.options.recipients');
	});

	it('sets user recipient option when recipient is user and collaborators', () => {
		const props = {
			trigger: 'grant.status.changed',
			selectedRecipientOption: RecipientOption.UserCollaborators,
			startingRecipients: '',
			startingFields: [],
			selectedField: '',
			selectedForm: 'f1',
			startingForm: 'f1',
			oneFormSelected: true,
			notificationSettings: { sendToCollaborators: false },
		};

		const recipientOptions = recipientOptionControllerFactory(apiService)(props);

		expect(recipientOptions.recipientOption.value).toBe(RecipientOption.User);
	});

	it('shows feature disabled when collaboration features is not enabled', () => {
		const props = {
			trigger: 'grant.status.changed',
			selectedRecipientOption: RecipientOption.UserCollaborators,
			startingRecipients: '',
			startingFields: [],
			selectedField: '',
			selectedForm: 'f1',
			startingForm: 'f1',
			oneFormSelected: true,
			notificationSettings: { sendToCollaborators: false },
		};

		const recipientOptions = recipientOptionControllerFactory(apiService)(props);
		(featureEnabled as Mock).mockReturnValueOnce(false);

		expect(recipientOptions.collaborationEnabled.value).toBeFalsy();
	});
});
