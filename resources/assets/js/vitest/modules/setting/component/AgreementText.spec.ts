import { afterEach, describe, expect, it, vi } from 'vitest';
import { agreementTextController, Props } from '@/modules/setting/components/AgreementText.controller';

describe('AgreementTextController', () => {
	afterEach(() => {
		vi.clearAllMocks();
	});

	it('resets agreement text', () => {
		const defaultTexts = {
			en_GB: 'English default',
			pl_PL: 'Polish default',
		};
		const multilingualResource = {
			translated: {
				en_GB: { agreementToTerms: 'GB' },
				pl_PL: { agreementToTerms: 'PL' },
			},
		};

		const props: Props = {
			defaultAgreementTexts: defaultTexts,
			multilingualResource: multilingualResource,
			hasCustomAgreementText: true,
		};

		const controller = agreementTextController(props);

		expect(multilingualResource).toEqual({
			translated: {
				en_GB: { agreementToTerms: 'GB' },
				pl_PL: { agreementToTerms: 'PL' },
			},
		});

		controller.onResetAgreement();

		expect(multilingualResource).toEqual({
			translated: {
				en_GB: { agreementToTerms: 'English default' },
				pl_PL: { agreementToTerms: 'Polish default' },
			},
		});
	});
});
