import { afterEach, describe, expect, it, vi } from 'vitest';
import { consentTextController, Props } from '@/modules/setting/components/ConsentText.controller';

describe('ConsentTextController', () => {
	afterEach(() => {
		vi.clearAllMocks();
	});

	it('resets consent text', () => {
		const defaultTexts = {
			en_GB: 'English default',
			pl_PL: 'Polish default',
		};
		const multilingualResource = {
			translated: {
				en_GB: { consentToNotificationsAndBroadcasts: 'GB' },
				pl_PL: { consentToNotificationsAndBroadcasts: 'PL' },
			},
		};

		const props: Props = {
			defaultConsentTexts: defaultTexts,
			multilingualResource: multilingualResource,
			hasCustomConsentText: true,
		};

		const controller = consentTextController(props);

		expect(multilingualResource).toEqual({
			translated: {
				en_GB: { consentToNotificationsAndBroadcasts: 'GB' },
				pl_PL: { consentToNotificationsAndBroadcasts: 'PL' },
			},
		});

		controller.onResetConsent();

		expect(multilingualResource).toEqual({
			translated: {
				en_GB: { consentToNotificationsAndBroadcasts: 'English default' },
				pl_PL: { consentToNotificationsAndBroadcasts: 'Polish default' },
			},
		});
	});
});
