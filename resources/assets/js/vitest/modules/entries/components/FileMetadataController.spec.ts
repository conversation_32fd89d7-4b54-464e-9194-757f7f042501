import { Hook } from '@/domain/utils/Types';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { fileMetadataControllerFactory, Props, View } from '@/modules/entries/components/FileMetadata.controller';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		translations: {},
		language: { locale: 'en_GB' },
	},
}));

vi.mock('@/domain/services/Api/Headers', async () => ({
	headersFactory: () => ({}),
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		routes: {
			'file.metadata': 'file/metadata/{id}',
		},
		language: {
			locale: 'en_GB',
			fallback: 'en_GB',
		},
	},
}));

vi.mock('@/modules/entries/components/FileMetadata.api', () => ({
	getFileMetadata: vi.fn(),
}));

describe('FileMetadata Controller', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('sets error flag if metadata api returns error', async () => {
		const props: Props = {
			file: 1,
		};

		const onMountedHookMock = vi.fn(async (callback: () => Promise<void>) => {
			await callback();
		});

		vi.mock('@/modules/entries/components/FileMetadata.api', () => ({
			getFileMetadata: () => vi.fn().mockRejectedValue(new Error('error')),
		}));

		const view: View = await fileMetadataControllerFactory({
			onMountedHook: onMountedHookMock as Hook,
		})(props);

		// Assert that the returned view has the expected properties
		expect(view.hasError.value).toBeTruthy();
		expect(view.loading.value).toBeFalsy();
		expect(onMountedHookMock).toHaveBeenCalled();
	});

	it('stops loading always after the api call', async () => {
		const props: Props = {
			file: 1,
		};

		const onMountedHookMock = vi.fn(async (callback: () => Promise<void>) => {
			await callback();
		});

		const view: View = await fileMetadataControllerFactory({
			onMountedHook: onMountedHookMock as Hook,
		})(props);

		// Assert that the returned view has the expected properties
		expect(view.loading.value).toBeFalsy();
		expect(onMountedHookMock).toHaveBeenCalled();
	});
});
