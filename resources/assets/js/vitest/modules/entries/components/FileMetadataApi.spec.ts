import { getFileMetadata } from '@/modules/entries/components/FileMetadata.api';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { DataRequestMethod, DataResponse, dataSource } from '@/domain/services/Api/DataSource';

vi.mock('@/domain/services/Api/Headers', async () => ({
	headersFactory: () => ({}),
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		routes: {
			'file.metadata': 'file/metadata/{id}',
		},
	},
}));

type FileMetadataResponse = DataResponse<{
	size: string;
	dataContent: string;
}>;

describe('FileMetadata api', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('should correctly fetch file metadata', async () => {
		const responseData = {
			size: 'test-size',
			dataContent: 'test-data-content',
		};

		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: responseData } as FileMetadataResponse);

		const response = await getFileMetadata()(123);

		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/file/metadata/123',
			method: DataRequestMethod.GET,
			headers: {},
			data: undefined,
		});
		expect(response).toStrictEqual(responseData);
	});
});
