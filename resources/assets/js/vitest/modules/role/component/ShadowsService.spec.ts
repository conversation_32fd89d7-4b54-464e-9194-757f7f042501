import { shadowsServiceFactory } from '@/modules/role/component/Shadows.service';
import { afterEach, describe, expect, it, vi } from 'vitest';

describe('Shadows service test suit', () => {
	afterEach(() => {
		vi.restoreAllMocks();
	});

	it('should reset the shadows', () => {
		const showSimpleForm = { value: false };
		const shadows = {
			showTopShadow: { value: true },
			showBottomShadow: { value: false },
			showRightShadow: { value: true },
			showLeftShadow: { value: true },
		};

		const shadowsService = shadowsServiceFactory(shadows, showSimpleForm);
		shadowsService.resetShadows();

		expect(shadows.showTopShadow.value).toBe(false);
		expect(shadows.showBottomShadow.value).toBe(true);
		expect(shadows.showRightShadow.value).toBe(false);
		expect(shadows.showLeftShadow.value).toBe(false);
	});

	it('should set the right shadow visibility', () => {
		const showSimpleForm = { value: false };
		const shadows = {
			showTopShadow: { value: true },
			showBottomShadow: { value: false },
			showRightShadow: { value: true },
			showLeftShadow: { value: true },
		};

		const shadowsService = shadowsServiceFactory(shadows, showSimpleForm);

		shadowsService.setRightShadowVisibility(false);
		expect(shadows.showRightShadow.value).toBe(false);

		shadowsService.setRightShadowVisibility(true);
		expect(shadows.showRightShadow.value).toBe(true);
	});

	// simple form
	it('should show top shadow and hide bottom shadow if viewport is scrolled to the bottom', () => {
		const showSimpleForm = { value: true };
		const shadows = {
			showTopShadow: { value: false },
			showBottomShadow: { value: true },
			showRightShadow: { value: true },
			showLeftShadow: { value: true },
		};
		const shadowsService = shadowsServiceFactory(shadows, showSimpleForm);

		const scrollEvent: WheelEvent = {
			target: {
				scrollTop: 100,
				scrollHeight: 200,
				clientHeight: 100,
			},
		} as unknown as WheelEvent;

		shadowsService.handleScroll(scrollEvent);

		expect(shadows.showTopShadow.value).toBe(true);
		expect(shadows.showBottomShadow.value).toBe(false);
		expect(shadows.showRightShadow.value).toBe(false);
		expect(shadows.showLeftShadow.value).toBe(false);
	});

	it('should show bottom shadow and hide top shadow if viewport is scrolled to the top', () => {
		const showSimpleForm = { value: true };
		const shadows = {
			showTopShadow: { value: true },
			showBottomShadow: { value: false },
			showRightShadow: { value: true },
			showLeftShadow: { value: true },
		};
		const shadowsService = shadowsServiceFactory(shadows, showSimpleForm);

		const scrollEvent: WheelEvent = {
			target: {
				scrollTop: 0,
				scrollHeight: 200,
				clientHeight: 100,
			},
		} as unknown as WheelEvent;

		shadowsService.handleScroll(scrollEvent);

		expect(shadows.showTopShadow.value).toBe(false);
		expect(shadows.showBottomShadow.value).toBe(true);
		expect(shadows.showRightShadow.value).toBe(false);
		expect(shadows.showLeftShadow.value).toBe(false);
	});

	// advanced form
	it('should show right shadow and hide left shadow if viewport is scrolled to the left', () => {
		const showSimpleForm = { value: false };
		const shadows = {
			showTopShadow: { value: true },
			showBottomShadow: { value: false },
			showRightShadow: { value: false },
			showLeftShadow: { value: true },
		};
		const shadowsService = shadowsServiceFactory(shadows, showSimpleForm);

		const scrollEvent: WheelEvent = {
			target: {
				scrollTop: 0,
				scrollHeight: 200,
				clientHeight: 100,
				clientWidth: 100,
				scrollLeft: 0,
				scrollWidth: 200,
			},
		} as unknown as WheelEvent;

		shadowsService.handleScroll(scrollEvent);

		expect(shadows.showTopShadow.value).toBe(false);
		expect(shadows.showBottomShadow.value).toBe(true);
		expect(shadows.showRightShadow.value).toBe(true);
		expect(shadows.showLeftShadow.value).toBe(false);
	});

	it('should show left shadow and hide right shadow if viewport is scrolled to the right', () => {
		const showSimpleForm = { value: false };
		const shadows = {
			showTopShadow: { value: true },
			showBottomShadow: { value: false },
			showRightShadow: { value: true },
			showLeftShadow: { value: false },
		};

		const shadowsService = shadowsServiceFactory(shadows, showSimpleForm);

		const scrollEvent: WheelEvent = {
			target: {
				scrollTop: 0,
				scrollHeight: 200,
				clientHeight: 100,
				clientWidth: 100,
				scrollLeft: 100,
				scrollWidth: 200,
			},
		} as unknown as WheelEvent;

		shadowsService.handleScroll(scrollEvent);

		expect(shadows.showTopShadow.value).toBe(false);
		expect(shadows.showBottomShadow.value).toBe(true);
		expect(shadows.showRightShadow.value).toBe(false);
		expect(shadows.showLeftShadow.value).toBe(true);
	});
});
