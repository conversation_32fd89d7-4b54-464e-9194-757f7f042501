import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Emit, Props, roleMatrixRowController } from '@/modules/role/component/RoleMatrixRow.controller';

vi.mock('@/services/global/translations.interface', () => ({
	getTrans: (key: string) => key,
}));

describe('roleMatrixRowController', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	const props: Props = {
		permission: [
			{
				resource: 'EntriesAll',
				action: 'create',
				mode: 'allow',
				actionDisabled: false,
				chapterLimited: false,
				guest: false,
				registration: false,
				name: 'EntriesAll',
				description: 'EntriesAll',
			},
			{
				resource: 'EntriesAll',
				action: 'view',
				mode: 'allow',
				actionDisabled: false,
				chapterLimited: false,
				guest: false,
				registration: false,
				name: 'EntriesAll',
				description: 'EntriesAll',
			},
			{
				resource: 'EntriesAll',
				action: 'update',
				mode: 'allow',
				actionDisabled: false,
				chapterLimited: false,
				guest: false,
				registration: false,
				name: 'EntriesAll',
				description: 'EntriesAll',
			},
			{
				resource: 'EntriesAll',
				action: 'delete',
				mode: 'allow',
				actionDisabled: false,
				chapterLimited: false,
				guest: false,
				registration: false,
				name: 'EntriesAll',
				description: 'EntriesAll',
			},
		],
		roleSettings: {
			chapterLimited: false,
			guest: false,
			registration: false,
			default: false,
		},
	};

	const emit: Emit = vi.fn();

	const controller = roleMatrixRowController(props, emit);

	it('should compute the basePermission', () => {
		expect(controller.basePermission.value).toEqual(props.permission[0]);
	});

	it('should compute the name and description', () => {
		expect(controller.name).toBe(props.permission[0]['name']);
		expect(controller.description).toBe(props.permission[0]['description']);
	});

	it('should compute required modes to build the UI', () => {
		expect(controller.modes).toEqual(['deny', 'inherit', 'allow']);
	});

	it('isDisabled should return false if matches all requirements', () => {
		expect(controller.isDisabled.value).toBe(false);
	});

	it('isDisabled should return true if disabledByChapterLimitedSetting is true', () => {
		const propsDisabledByChapterLimited = { ...props };
		propsDisabledByChapterLimited.permission[0]['chapterLimited'] = true;
		propsDisabledByChapterLimited.roleSettings['chapterLimited'] = true;

		const controller = roleMatrixRowController(propsDisabledByChapterLimited, emit);

		expect(controller.isDisabled.value).toBe(true);
	});

	it('isDisabled should return true if disabledByGuestSetting is true', () => {
		const propsDisabledByGuest = { ...props };
		propsDisabledByGuest.permission[0]['guest'] = true;
		propsDisabledByGuest.roleSettings['guest'] = true;

		const controller = roleMatrixRowController(propsDisabledByGuest, emit);

		expect(controller.isDisabled.value).toBe(true);
	});

	it('isDisabled should return true if disabledByRegistrationSetting is true', () => {
		const propsDisabledByRegistration = { ...props };
		propsDisabledByRegistration.permission[0]['registration'] = true;
		propsDisabledByRegistration.roleSettings['registration'] = true;

		const controller = roleMatrixRowController(propsDisabledByRegistration, emit);

		expect(controller.isDisabled.value).toBe(true);
	});

	it('isDisabled should return true if actionDisabled is true', () => {
		const propsDisabledByActionDisabled = { ...props };
		propsDisabledByActionDisabled.permission[0]['actionDisabled'] = true;

		const controller = roleMatrixRowController(propsDisabledByActionDisabled, emit);

		expect(controller.isDisabled.value).toBe(true);
	});

	it('should compute the isActionDisabled', () => {
		const propsDisabledByActionDisabled = { ...props };
		propsDisabledByActionDisabled.permission[0]['actionDisabled'] = true;

		const controllerDisabledByActionDisabled = roleMatrixRowController(propsDisabledByActionDisabled, emit);

		expect(controllerDisabledByActionDisabled.isActionDisabled('create')).toBe(true);

		const propsEnabledByActionDisabled = { ...props };
		propsEnabledByActionDisabled.permission[0]['actionDisabled'] = false;

		const controllerEnabledByActionDisabled = roleMatrixRowController(propsEnabledByActionDisabled, emit);

		expect(controllerEnabledByActionDisabled.isActionDisabled('create')).toBe(false);
	});

	it('should call setPermissionMode on emit when setPermissionMode is called', () => {
		controller.setPermissionMode('Chapters', 'create', 'allow');
		expect(emit).toHaveBeenCalledWith('setPermission', {
			resource: 'Chapters',
			action: 'create',
			mode: 'allow',
		});
	});

	it('should call setPermissionMode for each permission when setAllPermissionsMode is called', () => {
		controller.setAllPermissionsMode('allow');
		expect(emit).toHaveBeenCalledTimes(props.permission.length);
		expect(emit).toHaveBeenCalledWith('setPermission', {
			resource: 'EntriesAll',
			action: 'create',
			mode: 'allow',
		});
	});

	it('should compute allPermissionsAllowed', () => {
		expect(controller.allPermissionsAllowed.value).toBe(true);

		const propsAllPermissionsAllowed = { ...props };
		propsAllPermissionsAllowed.permission[0]['mode'] = 'deny';

		const controllerAllPermissionsAllowed = roleMatrixRowController(propsAllPermissionsAllowed, emit);

		expect(controllerAllPermissionsAllowed.allPermissionsAllowed.value).toBe(false);
	});

	it('should show Simple Checkbox if all permissions allowed', () => {
		const propsAllPermissionsAllowed = { ...props };
		propsAllPermissionsAllowed.permission[0]['mode'] = 'allow';
		propsAllPermissionsAllowed.permission[1]['mode'] = 'allow';
		propsAllPermissionsAllowed.permission[2]['mode'] = 'allow';
		propsAllPermissionsAllowed.permission[3]['mode'] = 'allow';

		const controllerAllPermissionsAllowed = roleMatrixRowController(propsAllPermissionsAllowed, emit);

		expect(controllerAllPermissionsAllowed.showSimpleCheckbox.value).toBe(true);
	});

	it('should show Simple Checkbox if all permissions inherited', () => {
		const propsAllPermissionsInherited = { ...props };
		propsAllPermissionsInherited.permission[0]['mode'] = 'inherit';
		propsAllPermissionsInherited.permission[1]['mode'] = 'inherit';
		propsAllPermissionsInherited.permission[2]['mode'] = 'inherit';
		propsAllPermissionsInherited.permission[3]['mode'] = 'inherit';

		const controllerAllPermissionsInherited = roleMatrixRowController(propsAllPermissionsInherited, emit);

		expect(controllerAllPermissionsInherited.showSimpleCheckbox.value).toBe(true);
	});

	it('should not show Simple Checkbox if all permissions denied', () => {
		const propsAllPermissionsDenied = { ...props };
		propsAllPermissionsDenied.permission[0]['mode'] = 'deny';
		propsAllPermissionsDenied.permission[1]['mode'] = 'deny';
		propsAllPermissionsDenied.permission[2]['mode'] = 'deny';
		propsAllPermissionsDenied.permission[3]['mode'] = 'deny';

		const controllerAllPermissionsDenied = roleMatrixRowController(propsAllPermissionsDenied, emit);

		expect(controllerAllPermissionsDenied.showSimpleCheckbox.value).toBe(false);
	});

	it('should not show Simple Checkbox if permissions mode are divergent', () => {
		const propsAllPermissionsAllowed = { ...props };
		propsAllPermissionsAllowed.permission[0]['mode'] = 'deny';
		propsAllPermissionsAllowed.permission[1]['mode'] = 'allow';
		propsAllPermissionsAllowed.permission[2]['mode'] = 'deny';

		const controllerAllPermissionsAllowed = roleMatrixRowController(propsAllPermissionsAllowed, emit);

		expect(controllerAllPermissionsAllowed.showSimpleCheckbox.value).toBe(false);
	});
});
