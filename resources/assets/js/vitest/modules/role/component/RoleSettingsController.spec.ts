import { describe, expect, it, vi } from 'vitest';
import { Emit, Props, roleSettingsController } from '@/modules/role/component/RoleSettings.controller';

vi.mock('@/services/global/translations.interface', () => ({
	getTrans: (key: string) => key,
}));

describe('roleSettingsController', () => {
	const props: Props = {
		roleSettings: {
			chapterLimited: 1,
			guest: 0,
			requireAuthenticator: 0,
			registration: 0,
			default: 0,
			formContentId: 1,
			completedContentId: 1,
			completedScoreSetId: 1,
		},
		role: {
			id: 1,
			permissions: JSON.stringify({}),
			roleRegistrationForm: JSON.stringify({}),
			roleRegistrationCompleted: JSON.stringify({}),
			completedScoreSets: JSON.stringify({}),
		},
		hasCompletedScoreSet: 'score-set',
		completedScoreSets: {
			'Season 1': {
				'1': 'Scoring',
				'2': 'Qualifying',
			},
			'Another season': {
				'3': 'Qualifying',
				'4': 'Top pick',
			},
		},
	};

	const emit: Emit = vi.fn();

	const controller = roleSettingsController(props, emit);

	it('should initialize the registrationAction ref', () => {
		expect(controller.registrationAction.value).toBe('score-set');
	});

	it('should compute the editing property', () => {
		expect(controller.editing.value).toBe(true);

		const propsNewRole = { ...props, role: { ...props.role, id: undefined } };

		const controllerNewRole = roleSettingsController(propsNewRole, emit);
		expect(controllerNewRole.editing.value).toBe(false);
	});

	it('should compute the chapterLimited property', () => {
		expect(controller.chapterLimited.value).toBe(props.roleSettings.chapterLimited);
	});

	it('should compute the guest property', () => {
		expect(controller.guest.value).toBe(props.roleSettings.guest);
	});

	it('should compute the requireAuthenticator property', () => {
		expect(controller.requireAuthenticator.value).toBe(props.roleSettings.requireAuthenticator);
	});

	it('should compute the registration property', () => {
		expect(controller.registration.value).toBe(props.roleSettings.registration);
	});

	it('should compute the defaultForRegistration property', () => {
		expect(controller.defaultForRegistration.value).toBe(props.roleSettings.default);
	});

	it('should compute the formContentId property', () => {
		expect(controller.formContentId.value).toBe(props.roleSettings.formContentId);
	});

	it('should compute the completedContentId property', () => {
		expect(controller.completedContentId.value).toBe(props.roleSettings.completedContentId);
	});

	it('should compute the completedScoreSetId property', () => {
		expect(controller.completedScoreSetId.value).toBe(props.roleSettings.completedScoreSetId);
	});

	it('should compute the parsedCompletedScoreSets property', () => {
		expect(controller.parsedCompletedScoreSets.value).toEqual([
			{
				name: 'Season 1',
				children: [
					{
						id: '1',
						name: 'Scoring',
					},
					{
						id: '2',
						name: 'Qualifying',
					},
				],
			},
			{
				name: 'Another season',
				children: [
					{
						id: '3',
						name: 'Qualifying',
					},
					{
						id: '4',
						name: 'Top pick',
					},
				],
			},
		]);
	});

	it('should call emit with the correct value when setRoleSetting is called', () => {
		controller.setRoleSetting(1, 'chapterLimited');
		expect(emit).toHaveBeenCalledWith('roleSettings', { chapterLimited: 1 });
	});
});
