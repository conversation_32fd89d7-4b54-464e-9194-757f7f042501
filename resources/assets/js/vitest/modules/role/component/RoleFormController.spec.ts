import { RoleSettings } from '@/modules/role/component/Role.types';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { Props, roleFormController } from '@/modules/role/component/RoleForm.controller';

vi.mock('@/lib/utils', () => ({
	getGlobalData: () => ({ locale: 'en_GB', fallback: 'en_GB' }),
}));

const roleProps: Props = {
	role: {
		default: 0,
		guest: 0,
		registration: 0,
		requireAuthenticator: 0,
		formContentId: 1,
		completedContentId: 2,
		completedScoreSetId: 3,
		chapterLimited: 0,
	},
	permissions: JSON.stringify({}),
	roleRegistrationForm: JSON.stringify({}),
	roleRegistrationCompleted: JSON.stringify({}),
	completedScoreSets: JSON.stringify({}),
};

describe('Role Form Controller test suite', () => {
	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should initialize the role settings correctly', () => {
		const controller = roleFormController(roleProps);

		expect(controller.roleSettings.value.default).toBe(0);
		expect(controller.roleSettings.value.guest).toBe(0);
		expect(controller.roleSettings.value.registration).toBe(0);
		expect(controller.roleSettings.value.requireAuthenticator).toBe(0);
		expect(controller.roleSettings.value.formContentId).toBe(1);
		expect(controller.roleSettings.value.completedContentId).toBe(2);
		expect(controller.roleSettings.value.completedScoreSetId).toBe(3);
		expect(controller.roleSettings.value.chapterLimited).toBe(0);
	});

	it('should set the role setting correctly', () => {
		const controller = roleFormController(roleProps);

		const updatedRoleSettings: RoleSettings = {
			default: 1,
			guest: 1,
			registration: 1,
			requireAuthenticator: 1,
			formContentId: 5,
			completedContentId: 6,
			completedScoreSetId: 7,
			chapterLimited: 1,
		};

		controller.setRoleSetting(updatedRoleSettings);

		expect(controller.roleSettings.value.default).toBe(1);
		expect(controller.roleSettings.value.guest).toBe(1);
		expect(controller.roleSettings.value.registration).toBe(1);
		expect(controller.roleSettings.value.requireAuthenticator).toBe(1);
		expect(controller.roleSettings.value.formContentId).toBe(5);
		expect(controller.roleSettings.value.completedContentId).toBe(6);
		expect(controller.roleSettings.value.completedScoreSetId).toBe(7);
		expect(controller.roleSettings.value.chapterLimited).toBe(1);
	});

	it('should toggle the view correctly', () => {
		const controller = roleFormController(roleProps);
		const currentShowSimpleFormValue = controller.showSimpleForm.value;

		controller.toggleView();

		expect(controller.showSimpleForm.value).toBe(!currentShowSimpleFormValue);
	});
});
