import { formModeServiceFactory } from '@/modules/role/component/FormMode.service';
import { afterEach, describe, expect, it, vi } from 'vitest';

describe('Form mode service test suit', () => {
	afterEach(() => {
		vi.restoreAllMocks();
	});

	const shadowsService = {
		resetShadows: () => undefined,
		handleScroll: () => undefined,
		setRightShadowVisibility: () => undefined,
	};

	const setRightShadowVisibilitySpy = vi.spyOn(shadowsService, 'setRightShadowVisibility');

	it('should toggle the simple form visibility', () => {
		const showSimpleFormRef = { value: true };
		const formModeService = formModeServiceFactory(showSimpleFormRef, shadowsService);

		formModeService.showSimpleFormToggle(showSimpleFormRef);
		expect(showSimpleFormRef.value).toBe(false);
		expect(setRightShadowVisibilitySpy).toHaveBeenCalledWith(true);

		vi.restoreAllMocks();

		formModeService.showSimpleFormToggle(showSimpleFormRef);
		expect(showSimpleFormRef.value).toBe(true);
		expect(setRightShadowVisibilitySpy).toHaveBeenCalledWith(false);
	});

	it('should set the simple form visibility with a specific value', () => {
		const showSimpleFormRef = { value: false };
		const formModeService = formModeServiceFactory(showSimpleFormRef, shadowsService);

		formModeService.showSimpleFormToggle(true);
		expect(showSimpleFormRef.value).toBe(true);
		expect(setRightShadowVisibilitySpy).toHaveBeenCalledWith(true);

		vi.restoreAllMocks();

		formModeService.showSimpleFormToggle(false);
		expect(showSimpleFormRef.value).toBe(false);
		expect(setRightShadowVisibilitySpy).toHaveBeenCalledWith(false);
	});

	it('should force the simple form visibility', () => {
		const showSimpleFormRef = { value: false };
		const formModeService = formModeServiceFactory(showSimpleFormRef, shadowsService);
		const resetShadowsSpy = vi.spyOn(shadowsService, 'resetShadows');

		formModeService.forceSimpleForm();

		expect(showSimpleFormRef.value).toBe(true);
		expect(resetShadowsSpy).toHaveBeenCalled();
	});
});
