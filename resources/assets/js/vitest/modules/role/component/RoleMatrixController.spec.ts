import { Permission } from '@/modules/role/component/Role.types';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { Emit, Props, roleMatrixController } from '@/modules/role/component/RoleMatrix.controller';

vi.mock('@/services/global/translations.interface', () => ({
	getTrans: (key: string) => key,
}));

const emit: Emit = vi.fn();

const props: Props = {
	permissions: {
		ApiKeys: [
			{ resource: 'ApiKeys', action: 'create', mode: 'deny' } as Permission,
			{ resource: 'ApiKeys', action: 'view', mode: 'deny' } as Permission,
			{ resource: 'ApiKeys', action: 'update', mode: 'deny' } as Permission,
			{ resource: 'ApiKeys', action: 'delete', mode: 'deny' } as Permission,
		],
	},
} as unknown as Props;

describe('Role Matrix Controller', () => {
	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should set permission mode correctly', () => {
		const controller = roleMatrixController(props, emit);

		controller.setPermission({ resource: 'ApiKeys', action: 'create', mode: 'allow' } as Permission);
		controller.setPermission({ resource: 'ApiKeys', action: 'view', mode: 'inherit' } as Permission);
		controller.setPermission({ resource: 'ApiKeys', action: 'update', mode: 'inherit' } as Permission);
		controller.setPermission({ resource: 'ApiKeys', action: 'delete', mode: 'deny' } as Permission);

		expect(controller.reactivePermissions.value.ApiKeys[0].mode).toBe('allow');
		expect(controller.reactivePermissions.value.ApiKeys[1].mode).toBe('inherit');
		expect(controller.reactivePermissions.value.ApiKeys[2].mode).toBe('inherit');
		expect(controller.reactivePermissions.value.ApiKeys[3].mode).toBe('deny');
	});

	it('should call onMounted hook', () => {
		const controller = roleMatrixController(props, emit);
		controller.onMountedCallback();

		expect(controller.showSimpleForm.value).toBe(true);
		expect(controller.showRightShadow.value).toBe(false);
		expect(controller.showLeftShadow.value).toBe(false);
	});

	it('should call onBeforeUnmount hook', () => {
		const controller = roleMatrixController(props, emit);
		controller.onBeforeUnmountCallback();

		// todo: test this
		// expect(controller.body.value).toBe(null);
	});

	it('should emit showSimpleForm', async () => {
		const controller = roleMatrixController(props, emit);
		await controller.showSimpleFormToggle(false);

		expect(emit).toHaveBeenCalledWith('showSimpleForm', false);
	});
});
