import Status from '@/lib/components/Uploader/Status';
import { afterEach, describe, expect, it, vi } from 'vitest';
import { previewController, Props, View } from '@/modules/document-templates/components/Uploader/Preview.controller';

vi.mock('@/lib/utils.js', () => ({
	supportedLanguages: () => ['en_GB', 'fr_CA'],
}));

describe('Preview Controller', () => {
	afterEach(() => {
		vi.clearAllMocks();
	});

	it('calculates in progress properties correctly', () => {
		const props: Props = {
			value: {
				status: Status.QUEUED,
				url: 'https://example.com/file.pdf',
				token: '12345',
				original: 'file.pdf',
				slug: 'PKJgtoise',
			},
		};

		const controller: View = previewController(props);

		expect(controller.isMultilingual.value).toBe(true);
		expect(controller.file.value).toEqual(props.value);
		expect(controller.inProgress.value).toBe(true);
	});

	it('calculates in progress properties when not in progress', () => {
		const props: Props = {};

		const controller: View = previewController(props);

		expect(controller.isMultilingual.value).toBe(true);
		expect(controller.file.value).toBeNull();
		expect(controller.inProgress.value).toBe(false);
	});
});
