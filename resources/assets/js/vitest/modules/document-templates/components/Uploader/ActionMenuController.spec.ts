import { actionMenuController, Props } from '@/modules/document-templates/components/Uploader/ActionMenu.controller';
import { afterEach, describe, expect, it, vi } from 'vitest';

const langMock = {
	get: (key: string) => `Mocked ${key}`,
};

vi.mock('@/services/global/translations.interface', () => ({
	getTrans: () => langMock,
}));

describe('Action Menu Controller', () => {
	afterEach(() => {
		vi.clearAllMocks();
	});

	it('calculates action menu items correctly when not disabled and with download link', () => {
		const props: Props = {
			disabled: false,
			downloadLink: 'https://example.com/file.pdf',
		};

		const controller = actionMenuController(props);

		expect(controller.items.value).toEqual([
			{
				label: 'Mocked files.actions.delete',
				id: 'delete',
				disabled: false,
			},
			{
				label: 'Mocked files.download',
				id: 'download',
				url: 'https://example.com/file.pdf',
				class: 'ignore',
				disabled: false,
			},
			{
				label: 'Mocked files.actions.replace',
				id: 'replace',
				disabled: false,
			},
		]);
	});
});
