import { afterEach, describe, expect, it, vi } from 'vitest';
import {
	View,
	wiredUploaderController,
} from '@/modules/document-templates/components/Uploader/WiredUploader.controller';

vi.mock('@/lib/utils', () => ({
	supportedLanguages: () => ['en_GB', 'fr_CA'],
	getGlobalData: () => ({ locale: 'en_GB', fallback: 'en_GB' }),
}));

vi.mock('@/services/global/translations.interface', () => ({
	getTrans: (key: string) => key,
}));

describe('Wired Uploader Controller', () => {
	afterEach(() => {
		vi.clearAllMocks();
	});

	it('correctly initializes data', () => {
		const controller: View = wiredUploaderController();

		expect(controller.fileToken.value).toBeNull();
		expect(controller.files.value).toEqual([]);
		expect(controller.isMultilingual.value).toBe(true);
	});

	it('correctly updates fileToken upon uploaded', () => {
		const controller: View = wiredUploaderController();

		const mockToken = 'abc123';
		const mockFile = { id: 'file123' };

		controller.uploaded(mockToken, mockFile);

		expect(controller.fileToken.value).toBe(mockToken);
	});
});
