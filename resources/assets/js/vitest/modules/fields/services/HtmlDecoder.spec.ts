import { decode } from '@/modules/fields/services/HtmlDecoder';
import { describe, expect, it } from 'vitest';

/**
 * @vitest-environment jsdom
 */
describe('HtmlDecoder', () => {
	it('should return empty string for null input', () => {
		const result = decode(null);
		expect(result).toBe('');
	});

	it('should strip HTML tags and decode entities', () => {
		const input = '<p>Test &amp; <strong>Title</strong></p>';
		const result = decode(input);
		expect(result).toBe('Test & Title');
	});

	it('should handle plain text correctly', () => {
		const input = 'Just a simple title';
		const result = decode(input);
		expect(result).toBe('Just a simple title');
	});

	it('should return empty string for empty input', () => {
		const result = decode('');
		expect(result).toBe('');
	});

	it('should decode HTML entities without tags', () => {
		const input = 'Title &amp; More';
		const result = decode(input);
		expect(result).toBe('Title & More');
	});

	it('should handle special characters', () => {
		const input = '<p>&lt;Test &gt; &amp; More&lt;/p&gt;</p>';
		const result = decode(input);
		expect(result).toBe('<Test > & More</p>');
	});

	it('should handle a mix of tags and encoded entities', () => {
		const input = '<p>Title &lt;strong&gt;&amp;</p>';
		const result = decode(input);
		expect(result).toBe('Title <strong>&');
	});

	it('should use the content within tags', () => {
		const input = '<div>pink</div> <b>cat</b> jumped over <a>green</a> <i>tree</i>';
		const result = decode(input);
		expect(result).toBe('pink cat jumped over green tree');
	});

	it('should trim leading and trailing whitespace', () => {
		const input = '   <p>Title &amp; More</p>   ';
		const result = decode(input);
		expect(result).toBe('Title & More');
	});

	it('should handle empty string with spaces and return empty string', () => {
		const input = '    ';
		const result = decode(input);
		expect(result).toBe('');
	});
});
