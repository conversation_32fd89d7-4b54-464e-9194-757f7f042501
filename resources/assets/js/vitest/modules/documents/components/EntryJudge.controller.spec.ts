import { beforeEach, describe, expect, it, vi } from 'vitest';
import { entryJudgeControllerFactory, View } from '@/modules/judging/components/EntryJudge.controller';

const dependenciesMock = {
	onMountedHook: vi.fn(),
};

const onMountedHookMock = vi.spyOn(dependenciesMock, 'onMountedHook');

vi.mock('@/lib/utils', () => ({
	getGlobal: vi.fn(),
	getGlobalData: vi.fn(),
}));

vi.mock('@/lib/store', () => ({
	default: {
		state: {
			comments: {},
		},
	},
}));

vi.mock('@/services/global/translations.interface', () => ({
	trans: (key: string) => key,
}));

vi.mock('@/services/global/features.interface', () => ({
	features: vi.fn(),
}));

const mockElement = {
	classList: {
		toggle: vi.fn(),
	},
	disabled: false,
};

global.document = {
	querySelectorAll: vi.fn().mockReturnValue([mockElement]),
};

beforeEach(() => {
	onMountedHookMock.mockReset();
});

describe('Entry Judge', () => {
	it('correctly initializes data', () => {
		const controllerFactory = entryJudgeControllerFactory(dependenciesMock);
		const controller: View = controllerFactory();

		expect(controller.buttonsDisabled.value).toBe(false);
		expect(onMountedHookMock).toHaveBeenCalledOnce();
	});

	it('correctly handles abstain toggled', () => {
		const controllerFactory = entryJudgeControllerFactory(dependenciesMock);
		const controller: View = controllerFactory();

		controller.handleAbstainToggled(true);

		expect(document.querySelectorAll).toHaveBeenCalledWith('.abstain-show');
		expect(document.querySelectorAll).toHaveBeenCalledWith('.abstain-hide');
		expect(document.querySelectorAll).toHaveBeenCalledWith('.abstain-disable:not(.locked-score)');

		expect(mockElement.classList.toggle).toHaveBeenCalledWith('hidden', false);
		expect(mockElement.disabled).toBe(true);
	});
});
