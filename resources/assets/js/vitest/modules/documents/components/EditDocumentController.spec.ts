import { beforeEach, describe, expect, it, vi } from 'vitest';
import { editDocumentControllerFactory, Props, View } from '@/modules/documents/components/EditDocument.controller';

const dependenciesMock = {
	onMountedHook: vi.fn(),
};

const onMountedHookMock = vi.spyOn(dependenciesMock, 'onMountedHook');

const supportedLanguagesMock = ['en_GB', 'fr_CA'];

const propsMock: Props = {
	name: 'edit-document',
	document: {
		shared: true,
		translations: [
			{ field: 'name', language: 'en_GB', value: 'Title in English' },
			{ field: 'name', language: 'fr_CA', value: 'Titre en français' },
		],
	},
};

vi.mock('@/lib/utils', () => ({
	supportedLanguages: () => supportedLanguagesMock,
}));

beforeEach(() => {
	onMountedHookMock.mockReset();
});

describe('Edit Document Controller', () => {
	it('correctly initializes data', () => {
		const controllerFactory = editDocumentControllerFactory(dependenciesMock);
		const controller: View = controllerFactory(propsMock);

		expect(controller.shared.value).toBe(true);
		expect(controller.supportedLanguages).toEqual(supportedLanguagesMock);
		expect(controller.translated.value).toEqual({});
		expect(onMountedHookMock).toHaveBeenCalledOnce();
	});

	it('correctly updates shared upon toggleShared call', () => {
		const controllerFactory = editDocumentControllerFactory(dependenciesMock);
		const controller: View = controllerFactory(propsMock);

		controller.toggleShared(false);

		expect(controller.shared.value).toBe(false);
	});

	it('correctly updates translated upon onTranslated call', () => {
		const controllerFactory = editDocumentControllerFactory(dependenciesMock);
		const controller: View = controllerFactory(propsMock);

		const updatedTranslated = [
			{ field: 'name', language: 'en_GB', value: 'New Title in English' },
			{ field: 'name', language: 'fr_CA', value: 'Nouveau Titre en français' },
		];

		controller.onTranslated(updatedTranslated);

		expect(controller.translated.value).toEqual(updatedTranslated);
	});

	it('correctly sets translations upon setTranslations call', () => {
		const controllerFactory = editDocumentControllerFactory(dependenciesMock);
		const controller: View = controllerFactory(propsMock);

		expect(controller.translated.value).toEqual({});

		controller.setTranslations();

		expect(controller.translated.value).toEqual({
			en_GB: { name: 'Title in English' },
			fr_CA: { name: 'Titre en français' },
		});
	});

	it('calls onMountedHook with correct callback', () => {
		const controllerFactory = editDocumentControllerFactory(dependenciesMock);
		const controller: View = controllerFactory(propsMock);

		expect(onMountedHookMock).toHaveBeenCalledWith(expect.any(Function));
		const callback = onMountedHookMock.mock.calls[0][0];

		// Before the callback is called, translated.value should be empty
		expect(controller.translated.value).toEqual({});

		callback();

		expect(controller.translated.value).toEqual({
			en_GB: { name: 'Title in English' },
			fr_CA: { name: 'Titre en français' },
		});
	});
});
