import { commentsServiceFactory } from '@/lib/components/Comments/commentsService';
import { computed } from 'vue';
import { afterEach, describe, expect, it, vi } from 'vitest';

let commits: string[] = [];
vi.mock('@/lib/store', () => ({
	default: {
		commit: (key: string, value: string) => {
			commits.push(key + ':' + value);
		},
		state: {
			comments: {
				commentsEdited: 0,
				pendingUploads: 2,
				userId: 99,
			},
		},
	},
}));

describe('CommentsService', () => {
	afterEach(() => {
		vi.clearAllMocks();
		commits = [];
	});

	it('gets userId', () => {
		const commentsService = commentsServiceFactory();
		const userId = computed(commentsService.userId);

		expect(userId.value).toBe(99);
	});

	it('increments commentsEdited', () => {
		const commentsService = commentsServiceFactory();

		expect(commits).toEqual([]);

		commentsService.changeCommentsEditedBy(1);

		expect(commits).toContain('comments/changeCommentsBeingEdited:1');
	});

	it('increments pendingUploads', () => {
		const commentsService = commentsServiceFactory();

		expect(commits).toEqual([]);

		commentsService.changePendingUploadsBy(2);

		expect(commits).toContain('comments/changePendingUploadsBy:2');
	});
});
