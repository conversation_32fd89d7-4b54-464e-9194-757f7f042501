import { Consumer } from '@/domain/models/Consumer';
import { FileProps } from '@/domain/models/File';
import { SetupContext } from 'vue';
import { UploadedFile } from '@/lib/types/UploadedFile';
import { useApi } from '@/modules/users/components/AvatarField.api';
import { useUploader } from '@/domain/services/Uploader/Uploader';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { avatarFieldController, Props, UploadOptions, View } from '@/modules/users/components/AvatarField.controller';

vi.mock('@/modules/users/Profile/ProfileServiceProvider', () => ({
	useProfileContainer: () => ({
		Consumer: () => ({
			consumer: {} as Consumer,
			updateImage: vi.fn(),
		}),
		onMounted: (f: () => void) => f(),
		onBeforeUnmount: () => {},
	}),
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		translations: {},
		language: { locale: 'en_GB' },
	},
}));

vi.mock('@/domain/services/Uploader/Uploader', () => ({
	useUploader: vi.fn(),
}));

vi.mock('@/modules/users/components/AvatarField.api', () => ({
	useApi: vi.fn(),
}));

let propsMock: Props;
describe('AvatarFieldController', () => {
	beforeEach(() => {
		propsMock = {
			uploadOptions: {} as UploadOptions,
			imageData: {} as FileProps,
		};
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should boot services', () => {
		avatarFieldController(propsMock, {} as SetupContext) as View;
		expect(useUploader).toHaveBeenCalledOnce();
		expect(useApi).toHaveBeenCalledOnce();
	});

	it('should return classes properly', () => {
		const view = avatarFieldController(propsMock, {} as SetupContext) as View;
		view.isDragging.value = true;
		view.isUploading.value = false;

		expect(view.classes.value).toStrictEqual({
			'dnd-element': true,
			'action-card': true,
			'full-width': true,
			'drag-enter': true,
		});

		view.isDragging.value = false;
		expect(view.classes.value).toStrictEqual({
			'dnd-element': true,
			'action-card': true,
			'full-width': true,
			'drag-enter': false,
		});

		view.isDragging.value = true;
		view.isUploading.value = true;
		expect(view.classes.value).toStrictEqual({
			'dnd-element': true,
			'action-card': true,
			'full-width': true,
			'drag-enter': false,
		});
	});

	it('should have editorModalIsOpen set to false initially', () => {
		const view = avatarFieldController(propsMock, {} as SetupContext) as View;
		expect(view.editorModalIsOpen.value).toBe(false);
	});

	it('should update uploadedFile size with processedFile size in handleProcess', async () => {
		const mockApi = {
			delete: vi.fn().mockResolvedValue(undefined),
			upload: vi.fn().mockResolvedValue({ token: 'token', file: 123 }),
		};
		(useApi as unknown as vi.Mock).mockReturnValue(mockApi);

		const mockUploaderService = { upload: vi.fn() };
		(useUploader as unknown as vi.Mock).mockReturnValue(mockUploaderService);

		const view = avatarFieldController(propsMock, {} as SetupContext) as View;

		const processedFile = {
			size: 1000000,
		} as File;

		// Set up uploadedFile
		view.uploadedFile.value = {
			size: 5000000,
		} as UploadedFile;

		const mockImage = { src: 'mock-image-src', addEventListener: vi.fn((x: unknown, cb) => cb()) };
		global.Image = vi.fn(() => mockImage as unknown as HTMLImageElement);

		const fileReaderMock = {
			addEventListener: vi.fn((x: unknown, cb) => cb()),
			readAsDataURL: vi.fn(),
			result: 'data:image/jpeg;base64,...',
		};
		vi.stubGlobal(
			'FileReader',
			vi.fn(() => fileReaderMock)
		);

		await view.handleProcess(processedFile);

		expect(view.uploadedFile.value.size).toBe(processedFile.size);
	});
});
