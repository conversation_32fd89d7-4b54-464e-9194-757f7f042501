import { checkComputed } from '@/../vitest/_common/ControllerHelpers';
import { SetupContext } from 'vue';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
	editorModalController,
	HandleProcessEventType,
	Props,
	View,
} from '@/modules/users/components/EditorModal.controller';
import { editorProps, useEditorStyles } from '@/modules/users/components/EditorModal.service';

global.File = class {
	constructor(public parts: string[], public name: string, public options: { type?: string; size?: number } = {}) {}
	get size() {
		return this.options.size || 0;
	}
};

vi.mock('@/modules/users/Profile/ProfileServiceProvider', () => ({
	useProfileContainer: () => ({
		onMounted: (f: () => void) => f(),
	}),
}));

vi.mock('@/modules/users/components/EditorModal.service', () => ({
	editorProps: {
		foo: 'bar',
	},
	useEditorStyles: vi.fn(),
}));

const uploadOptions = { maxFileSize: 1 };
const props = {
	uploadOptions,
};

describe('Editor ( pintura ) modal Controller', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('initializes styles from service', async () => {
		const view = editorModalController({} as Props, {} as SetupContext) as View;
		expect(view.editorProps).toStrictEqual(editorProps);
		expect(useEditorStyles).toHaveBeenCalledOnce();
		checkComputed(view)('editorStyles');
	});

	it('emits process event', async () => {
		const emit = vi.fn();
		const view = editorModalController(props as Props, { emit } as unknown as SetupContext) as View;
		view.handleProcess({ dest: 123 } as unknown as HandleProcessEventType);
		expect(emit).toHaveBeenCalledWith('process', 123);
	});

	it('emits cancel event', async () => {
		const emit = vi.fn();
		const view = editorModalController({} as Props, { emit } as unknown as SetupContext) as View;
		view.cancelEditorModal();
		expect(emit).toHaveBeenCalledWith('cancel');
	});

	it('sets error message if file is too big', async () => {
		const emit = vi.fn();
		const view = editorModalController(props as Props, { emit } as unknown as SetupContext) as View;
		const largeFile = new File(['dummy content'], 'test.png', { size: 2 * 1024 * 1024 }); // 2 MB file
		view.handleProcess({ dest: largeFile });
		expect(emit).not.toHaveBeenCalled();
		expect(view.errorMessage.value).toBe('files.avatar.max_file_size');
	});
});
