import { UploadedFile } from '@/lib/types/UploadedFile';
import { useApi } from '@/modules/users/components/AvatarField.api';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { DataRequestMethod, DataResponse, dataSource } from '@/domain/services/Api/DataSource';

vi.mock('@/domain/services/Api/Headers', async () => ({
	headersFactory: () => ({}),
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		routes: {
			'file.own.delete': 'delete/{id}',
		},
	},
}));

const api = useApi({
	upload: 'https://upload/',
	status: 'https://status/',
});

describe('AvatarField api', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('uploads file', async () => {
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: 'test-file-id' } as DataResponse<unknown>);

		const response = await api.upload({ foo: 'bar' } as unknown as UploadedFile);
		expect(dataSource.request).toHaveBeenCalledWith({
			url: 'https://upload/',
			method: DataRequestMethod.POST,
			data: { foo: 'bar' },
			headers: {},
		});
		expect(response).toBe('test-file-id');
	});

	it('gets status', async () => {
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: 'test-upload' } as DataResponse<unknown>);

		const response = await api.status(123);
		expect(dataSource.request).toHaveBeenCalledWith({
			url: 'https://status/123',
			method: DataRequestMethod.GET,
			data: undefined,
			headers: {},
		});
		expect(response).toBe('test-upload');
	});

	it('delets file', async () => {
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: 'test-delete' } as DataResponse<unknown>);

		const response = await api.delete(123);
		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/delete/123',
			method: DataRequestMethod.DELETE,
			data: undefined,
			headers: {},
		});
		expect(response).toBe('test-delete');
	});
});
