import { broadcastFormController } from '@/modules/broadcasts/components/BroadcastForm.controller';

describe('Referee Controller', () => {
	it('availableMergeFields should be default options if consolidated is false', () => {
		const props = {
			mergeFields: ['field1', 'field2'],
			consolidatedMergeFields: ['field3'],
			initialConsolidated: 0,
			initialTranslatedResource: {
				translated: {
					en_GB: {
						subject: 'Subject',
						body: 'Body',
					},
				},
			},
		};
		const view = broadcastFormController(props) as View;

		expect(view.availableMergeFields.value).toEqual(['field1', 'field2']);
	});

	it('availableMergeFields should use consolidated options if consolidated is true', () => {
		const props = {
			mergeFields: ['field1', 'field2'],
			consolidatedMergeFields: ['field3'],
			initialConsolidated: 1,
			initialTranslatedResource: {
				translated: {
					en_GB: {
						subject: 'Subject',
						body: 'Body',
					},
				},
			},
		};
		const view = broadcastFormController(props) as View;

		expect(view.availableMergeFields.value).toEqual(['field3']);
	});

	it('canConsolidate should return true if all used merge fields are in consolidated merge fields', () => {
		const props = {
			mergeFields: ['field1', 'field2'],
			consolidatedMergeFields: ['field3'],
			initialConsolidated: 0,
			initialTranslatedResource: {
				translated: {
					en_GB: {
						subject: '{field3}',
						body: '{field3}',
					},
				},
			},
		};
		const view = broadcastFormController(props) as View;

		expect(view.canConsolidate.value).toBe(true);
	});

	it('canConsolidate should return false if any used merge fields is not in consolidated merge fields', () => {
		const props = {
			mergeFields: ['field1', 'field2'],
			consolidatedMergeFields: ['field3'],
			initialConsolidated: 0,
			initialTranslatedResource: {
				translated: {
					en_GB: {
						subject: '{field1}',
						body: '{field3}',
					},
				},
			},
		};
		const view = broadcastFormController(props) as View;

		expect(view.canConsolidate.value).toBe(false);
	});

	it('canConsolidate should return false if any used merge fields is not in consolidated merge fields in any language', () => {
		const props = {
			mergeFields: ['field1', 'field2'],
			consolidatedMergeFields: ['field3'],
			initialConsolidated: 0,
			initialTranslatedResource: {
				translated: {
					en_GB: {
						subject: '{field3}',
						body: '{field3}',
					},
					fr_FR: {
						subject: '{field1}',
						body: '{field3}',
					},
				},
			},
		};
		const view = broadcastFormController(props) as View;

		expect(view.canConsolidate.value).toBe(false);
	});

	it('canConsolidate should return true if no merge fields are used', () => {
		const props = {
			mergeFields: ['field1', 'field2'],
			consolidatedMergeFields: ['field3'],
			initialConsolidated: 0,
			initialTranslatedResource: {
				translated: {
					en_GB: {
						subject: 'Subject',
						body: 'Body',
					},
				},
			},
		};
		const view = broadcastFormController(props) as View;

		expect(view.canConsolidate.value).toBe(true);
	});

	it('canConsolidate should return true if consolidated resource fields are used', () => {
		const props = {
			mergeFields: ['field1', 'field2'],
			consolidatedMergeFields: ['user_field:abcd1234'],
			initialConsolidated: 0,
			initialTranslatedResource: {
				translated: {
					en_GB: {
						subject: '{user_field:test}',
						body: 'Body',
					},
				},
			},
		};
		const view = broadcastFormController(props) as View;

		expect(view.canConsolidate.value).toBe(true);
	});

	it('canConsolidate should return false if non-consolidated resource fields are used', () => {
		const props = {
			mergeFields: ['entry_field:abcd1234'],
			consolidatedMergeFields: ['user_field:abcd1234'],
			initialConsolidated: 0,
			initialTranslatedResource: {
				translated: {
					en_GB: {
						subject: '{entry_field:test}',
						body: 'Body',
					},
				},
			},
		};
		const view = broadcastFormController(props) as View;

		expect(view.canConsolidate.value).toBe(false);
	});
});
