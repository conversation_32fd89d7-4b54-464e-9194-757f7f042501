import { afterEach, beforeEach, describe, expect, it, Mock, vi } from 'vitest';

import {
	Context,
	entryJudgeCommentsControllerFactory,
	Props,
	View,
} from '@/modules/judging/components/EntryJudgeComments.controller';

const dependenciesMock = {
	onMountedHook: vi.fn(),
	watchHook: vi.fn(),
};

const onMountedHookMock = vi.spyOn(dependenciesMock, 'onMountedHook');
const watchHookMock = vi.spyOn(dependenciesMock, 'watchHook');

vi.mock('@/lib/utils', () => ({
	getGlobal: vi.fn(),
}));

vi.mock('@/services/global/translations.interface', () => ({
	getTrans: (key: string) => key,
}));

const baseProps: Props = {
	canComment: true,
	checked: true,
	disabled: false,
	label: 'label',
	name: 'name',
	showCommentsButton: true,
	commentRequired: false,
	comments: {
		comments: [],
		labels: {
			placeholder: '',
			saveComment: '',
		},
		token: '',
		readOnly: false,
		commentCreateUrl: '',
		commentDeleteUrl: '',
		commentUpdateUrl: '',
		translations: '{}',
	},
};

const ctxMock: Context = {
	emit: ((...args: (string | boolean)[]) => {
		(ctxMock.emit as Context).calls.push(args);
	}) as Context,
};
(ctxMock.emit as Context).calls = [];

const view = entryJudgeCommentsControllerFactory(dependenciesMock)({ props: baseProps, ctx: ctxMock });

describe('entryJudgeCommentsController test suite', () => {
	beforeEach(() => {
		global.document = {
			querySelector: vi.fn(),
		} as unknown as Document;

		vi.restoreAllMocks();
		onMountedHookMock.mockReset();
		watchHookMock.mockReset();
	});

	afterEach(() => {
		global.document = {} as unknown as Document;

		vi.resetAllMocks();
	});

	it('should open modal', () => {
		const view: View = entryJudgeCommentsControllerFactory(dependenciesMock)({
			props: { ...baseProps, checked: false },
			ctx: ctxMock,
		});

		expect(view.modalIsOpen.value).toBe(false);

		view.openModal(true);

		expect(view.modalIsOpen.value).toBe(true);
	});

	it('should open modal when checking the checkbox', () => {
		const view: View = entryJudgeCommentsControllerFactory(dependenciesMock)({
			props: { ...baseProps, checked: false },
			ctx: ctxMock,
		});

		expect(view.modalIsOpen.value).toBe(false);
		expect(view.isChecked.value).toBe(false);

		view.toggleCheckbox(true);
		expect(view.isChecked.value).toBe(true);
		expect(view.modalIsOpen.value).toBe(true);
	});

	it('should uncheck the checkbox if there are no comments and comments are required', () => {
		const view: View = entryJudgeCommentsControllerFactory(dependenciesMock)({
			props: { ...baseProps, commentRequired: true },
			ctx: ctxMock,
		});

		expect(view.modalIsOpen.value).toBe(false);
		expect(view.isChecked.value).toBe(true);

		view.closeModal();

		expect(view.isChecked.value).toBe(false);
		expect(view.modalIsOpen.value).toBe(false);
	});

	it('should keep the checkbox checked if there are comments when closing the modal', () => {
		const view: View = entryJudgeCommentsControllerFactory(dependenciesMock)({
			props: { ...baseProps, comments: { ...baseProps.comments, comments: [{ id: 1 }] } },
			ctx: ctxMock,
		});

		expect(view.modalIsOpen.value).toBe(false);
		expect(view.isChecked.value).toBe(true);

		view.closeModal();

		expect(view.isChecked.value).toBe(true);
		expect(view.modalIsOpen.value).toBe(false);
	});

	it('should not uncheck the checkbox if comments are not required when closing the modal', () => {
		const view: View = entryJudgeCommentsControllerFactory(dependenciesMock)({ props: baseProps, ctx: ctxMock });

		expect(view.modalIsOpen.value).toBe(false);
		expect(view.isChecked.value).toBe(true);

		view.closeModal();

		expect(view.isChecked.value).toBe(true);
		expect(view.modalIsOpen.value).toBe(false);
	});

	it('should hide top shadow and display bottom shadow when the view is at the top', () => {
		const eventMock = { target: { scrollTop: 0, scrollHeight: 985, clientHeight: 747 } } as unknown as Event;

		view.handleShadows(eventMock);

		expect(view.showTopShadow.value).toBe(false);
		expect(view.showBottomShadow.value).toBe(true);
	});

	it('should display top and bottom shadow when the view is in the middle', () => {
		const eventMock = { target: { scrollTop: 125, scrollHeight: 985, clientHeight: 747 } } as unknown as Event;

		view.handleShadows(eventMock);

		expect(view.showTopShadow.value).toBe(true);
		expect(view.showBottomShadow.value).toBe(true);
	});

	it('should display top shadow and hide bottom shadow when the view is at the bottom', () => {
		const eventMock = { target: { scrollTop: 238, scrollHeight: 985, clientHeight: 747 } } as unknown as Event;

		view.handleShadows(eventMock);

		expect(view.showTopShadow.value).toBe(true);
		expect(view.showBottomShadow.value).toBe(false);
	});

	it('should change the z-index of the .header-controls on modal toggle', () => {
		const mockStyle = {
			zIndex: undefined,
		};

		(global.document.querySelector as Mock).mockReturnValue({
			style: mockStyle,
		});

		view.openModal(true);
		expect(global.document.querySelector).toHaveBeenCalledWith('.header-controls');
		expect(mockStyle.zIndex).toBe('1050');

		view.closeModal();
		expect(global.document.querySelector).toHaveBeenCalledWith('.header-controls');
		expect(mockStyle.zIndex).toBe('10');
	});

	it('calls onMountedHook with correct callback', () => {
		const view = entryJudgeCommentsControllerFactory(dependenciesMock)({ props: baseProps, ctx: ctxMock });

		expect(onMountedHookMock).toHaveBeenCalledWith(expect.any(Function));

		const callback = onMountedHookMock.mock.calls[0][0];
		expect(view.isChecked.value).to.be.true;

		callback();

		expect(ctxMock.emit.calls).toContainEqual(['abstain-toggled', true]);
	});

	it('should emit abstain-toggled event when isChecked changes', async () => {
		const view = entryJudgeCommentsControllerFactory(dependenciesMock)({
			props: { ...baseProps, isChecked: false },
			ctx: ctxMock,
		});

		expect(watchHookMock).not.toHaveBeenCalled;

		const callback = watchHookMock.mock.calls[0][1];
		view.isChecked.value = true;

		callback();

		expect(watchHookMock).toHaveBeenCalled;
		expect(ctxMock.emit.calls).toContainEqual(['abstain-toggled', true]);
	});
});
