import AttachFile from '@/lib/components/Shared/editor/ckeditor/plugins/AttachFile';
import { type Editor } from '@/lib/components/Shared/editor/TextEditor.types';
import ForceHttpsLinks from '@/lib/components/Shared/editor/ckeditor/plugins/ForceHttpsLinks';
import { getGlobalData } from '@/lib/utils';
import ImageBlockInlineStyle from '@/lib/components/Shared/editor/ckeditor/plugins/ImageBlockInlineStyle';
import { pipe } from 'fp-ts/function';
import vimeo from '@/lib/components/Shared/editor/ckeditor/providers/vimeo';
import youtube from '@/lib/components/Shared/editor/ckeditor/providers/youtube';
import {
	Alignment,
	Autoformat,
	BlockQuote,
	Bold,
	Code,
	DecoupledEditor,
	Essentials,
	Heading,
	ImageBlock,
	ImageCaption,
	ImageInsertViaUrl,
	ImageResize,
	ImageResizeHandles,
	Indent,
	IndentBlock,
	Italic,
	Link,
	List,
	ListProperties,
	MediaEmbed,
	Paragraph,
	PasteFromOffice,
	Strikethrough,
	Subscript,
	Superscript,
	Table,
	Underline,
	Undo,
} from 'ckeditor5';
import type { EditorConfig, ToolbarConfig } from 'ckeditor5';
import { isArray, isString } from '@/domain/utils/TypePredicates';

/**
 * Returns the locale in ISO 639-1 format for Right–to–left (RTL) languages support.
 *
 * @see https://ckeditor.com/docs/ckeditor5/latest/features/ui-language.html
 */
const getLocaleCode = (): string => {
	const { locale } = getGlobalData('language');
	return locale.split('_')[0];
};

// Essential plugins that are always included, regardless of toolbar configuration
const corePlugins: EditorConfig['plugins'] = [Essentials, Paragraph];

// Default plugins included when a toolbar configuration is provided
const standardPlugins: EditorConfig['plugins'] = [
	Alignment,
	Autoformat,
	PasteFromOffice,
	Strikethrough,
	Subscript,
	Superscript,
	Table,
];

// Mapping of toolbar items to their corresponding plugins
const toolbarPluginMap: Record<string, EditorConfig['plugins']> = {
	undo: [Undo],
	redo: [Undo],
	heading: [Heading],
	bold: [Bold],
	italic: [Italic],
	underline: [Underline],
	link: [Link, ForceHttpsLinks],
	blockQuote: [BlockQuote],
	code: [Code],
	bulletedList: [List, ListProperties],
	numberedList: [List, ListProperties],
	outdent: [Indent, IndentBlock],
	indent: [Indent, IndentBlock],
	mediaEmbed: [MediaEmbed],
	insertImage: [ImageBlock, ImageInsertViaUrl, ImageBlockInlineStyle, ImageCaption, ImageResize, ImageResizeHandles],
	attachFile: [AttachFile],
};

const cleanToolbarItems = (toolbarConfig?: ToolbarConfig): string[] => {
	if (!toolbarConfig || isArray(toolbarConfig)) return [];
	return (toolbarConfig.items ?? [])
		.filter((item): item is string => isString(item) && item !== '|')
		.filter((item) => !toolbarConfig?.removeItems?.includes(item));
};

const mapToolbarItemsToPlugins = (items: string[]): EditorConfig['plugins'] =>
	items.flatMap((item) => toolbarPluginMap[item] ?? []);

const combinePlugins = (toolbarItems: string[]): EditorConfig['plugins'] => [
	...corePlugins,
	...(toolbarItems.length > 0 ? standardPlugins : []),
	...(mapToolbarItemsToPlugins(toolbarItems) ?? []),
];

const uniquePlugins = (plugins: EditorConfig['plugins']): EditorConfig['plugins'] => [...new Set(plugins)];

const configureEditorPlugins = (toolbarConfig?: ToolbarConfig): EditorConfig['plugins'] =>
	pipe(toolbarConfig, cleanToolbarItems, combinePlugins, uniquePlugins);
const ckeditorConfig = (options: Partial<EditorConfig> = {}): EditorConfig => ({
	licenseKey: process.env.CKEDITOR_LICENSE_KEY,
	language: {
		ui: getGlobalData('language'),
		content: getLocaleCode(),
	},
	ui: {
		poweredBy: {
			forceVisible: false,
		},
	},
	plugins: configureEditorPlugins(options?.toolbar),
	link: {
		addTargetToExternalLinks: true, // Auto add target="_blank" and rel="noopener noreferrer" to external links
		defaultProtocol: 'https://',
		allowedProtocols: ['https', 'mailto', 'tel'],
		decorators: {
			isRelative: {
				mode: 'automatic',
				callback: (url) => !!url?.startsWith('/'),
				attributes: {
					target: '_blank',
					rel: 'noopener noreferrer',
					'data-redirector': '',
				},
			},
			withDataRedirector: {
				mode: 'automatic',
				attributes: {
					'data-redirector': '',
				},
				callback: (url) => !!(url?.startsWith('http://') || url?.startsWith('https://') || url?.startsWith('/')),
			},
		},
	},
	mediaEmbed: {
		previewsInData: true,
		providers: [youtube, vimeo],
	},
	heading: {
		options: [
			{ model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
			{ model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
			{ model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
			{ model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
			{ model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' },
			{ model: 'heading5', view: 'h5', title: 'Heading 5', class: 'ck-heading_heading5' },
			{ model: 'heading6', view: 'h6', title: 'Heading 6', class: 'ck-heading_heading6' },
		],
	},
	image: {
		toolbar: ['toggleImageCaption', 'imageTextAlternative'],
		insert: { type: 'block' },
	},
	list: {
		properties: {
			startIndex: true,
			styles: false,
		},
	},
	...options, // Spread additional options to override the default configuration
});

const initCKEditor = async ({
	element,
	toolbar,
	callback,
}: {
	element: HTMLElement | string;
	toolbar: ToolbarConfig;
	callback: (newEditor: Editor) => void;
}) => {
	DecoupledEditor.create(element, ckeditorConfig({ toolbar }))
		.then((editor: Editor) => callback(editor))
		.catch((error) => console.error(error.stack));
};

export { initCKEditor };
