import { apiRequest } from '@/modules/billing/services/Api';
import toastr from 'toastr';
import { upgradeSubscriptionPlan } from '@/modules/billing/components/subscriptions/SubscriptionsUpgradePlan.api';
import { Plan, Subscription } from '@/modules/billing/Billing.types';
import { Ref, ref, SetupFunction } from 'vue';
import { trans, Trans } from '@/domain/dao/Translations';

type Props = {
	subscription: Subscription;
};

type View = {
	lang: Trans;
	inViewMode: Ref<boolean>;
	plan: Ref<Plan | null>;
	error: Ref<string | null>;
	toggleViewMode: () => void;
	save: () => void;
};

const subscriptionsUpgradePlanController: SetupFunction<Props, View> = (props): View => {
	const lang = trans();
	const inViewMode = ref(true);
	const plan = ref(null);
	const error = ref(null);

	const toggleViewMode = () => {
		plan.value = null;
		error.value = null;
		inViewMode.value = !inViewMode.value;
	};

	const save = async () => {
		if (plan.value === null) {
			error.value = lang.get('billing.validation.required');
			return;
		}

		await apiRequest(
			upgradeSubscriptionPlan({
				subscriptionId: props.subscription.id,
				plan: plan.value,
				currentPlan: props.subscription.planName,
			})
				.then(() => {
					window.location.href = '/billing?message=account-upgraded';
				})
				.catch(() => toastr.error(lang.get('miscellaneous.alerts.generic')))
		);
	};

	return {
		lang,
		inViewMode,
		plan,
		error,
		toggleViewMode,
		save,
	};
};

export { Props, View, subscriptionsUpgradePlanController };
