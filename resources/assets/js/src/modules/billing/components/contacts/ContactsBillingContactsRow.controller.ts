import { apiRequest } from '@/modules/billing/services/Api';
import { BillingContact } from '@/modules/billing/Billing.types';
import emailValidator from '@/lib/components/Fields/validator/email';
import requiredValidator from '@/lib/components/Fields/validator/required';
import toastr from 'toastr';
import {
	ContactsBillingContactsRowEmitters,
	ContactsBillingContactsRowEvents,
} from '@/modules/billing/components/contacts/ContactsBillingContactsRow.events';
import { deleteBillingContact, save } from '@/modules/billing/components/contacts/ContactsBillingContactsRow.api';
import { Ref, ref, SetupFunction } from 'vue';
import { Trans, trans } from '@/domain/dao/Translations';

type Props = {
	billingContact: BillingContact;
	canEdit: boolean;
	viewMode: boolean;
	customerId: string;
};

enum Error {
	FIRSTNAME = 'firstName',
	LASTNAME = 'lastName',
	EMAIL = 'email',
}

type ErrorMap = Partial<{ [key in Error]: string }>;

type View = {
	lang: Trans;
	inViewMode: Ref<boolean>;
	editable: Ref<boolean>;
	viewContact: Ref<BillingContact | null>;
	editContact: Ref<BillingContact | null>;
	errors: Ref<ErrorMap>;
	showConfirmDelete: Ref<boolean>;
	toggleEditContactForm: () => void;
	saveContact: () => void;
	resetForm: () => void;
	hideDeleteConfirmationDialog: () => void;
	deleteContact: () => void;
	showDeleteConfirmationDialog: () => void;
	errorExists: (field: Error) => boolean;
};

type Ctx = {
	emit: ContactsBillingContactsRowEmitters;
};

const contactsBillingContactsRowController: SetupFunction<Props, View> = (props, { emit }: Ctx): View => {
	const lang = trans();

	const inViewMode = ref(props.viewMode);
	const editable = ref(props.canEdit);
	const viewContact = ref(props.billingContact);
	if (!props.billingContact.customerId) {
		viewContact.value.customerId = props.customerId;
	}

	const editContact = ref({ ...viewContact.value });
	const errors: Ref<ErrorMap> = ref({});
	const showConfirmDelete = ref(false);

	const errorExists = (field: Error) => errors.value[field] !== undefined;

	const toggleEditContactForm = () => {
		inViewMode.value = false;
	};

	const saveContact = async () => {
		if (!validateForm()) {
			return;
		}

		await apiRequest<BillingContact>(
			save(editContact.value)
				.then((response) => handleSaveAjaxResponse(response))
				.catch(() => toastr.error(lang.get('billing.account_contacts.messages.save_contact_error')))
		);
	};

	const handleSaveAjaxResponse = (response: BillingContact) => {
		emit(ContactsBillingContactsRowEvents.ContactSaved, editContact.value);
		editContact.value.id = response.id;
		viewContact.value = { ...editContact.value };
		inViewMode.value = true;
	};

	const handleDeleteAjaxResponse = () => {
		emit(ContactsBillingContactsRowEvents.ContactDeleted, viewContact.value);
		hideDeleteConfirmationDialog();
	};

	const showDeleteConfirmationDialog = () => {
		showConfirmDelete.value = true;
	};

	const hideDeleteConfirmationDialog = () => {
		showConfirmDelete.value = false;
	};

	const deleteContact = async () =>
		apiRequest(
			deleteBillingContact(props.customerId, props.billingContact.id)
				.then(() => handleDeleteAjaxResponse())
				.catch(() => toastr.error(lang.get('billing.account_contacts.messages.delete_contact_error')))
		);

	const resetForm = () => {
		editContact.value = { ...viewContact.value };
		errors.value = {};
		inViewMode.value = true;
		emit(ContactsBillingContactsRowEvents.FormClosed);
	};

	const validateForm = () => {
		errors.value = {};

		if (!requiredValidator(editContact.value.firstName)) {
			errors.value[Error.FIRSTNAME] = lang.get('billing.validation.required');
		}

		if (!requiredValidator(editContact.value.lastName)) {
			errors.value[Error.LASTNAME] = lang.get('billing.validation.required');
		}

		if (requiredValidator(editContact.value.email)) {
			if (!emailValidator(editContact.value.email)) {
				errors.value[Error.EMAIL] = lang.get('billing.validation.email');
			}
		} else {
			errors.value[Error.EMAIL] = lang.get('billing.validation.required');
		}

		return Object.keys(errors.value).length === 0;
	};

	return {
		lang,
		inViewMode,
		editable,
		viewContact,
		editContact,
		errors,
		showConfirmDelete,
		toggleEditContactForm,
		saveContact,
		resetForm,
		hideDeleteConfirmationDialog,
		deleteContact,
		showDeleteConfirmationDialog,
		errorExists,
	};
};

export { Props, View, contactsBillingContactsRowController };
