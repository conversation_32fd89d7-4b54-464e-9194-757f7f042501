import { apiRequest } from '@/modules/billing/services/Api';
import { isUndefined } from '@/domain/utils/TypePredicates';
import requiredValidator from '@/lib/components/Fields/validator/required';
import toastr from 'toastr';
import { useVariablesDao } from '@/domain/dao/Variables';
import { computed, ComputedRef, Ref, ref, SetupFunction, watch } from 'vue';
import { Countries, Organisation, Regions } from '@/modules/billing/Billing.types';
import {
	InvoicesOrganisationAddressEmitters,
	InvoicesOrganisationAddressEvents,
} from '@/modules/billing/components/invoices/InvoicesOrganisationAddress.events';
import { Trans, trans } from '@/domain/dao/Translations';
import {
	updateOrganisationAddress,
	UpdateOrganisationAddressResponse,
} from '@/modules/billing/components/invoices/InvoicesOrganisationAddress.api';

type Props = {
	organisation: Organisation;
	countries: Countries;
	vatCountries: Countries;
};

enum AddressField {
	ADDRESS = 'address',
	CITY = 'city',
	REGION = 'region',
	POSTAL_CODE = 'postalCode',
	COUNTRY_CODE = 'countryCode',
	VAT_NUMBER = 'vatNumber',
}

type ErrorMap = { [key in AddressField]?: string[] };

type AjaxError = {
	response: {
		data: {
			[key in AddressField]?: string[];
		};
	};
	message: string;
};

type View = {
	lang: Trans;
	regions: Ref<Regions>;
	viewData: Ref<Organisation | null>;
	editData: Ref<Organisation | null>;
	errors: Ref<ErrorMap>;
	inViewMode: Ref<boolean>;
	showRegion: ComputedRef<boolean>;
	validationMessage: ComputedRef<{ message: string; type: string } | null>;
	showVatInput: ComputedRef<boolean>;
	removeCountryCode: (event: Event) => void;
	resetErrors: (error: AddressField) => void;
	toggleViewMode: () => void;
	updateEditDataRegion: (region: string) => void;
	updateEditDataCountry: (event: Event) => void;
	saveOrganisationAddress: () => void;
	// eslint-disable-next-line @typescript-eslint/naming-convention
	AddressField: typeof AddressField;
};

type Ctx = {
	emit: InvoicesOrganisationAddressEmitters;
};

const variables = useVariablesDao<{
	regions: Regions;
}>();

const invoicesOrganisationAddressController: SetupFunction<Props, View> = (props, { emit }: Ctx): View => {
	const lang = trans();

	const regions = ref(variables.regions);
	const viewData = ref(props.organisation);
	const editData = ref({ ...viewData.value });
	const errors: Ref<ErrorMap> = ref({});
	const inViewMode = ref(true);
	const errorMessage = ref<string | null>(null);

	const showRegion = computed(() => Object.prototype.hasOwnProperty.call(regions.value, editData.value.countryCode));

	const validationMessage = computed(() => {
		if (errorMessage.value) {
			return {
				message: errorMessage.value,
				type: 'error',
			};
		}

		return null;
	});

	watch(showRegion, (newVal) => {
		if (!newVal) {
			editData.value.region = null;
		}
	});

	const showVatInput = computed(() => !isUndefined(props.vatCountries[editData.value.countryCode]));

	const removeCountryCode = (event: Event) => {
		const element = event.target as HTMLInputElement;
		const vatNumber = element.value;

		if (/^[A-Za-z]{2}/.test(vatNumber)) {
			editData.value.vatNumber = vatNumber.replace(/^[A-Za-z]{2}/, '');
		}
	};

	const validateForm = () => {
		errors.value = {};
		errorMessage.value = null;

		if (!requiredValidator(editData.value.countryCode)) {
			errors.value[AddressField.COUNTRY_CODE] = lang.get('billing.validation.required');
		}

		if (showRegion.value && !requiredValidator(editData.value.region)) {
			errors.value[AddressField.REGION] = lang.get('billing.validation.required');
		}

		return Object.keys(errors.value).length === 0;
	};

	const toggleViewMode = () => {
		editData.value = { ...viewData.value };
		showHideForm();
	};

	const handleSaveAjaxResponse = (response: UpdateOrganisationAddressResponse) => {
		viewData.value = { ...editData.value };
		showHideForm();
		const address = response.data;
		emit(InvoicesOrganisationAddressEvents.AddressUpdated, address);
	};

	const showHideForm = () => {
		errors.value = {};
		errorMessage.value = null;
		inViewMode.value = !inViewMode.value;
	};

	const handleAjaxErrors = (error: AjaxError) => {
		errors.value = { ...error.response.data };
		errorMessage.value = error.message;
		toastr.error(lang.get('billing.invoices.messages.save_address_error'));
	};

	const updateEditDataCountry = (event: Event) => {
		const element = event.target as HTMLSelectElement;
		editData.value.country = props.countries[element.value];

		if (!isUndefined(errors.value.countryCode)) {
			delete errors.value.countryCode;
		}
	};

	const updateEditDataRegion = (region: string) => {
		editData.value.region = region;

		if (!isUndefined(errors.value.region)) {
			delete errors.value.region;
		}
	};

	const resetErrors = (error: AddressField) => {
		if (!isUndefined(errors.value[error])) {
			delete errors.value[error];
		}
	};

	const saveOrganisationAddress = async () => {
		if (!validateForm()) {
			return;
		}

		try {
			const response = await apiRequest<UpdateOrganisationAddressResponse>(updateOrganisationAddress(editData.value));
			handleSaveAjaxResponse(response);
		} catch (error: unknown) {
			handleAjaxErrors(error as AjaxError);
		}
	};

	return {
		AddressField,
		lang,
		regions,
		viewData,
		editData,
		errors,
		inViewMode,
		showRegion,
		validationMessage,
		showVatInput,
		removeCountryCode,
		resetErrors,
		toggleViewMode,
		updateEditDataRegion,
		updateEditDataCountry,
		saveOrganisationAddress,
	};
};

export { Props, View, invoicesOrganisationAddressController };
