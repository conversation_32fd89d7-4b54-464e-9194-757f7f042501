import { apiRequest } from '@/modules/billing/services/Api';
import toastr from 'toastr';
import { billingUIBus, BillingUISignals } from '@/modules/billing/signals/Billing';
import { Card, Organisation, Subscription } from '@/modules/billing/Billing.types';
import {
	CardComponent,
	ChargeBeeType,
	getChargebee,
	PaymentIntent,
} from '@/modules/billing/components/invoices/InvoicesPaymentMethod.service';
import { computed, ComputedRef, Ref, ref, SetupFunction } from 'vue';
import {
	fetchPaymentIntent,
	PaymentIntentResponse,
	updatePaymentMethod,
	UpdatePaymentMethodRequest,
	UpdatePaymentMethodResponse,
} from '@/modules/billing/components/invoices/InvoicesPaymentMethod.api';
import { trans, Trans } from '@/domain/dao/Translations';

type Props = {
	organisation: Organisation;
	subscription: Subscription;
};

type View = {
	lang: Trans;
	inViewMode: Ref<boolean>;
	save: () => void;
	toggleViewMode: () => void;
	chargebee: ChargeBeeType;
	clearCardForm: Ref<boolean>;
	cardIsOk: Ref<boolean>;
	cardComponent: Ref<CardComponent | null>;
	disableSaveButton: ComputedRef<boolean>;
	setCardComponent: (cardComponent: CardComponent) => void;
	setCardIsOk: (cardStatus: boolean) => void;
	card: Ref<Card | null>;
};

const invoicesPaymentMethodController: SetupFunction<Props, View> = (props): View => {
	const lang = trans();
	const card = ref(props.subscription?.card || null);
	const chargebee = getChargebee({
		site: props.subscription.gatewayConfiguration.site,
		domain: props.subscription.gatewayConfiguration.domain,
		publishableKey: props.subscription.gatewayConfiguration['publishable-key'],
	});
	const clearCardForm = ref(false);
	const cardIsOk = ref(false);
	const cardComponent: Ref<CardComponent | null> = ref(null);
	const inViewMode = ref(true);
	const disableSaveButton = computed(() => !cardIsOk.value);

	const getPaymentIntent = async (): Promise<PaymentIntent> => {
		const parameters = {
			customer_id: props.subscription.customerId,
			currencyCode: props.subscription.currency,
			plan: props.subscription.planName,
			billingName: props.organisation.name,
			address: props.organisation.address,
			city: props.organisation.city,
			region: props.organisation.region,
			postcode: props.organisation.postalCode,
			country: props.organisation.countryCode,
			currency: props.subscription.currency,
			vatNumber: props.organisation.vatNumber,
			currentProduct: props.subscription.currentProduct,
		};

		// Ensure cardComponent is not null and authorizeWith3ds is called
		if (!cardComponent.value) {
			throw new Error(lang.get('billing.invoices.payment_method.messages.card_not_set'));
		}

		// Fetch payment intent response and ensure it's a valid PaymentIntentResponse
		const paymentIntentResponse = await apiRequest<PaymentIntentResponse>(fetchPaymentIntent(parameters), true, false);

		if (!paymentIntentResponse || !paymentIntentResponse.payment_intent) {
			throw new Error(lang.get('billing.invoices.payment_method.messages.card_unverified'));
		}

		const paymentIntent = await cardComponent.value.authorizeWith3ds(paymentIntentResponse.payment_intent).catch(() => {
			throw new Error(lang.get('billing.invoices.payment_method.messages.card_unverified'));
		});

		if (!paymentIntent || paymentIntent.status !== 'authorized') {
			throw new Error(lang.get('billing.invoices.payment_method.messages.card_unauthorised'));
		}

		return paymentIntent;
	};

	const save = async () => {
		try {
			const paymentIntent = await getPaymentIntent();
			if (paymentIntent.customer_id && paymentIntent.id) {
				await savePaymentMethod({
					customerId: paymentIntent.customer_id,
					paymentIntentId: paymentIntent.id,
				});
			}
		} catch (error) {
			toastr.error((error as Error).message);
		} finally {
			billingUIBus.emit(BillingUISignals.HIDE_LOADING);
		}
	};

	const savePaymentMethod = async (parameters: UpdatePaymentMethodRequest): Promise<UpdatePaymentMethodResponse> =>
		apiRequest<UpdatePaymentMethodResponse>(
			updatePaymentMethod(parameters)
				.then((response) => handleUpdatePaymentMethodResponse(response))
				.catch(() => toastr.error(lang.get('billing.invoices.payment_method.messages.update_error'))),
			false,
			true
		);

	const toggleViewMode = () => showHideForm();

	const showHideForm = () => {
		inViewMode.value = !inViewMode.value;
		cardIsOk.value = false;
	};

	const setCardComponent = (card: CardComponent) => (cardComponent.value = card);

	const setCardIsOk = (cardStatus: boolean) => (cardIsOk.value = cardStatus);

	const handleUpdatePaymentMethodResponse = (response: UpdatePaymentMethodResponse) => {
		clearCardForm.value = true;
		cardIsOk.value = false;
		card.value = response.data;
		showHideForm();
	};

	return {
		lang,
		inViewMode,
		save,
		toggleViewMode,
		chargebee,
		clearCardForm,
		cardIsOk,
		cardComponent,
		disableSaveButton,
		setCardComponent,
		setCardIsOk,
		card,
	};
};

export { Props, View, invoicesPaymentMethodController };
