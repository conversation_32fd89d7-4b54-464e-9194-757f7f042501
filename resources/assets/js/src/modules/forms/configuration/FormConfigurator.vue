<template>
	<tabbed-content :tabs="tabs">
		<template slot="general">
			<div class="col-xs-12 row">
				<div class="row">
					<div class="col-xs-12 col-md-6">
						<div class="field-container">
							<form-config-general
								v-model="generalData"
								:form-types="types"
								:form="form"
								:trial-account="trialAccount"
								@input="typeChanged"
							/>
							<input v-if="!multiChapter" type="hidden" value="all" name="chapterOption" />
						</div>
					</div>
				</div>
			</div>
		</template>

		<template slot="cover">
			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-12 col-sm-6 col-lg-4">
						<div class="field-container">
							<form-config-cover v-model="coverData" :form="form" />
						</div>
					</div>
					<div class="col-xs-12 col-sm-6 col-lg-4">
						<div class="panel panel-default">
							<div class="panel-body">
								<div class="panel-title">
									<h4><i class="af-icons af-icons-preview preview-icon"></i>{{ lang.get('form.form.preview.label') }}</h4>
								</div>
								<form-widget :cover-image-url="coverImageUrl" :form-name="formName" :call-to-action="callToAction" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</template>

		<template v-if="multiChapter && is('entry')" slot="chapters">
			<div class="col-xs-12 col-md-6">
				<form-config-chapters :form="form" :chapters="chapters" />
			</div>
		</template>

		<template v-if="is('entry')" slot="rounds">
			<div class="col-xs-12 col-md-6">
				<form-config-rounds :form="form" :rounds="rounds" :translated="translated" :form-name="formName" />
			</div>
		</template>

		<template slot="advanced">
			<div class="col-xs-12 col-md-6">
				<form-config-advanced
					:settings="form.settings"
					:form-type="form.type"
					:content-blocks="formContentBlocks"
					:selected-content-block="form.contentBlock"
					:form-roles="formRoles"
					:selected-roles="form.selectedRoles"
				/>
			</div>
		</template>

		<template slot-scope="allTabs">
			<div class="row">
				<div class="col-xs-12">
					<div v-if="stage === 'new'" class="form-actions">
						<button key="b1" type="submit" class="btn btn-primary btn-lg" :disabled="!valid">
							{{ lang.get('buttons.saveNext') }}
						</button>
					</div>

					<div v-else-if="stage === 'continued'" class="form-actions">
						<button
							v-if="allTabs.hasNextTab"
							key="b2"
							class="btn btn-primary btn-lg"
							type="button"
							@click="allTabs.selectNext"
						>
							{{ lang.get('buttons.next') }}
						</button>
						<button v-else key="b3" type="submit" class="btn btn-primary btn-lg" :disabled="!valid">
							{{ lang.get('buttons.saveNext') }}
						</button>
						<br />
						<a v-if="allTabs.hasPrevTab" class="form-back" @click="allTabs.selectPrev">
							<i class="af-icons af-icons-arrow-tail-left"></i>Back
						</a>
					</div>

					<div v-else class="form-actions">
						<button key="b4" type="submit" class="btn btn-primary btn-lg" :disabled="!valid">
							{{ lang.get('buttons.save') }}
						</button>
						<button key="b5" type="button" class="btn btn-tertiary btn-lg" @click="onCancel">
							{{ lang.get('buttons.cancel') }}
						</button>
					</div>
				</div>
				<input v-if="!multiChapter" type="hidden" value="all" name="chapterOption" />
			</div>
		</template>
	</tabbed-content>
</template>

<script>
import TabbedContent from '@/lib/components/TabbedContent';

import langMixin from '@/lib/components/Translations/mixins/lang-mixin.js';
import linksMixin from '@/lib/components/Shared/mixins/links-mixin.js';
import featuresMixin from '@/lib/components/Shared/mixins/features-mixin.js';
import FormConfigGeneral from '@/lib/components/Forms/FormConfigGeneral';
import FormConfigCover from '@/lib/components/Forms/FormConfigCover';
import FormConfigChapters from '@/lib/components/Forms/FormConfigChapters';
import FormConfigRounds from '@/lib/components/Forms/FormConfigRounds';
import FormWidget from '@/lib/components/Forms/FormWidget';
import { stripTags } from '@/lib/string';
import { mapMutations } from 'vuex';
import FormConfigAdvanced from '@/lib/components/Forms/FormConfigAdvanced.vue';

export default {
	name: 'FormConfigurator',
	components: {
		TabbedContent,
		FormConfigGeneral,
		FormConfigChapters,
		FormConfigCover,
		FormConfigRounds,
		FormWidget,
		FormConfigAdvanced,
	},
	mixins: [langMixin, linksMixin, featuresMixin],
	props: {
		form: {
			type: Object,
			required: true,
		},
		types: {
			type: Array,
			required: true,
		},
		chapters: {
			type: Array,
			required: true,
		},
		rounds: {
			type: Array,
			required: true,
		},
		routes: {
			type: Object,
			default: () => ({}),
		},
		multiChapter: {
			type: Boolean,
			default: true,
		},
		multiForm: {
			type: Boolean,
			default: true,
		},
		stage: {
			type: String,
			required: true,
		},
		supportedLanguages: {
			type: Array,
			default: () => [],
		},
		typeLocked: {
			type: Boolean,
			default: true,
		},
		selectedType: {
			type: String,
			default: null,
		},
		trialAccount: {
			type: Boolean,
			default: false,
		},
		formContentBlocks: {
			type: Array,
			default: () => [],
		},
		formRoles: {
			type: Array,
			default: () => [],
		},
	},
	data() {
		return {
			generalData: null,
			coverData: null,
			formValue: this.form,
		};
	},
	computed: {
		formSettings() {
			return this.form.settings || {};
		},
		formName() {
			if (!this.generalData || !this.generalData.translated || !this.generalData.translated[this.defaultLanguage])
				return null;

			return this.generalData.translated[this.defaultLanguage].name || null;
		},
		defaultCallToAction() {
			return stripTags(this.lang.get('entries.titles.start'));
		},
		callToAction() {
			if (!this.coverData || !this.coverData.translated || !this.coverData.translated[this.defaultLanguage])
				return this.defaultCallToAction;

			return this.coverData.translated[this.defaultLanguage].callToAction || this.defaultCallToAction;
		},
		coverImageUrl() {
			return this.coverData ? this.coverData.preview || null : null;
		},
		translated() {
			if (!this.generalData) {
				return {
					name: {
						[this.defaultLanguage]: 'form',
					},
				};
			}

			const translated = { name: {} };
			Object.keys(this.generalData.translated).forEach(
				(lang) => (translated.name[lang] = this.generalData.translated[lang].name)
			);
			return translated;
		},
		tabs() {
			const general = {
				id: 'general',
				name: this.lang.get('form.configuration.details.label'),
				active: true,
			};
			const cover = {
				id: 'cover',
				name: this.lang.get('form.configuration.cover.label'),
				disabled: !this.form.id,
			};
			const chapters = {
				id: 'chapters',
				name: this.lang.get('form.configuration.chapters.label'),
				disabled: !this.form.id,
			};
			const rounds = {
				id: 'rounds',
				name: this.lang.get('form.configuration.rounds.label'),
				disabled: !this.form.id,
			};
			const advanced = {
				id: 'advanced',
				name: this.lang.get('form.configuration.advanced.label'),
				disabled: !this.form.id,
			};

			if (this.form.type === 'entry') {
				return this.showChapters ? [general, cover, chapters, rounds, advanced] : [general, cover, rounds, advanced];
			}

			return [general, advanced];
		},
		buttonLabel() {
			return this.fresh ? this.lang.get('buttons.saveNext') : this.lang.get('buttons.saveClose');
		},
		uploadInProgress() {
			return this.coverData ? this.coverData.uploadStatus === 'uploading' : false;
		},
		valid() {
			if (this.generalData) {
				return (
					this.generalData &&
					Object.keys(this.generalData.translated).find((v) => this.generalData.translated[v].name.length) &&
					!this.uploadInProgress
				);
			}

			return false;
		},
		fresh() {
			return !this.form.id;
		},
		showChapters() {
			return this.multiChapter;
		},
	},
	provide() {
		return {
			routes: this.routes,
		};
	},
	methods: {
		...mapMutations('global', ['storeGlobalState']),
		onCancel() {
			window.location.href = '/' + this.routes['forms.index'];
		},
		is(type) {
			return this.generalData && this.generalData.formType === type;
		},
		typeChanged(event) {
			this.$emit('update:form', { ...this.form, type: event.formType });
		},
	},
	beforeCreate() {
		// Reset vuex store modules to default state
		this.$store.commit('global/resetState');
	},
	created() {
		this.storeGlobalState({
			supportedLanguages: Object.freeze(this.supportedLanguages),
		});
		this.storeGlobalState({ defaultLanguage: this.defaultLanguage });

		if (this.form && this.selectedType) {
			this.$emit('update:form', { ...this.form, type: this.selectedType });
		}
	},
};
</script>

<style scoped>
.form-back {
	display: inline-flex;
	flex-direction: row;
	align-items: center;
	text-decoration: none;
	column-gap: 0.2em;
}

.preview-icon {
	font-size: 25px;
	margin-right: 3px;
	margin-top: -7px;
}
</style>
