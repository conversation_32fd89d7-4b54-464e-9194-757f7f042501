<broadcast-form
    :merge-fields="@js($mergeFields = $broadcast->mergeFields())"
    :consolidated-merge-fields="@js($consolidatedMergeFields = $broadcast->consolidatedMergeFields())"
    :initial-consolidated="@js($broadcast->consolidateRecipients ? 1 : 0)"
    :initial-translated-resource="@js(['translated' => $broadcast->translated])"
    inline-template>
    <div class="row">
        <div class="col-xs-12 col-md-6">
            <h3>{{ trans('broadcasts.form.heading.sender') }}</h3>
            <div class="form-group {{ FormError::classIfError('senderName') }}">
                <label for="senderName">
                    {{ trans('broadcasts.form.sender_name.label') }} {!! HTML::optional(true) !!}
                </label>
                {!! html()->text('senderName', $broadcast->senderName)->attributes(['class' => 'form-control']) !!}
            </div>

            <div class="form-group {{ FormError::classIfError('senderAddress') }}">
                <label for="senderAddress">
                    {{ trans('broadcasts.form.sender_address.label') }} {!! HTML::optional(true) !!}
                </label>
                {!! html()->text('senderAddress', $broadcast->senderAddress)->attributes(['class' => 'form-control']) !!}
            </div>

            <div class="form-group">
                <div class="checkbox styled">
                    {!! html()->checkbox('notifySender', $broadcast->notifySender, true)->id('sendcopy') !!}
                    <label for="sendcopy">
                        {!! trans('broadcasts.form.send-copy.label') !!}
                    </label>
                </div>
            </div>

            <h3>{{ trans('broadcasts.form.heading.message') }}</h3>

            <merge-fields
                :merge-fields="availableMergeFields"
                help-text="{{ trans('broadcasts.form.content.help') }}"
            >
                <template #default="useMergeFields">
                    <div class="form-group">
                        {!! html()->label(trans('broadcasts.form.subject.label'), 'subject') !!}
                        <help-icon content="{{ trans('broadcasts.form.subject.help') }}"></help-icon>
                        <multilingual
                            mode="text"
                            markdown-mode="full"
                            name="subject"
                            property="subject"
                            :resource="translatedResource"
                            :supported-languages="@js(current_account()->supportedLanguageCodes())"
                            input-class="form-control"
                            @keyup="(element) => useMergeFields.setCaretPosition(element)"
                            @focus="(element) => useMergeFields.setCaretPosition(element)"
                            markdown-guide-label="{{ trans('miscellaneous.markdown_guide.label') }}"
                            markdown-guide-url="{{ config('links.'.current_account_brand().'.markdown_guide') }}"
                            @input="onTranslatedInput"
                        ></multilingual>
                    </div>

                    <div class="form-group">
                        {!! html()->label(trans('broadcasts.form.body.label'), 'body') !!}
                        <multilingual
                            mode="editor"
                            markdown-mode="full"
                            name="body"
                            property="body"
                            :resource="translatedResource"
                            :supported-languages="@js(current_account()->supportedLanguageCodes())"
                            :remove-items-toolbar="['mediaEmbed']"
                            :allows-inline-images="true"
                            input-class="form-control"
                            @keyup="(element) => useMergeFields.setCaretPosition(element)"
                            @focus="(element) => useMergeFields.setCaretPosition(element)"
                            markdown-guide-label="{{ trans('miscellaneous.markdown_guide.label') }}"
                            markdown-guide-url="{{ config('links.'.current_account_brand().'.markdown_guide') }}"
                            @input="onTranslatedInput"
                        ></multilingual>
                    </div>
                </template>
            </merge-fields>

            <h3>{{ trans('notifications.form.offset.heading') }}</h3>
            <fieldset>
                <legend class="sr-only">{{ trans('notifications.form.offset.heading') }}</legend>
                <div class="radio styled">
                    {!! html()->radio('scheduleOption', !$broadcast->dueDate, 'immediately')->id('schedule-option-immediately') !!}
                    <label for='schedule-option-immediately'>
                        {{ trans('broadcasts.schedule.immediately') }}
                    </label>
                </div>
                <div class="radio styled">
                    {!! html()->radio('scheduleOption', $broadcast->dueDate, 'schedule')->id('schedule-option-schedule') !!}
                    <label for='schedule-option-schedule'>
                        {{ trans('broadcasts.schedule.schedule') }}
                    </label>
                </div>
                <div id="datetimeSelector" class="@if(!$broadcast->dueDate) hidden @endif">
                    <localised-datepicker
                        id="due"
                        name="due"
                        mode="datetime"
                        :value="@jsObject($dueDate)"
                        :default-timezone="@js($defaultTimezone)"
                        :include-timezone="true"
                        :preselect-current-date="false"
                        :highlight-today="false"
                        :hidden-fields="true"
                    />
                </div>
            </fieldset>
        </div>

        <div class="col-xs-12 col-md-6">
            @include('broadcast.recipients-table')

            @if(in_array($broadcast->type, config('broadcasts.collaborative')))
                <div class="form-group">
                    <div class="checkbox styled">
                        <input
                            type="checkbox"
                            name="includeCollaborators"
                            id="includeCollaborators"
                            @if($includeCollaborators) checked @endif
                            @if(feature_disabled('collaboration')) disabled @endif>
                        <label for="includeCollaborators">
                            @if(feature_enabled('collaboration'))
                                @lang('broadcasts.form.include_collaborators.label')
                            @else
                                <p>
                                    @lang('features.not_available')
                                    <a href="/feature-disabled/collaboration">@lang('shared.learn_more')</a>
                                </p>
                            @endif
                        </label>
                    </div>
                </div>
            @endif

            @if (in_array($broadcast->type, config('broadcasts.optional_consolidation')))
                <div class="form-group">
                    <popover class="radio styled" v-if="!canConsolidate" content="@lang('broadcasts.form.can_not_consolidate')" placement="right" trigger="hover">
                        <div>
                            <input :disabled="!canConsolidate" type="radio" name="consolidateRecipients" id="consolidateRecipients" :value="1" v-model="consolidated">
                            <label for="consolidateRecipients">
                                {!! trans('broadcasts.form.consolidate_recipients.yes') !!}
                            </label>
                        </div>
                    </popover>
                    <div v-else class="radio styled">
                        <input :disabled="!canConsolidate" type="radio" name="consolidateRecipients" id="consolidateRecipients" :value="1" v-model="consolidated">
                        <label for="consolidateRecipients">
                            {!! trans('broadcasts.form.consolidate_recipients.yes') !!}
                        </label>
                    </div>
                    <div class="radio styled">
                        <input type="radio" name="consolidateRecipients" id="doNotConsolidateRecipients" :value="0" v-model="consolidated">
                        <label for="doNotConsolidateRecipients">
                            {!! trans("broadcasts.form.consolidate_recipients.no.{$broadcast->type}") !!}
                        </label>
                    </div>
                </div>
                <div v-if="consolidated" class="alert-info sticky form-group" role="alert">
                    <div class="icon">
                        <div class="af-icons-md af-icons-md-alert-info"></div>
                    </div>
                    <div class="message">
                        {!! trans('broadcasts.form.consolidate_warning') !!}
                    </div>
                </div>
            @endif

            @include('partials.notifications.legal-basis')
        </div>

        @if (!isset($broadcast->id))
            {!! html()->hidden('seasonId', $broadcast->seasonId) !!}
            {!! html()->hidden('filters', json_encode($broadcast->filters)) !!}
            {!! html()->hidden('type', $broadcast->type) !!}
        @endif
    </div>
</broadcast-form>

<div class="form-actions">
    @if (! $numberOfRecipientsExceeded)
        @include('broadcast.buttons.review-send', ['buttonDisabled' => !$canSave])
        @include('broadcast.buttons.save-draft', ['buttonDisabled' => !$canSave])
        @includeWhen(isset($broadcast->id) && $canDelete, 'broadcast.buttons.discard', ['route' => 'broadcast.index'])
    @else
        <div class="mbm">
            @lang('broadcasts.errors.number_of_recipients_exceeded', ['max' => $numberOfRecipientsExceeded])
        </div>
    @endif
    @include('html.buttons.cancel', ['route' => 'broadcast.index'])
</div>
