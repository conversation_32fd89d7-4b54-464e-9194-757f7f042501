/**
 * Available tasks:
 *
 * gulp build
 * gulp watch
 * gulp test
 *
 * Run with `--env=production` or `--env=uat` to enable CSS and Javascript minification.
 * Run with `--verbose` switch to see all files that go into the bundle.
 */

// Load all the required plugins.
var gulp        = require('gulp'),
    babelify    = require('babelify'),
    browserify  = require('browserify'),
    buffer      = require('vinyl-buffer'),
    fs          = require('fs'),
    minimist    = require('minimist'),
    notify      = require('gulp-notify'),
    rename      = require('gulp-rename'),
    sass        = require('gulp-sass'),
    uglify      = require('gulp-uglify'),
    source      = require('vinyl-source-stream'),
    rtlcss      = require('gulp-rtlcss'),
    watchify    = require('watchify'),
    gutil       = require('gulp-util'),
    vueify      = require('vueify');

var custom = null;
var options = minimist(process.argv.slice(2));
var isProduction = false;

if (options.env == 'uat' || options.env == 'production') {
    isProduction = true;
} else {
    gutil.log('Running in', gutil.colors.green('development'), 'mode...');
}

// Avoid including development related code
// https://vuejs.org/v2/guide/deployment.html
process.env.NODE_ENV = isProduction ? 'production' : 'development';

function errorHandler(error) {
    gutil.log(error.toString());

    notify.onError({
        title: 'Error',
        message: '<%= error.message %>'
    })(error);

    this.emit('end');
}

function depHandler(dep) {
    if (options.verbose) {
        gutil.log(dep.file);
    }
}

if (fs.existsSync('./gulp.custom.js')) {
    custom = require('./gulp.custom');
}

var input  = 'resources/assets/',
    output = 'public/';

var styles = [
    input + 'sass/shift.scss'
];

var stylesRtl = [
    input + 'sass/rtl.scss'
];

var sassConfig = isProduction ? {outputStyle: 'compressed'} : {};
var cssExtension = isProduction ? '.min.css' : '.dev.css';
var jsExtension = isProduction ? '.min.js' : '.dev.js';

gulp.task('styles', function() {
    return gulp.src(styles)
        .pipe(sass(sassConfig).on('error', errorHandler))
        .pipe(rename('awardforce'+cssExtension))
        .pipe(gulp.dest(output + '/css'))
        .pipe(notify({ message: 'SCSS files compiled.' }));
});

gulp.task('styles-rtl', function() {
    return gulp.src(stylesRtl)
        .pipe(sass(sassConfig).on('error', errorHandler))
        .pipe(rtlcss('awardforce-rtl'+cssExtension))
        .pipe(rename('awardforce-rtl'+cssExtension))
        .pipe(gulp.dest(output + '/css'))
        .pipe(notify({ message: 'RTL SCSS files compiled.' }));
});

function bundle(watch) {
    var bundler = browserify({
        entries: ['./'+input+'js/src/main.js'],
        debug: true,
        cache: {},
        packageCache: {}
    }).transform(babelify).transform(vueify);

    bundler = watch ? watchify(bundler) : bundler;

    function rebundle() {
        var task = bundler.bundle()
            .on('error', errorHandler)
            .pipe(source('awardforce'+jsExtension))
            .pipe(buffer());

        if (isProduction) {
            task.pipe(uglify({ output: { quote_keys: true }}));
        }

        return task.pipe(gulp.dest('./'+output+'js/'))
            .pipe(notify({ message: 'Javascript files compiled.' }));
    }

    bundler.on('update', function() {
        gutil.log('Bundling scripts...');
        rebundle();
    });

    bundler.on('log', gutil.log);
    bundler.on('dep', depHandler);

    return rebundle();
}

gulp.task('scripts', function() {
    return bundle();
});

gulp.task('watchify', function() {
    return bundle(true);
});

gulp.task('test', function(done) {
    var karmaServer = require('karma').Server

    new karmaServer({
        configFile: __dirname + '/karma.conf.js',
        singleRun: true
    }, done).start();
});

gulp.task('lint', function() {
    var eslint = require('gulp-eslint');

    return gulp.src([
            input + 'js/src/**/*.vue',
            input + 'js/tests/**/*.js'
        ])
        .pipe(eslint())
        .pipe(eslint.format())
        .pipe(eslint.failOnError());
});

// Helper task for watching files for changes - watchify has to be run only once
// and it will incrementally rebuild the bundle.
gulp.task('watch', ['styles', 'styles-rtl', 'watchify'], function() {
    gulp.watch(input + 'sass/**', ['styles']);
    gulp.watch(input + 'sass/**', ['styles-rtl']);
});

// Task used to catch all tasks required during build time.
// Please include here any new tasks that have to be run by Jenkins.
gulp.task('build', ['styles', 'styles-rtl', 'scripts']);

// When running gulp without any tasks, it'll watch the scripts, styles, and do artisan publishing, etc.
gulp.task('default' , ['watch']);
