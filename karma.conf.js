const mixWebpackConfig = require('laravel-mix/setup/webpack.config.js');

const loadWebpackConfig = async () => {
  const webpackConfig = await mixWebpackConfig();

  // No need for entry, output in test context
  delete webpackConfig.entry;
  delete webpackConfig.output;

  // This webpack option controls if and how source maps are generated
  // https://webpack.js.org/configuration/devtool/
  webpackConfig.devtool = 'inline-source-map';

  // Remove incompatible webpack rules
  webpackConfig.module.rules = webpackConfig.module.rules.filter(rule => {
    return rule.test.source !== '\\.(cjs|mjs|jsx?|tsx?)$';
  });

  return webpackConfig;
};

module.exports = async (config) => {
  config.set({

    // base path that will be used to resolve all patterns (eg. files, exclude)
    basePath: '',

    // frameworks to use
    // available frameworks: https://npmjs.org/browse/keyword/karma-adapter
    frameworks: ['mocha', 'webpack'],

    // list of files / patterns to load in the browser
    files: [
      // jQuery is included as a global and loaded from a CDN - to make the
      // testing environment reflect that it is loaded here first
      'node_modules/jquery/dist/jquery.min.js',

      // initialise globals
      'resources/assets/js/tests/app.js',
      // all files ending in "Spec.js" or ".spec.js"
      // set watched to false as we use webpack's watch mode
			{ pattern: 'resources/assets/js/**/*[sS]pec.js', watched: false },
    ],

    // preprocess matching files before serving them to the browser
    // available preprocessors: https://npmjs.org/browse/keyword/karma-preprocessor
    preprocessors: {
			'resources/assets/js/**/*.js': ['webpack'],
    },

    proxies: {
      // Supress 404 errors when serving images in the test context
      '/img': ''
    },

    // test results reporter to use
    // possible values: 'dots', 'progress'
    // available reporters: https://npmjs.org/browse/keyword/karma-reporter
    reporters: ['mocha'],

    // webpack configuration
    webpack: await loadWebpackConfig(),

    // webpack middleware configuration
    webpackMiddleware: {
      stats: 'errors-only',
      logLevel: 'warn',

      // throw away the compiled source after the test run
      writeToDisk: false
    },

    // web server port
    port: 9876,

    // enable / disable colors in the output (reporters and logs)
    colors: true,

    // level of logging
    // possible values: config.LOG_DISABLE || config.LOG_ERROR || config.LOG_WARN || config.LOG_INFO || config.LOG_DEBUG
    logLevel: config.LOG_INFO,

    // enable / disable watching file and executing tests whenever any file changes
    autoWatch: false,

    // start these browsers
    // available browser launchers: https://npmjs.org/browse/keyword/karma-launcher
    browsers: ['ChromeHeadless'],

    // Continuous Integration mode
    // if true, Karma captures browsers, runs the tests and exits
    singleRun: true,

    // Concurrency level
    // how many browser should be started simultaneous
    concurrency: Infinity

  });
}
