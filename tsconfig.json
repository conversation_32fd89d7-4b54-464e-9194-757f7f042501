{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "importHelpers": true, "isolatedModules": false, "noEmit": false, "allowJs": true, "experimentalDecorators": true, "skipLibCheck": true, "lib": ["esnext", "dom"], "types": ["j<PERSON>y", "plupload"], "baseUrl": ".", "paths": {"@/*": ["resources/assets/js/src/*"], "vue/*": ["/vue/dist/vue.esm.js/*"], "vuex/*": ["vuex/dist/vue.esm.js/*"]}}, "vueCompilerOptions": {"experimentalCompatMode": 2}, "include": ["resources/**/*"], "references": [{"path": "./tsconfig.node.json"}], "exclude": ["**/*.spec.ts", "resources/assets/js/vitest/**/*"]}