<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('score_sets', function (Blueprint $table) {
            $table->boolean('internal_comments')->after('settings')->default(true);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('score_sets', function (Blueprint $table) {
            $table->dropColumn('internal_comments');
        });
    }
};
