# Changelog

All notable changes to this project will be documented in this file.

## v4.0.11 - 2025-08-23

### ✨ Features

- Ai agents feature toggle ([#8121](https://github.com/tectonic/the-force/pull/8121)) [@pktharindu](https://github.com/pktharindu)
- Offsite payment webhooks: Stripe Checkout ([#8099](https://github.com/tectonic/the-force/pull/8099)) [@acarpio89](https://github.com/acarpio89)
- Upgrade snowflakes factory and based on the upgraded identi… ([#8113](https://github.com/tectonic/the-force/pull/8113)) [@kontoulis](https://github.com/kontoulis)
- Dummy payment webhook route ([#8163](https://github.com/tectonic/the-force/pull/8163)) [@acarpio89](https://github.com/acarpio89)

### 🛠️ Improvements

- Improvement/xss issue with entrant term change ([#8150](https://github.com/tectonic/the-force/pull/8150)) [@adamorlowski-cf](https://github.com/adamo<PERSON>owski-cf)
- Add user language preference to manage entries ([#8141](https://github.com/tectonic/the-force/pull/8141)) [@fmarquesto](https://github.com/fmarquesto)
- Update allocation payment type ([#8092](https://github.com/tectonic/the-force/pull/8092)) [@thanosalexandris](https://github.com/thanosalexandris)
- Dropdown filter group on list views ([#8144](https://github.com/tectonic/the-force/pull/8144)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)

### 🐛 Bug Fixes

- Fix data-redirector link redirection ([#8190](https://github.com/tectonic/the-force/pull/8190)) [@thanosalexandris](https://github.com/thanosalexandris)
- Forms List: Added missing translation to Invite applicants action ([#8175](https://github.com/tectonic/the-force/pull/8175)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Remove output html from test as it results in double encoding ([#8052](https://github.com/tectonic/the-force/pull/8052)) [@kontoulis](https://github.com/kontoulis)
- Offsite payment webhooks: remove leftover tests ([#8180](https://github.com/tectonic/the-force/pull/8180)) [@acarpio89](https://github.com/acarpio89)
- accept image file in the logo uploader ([#8162](https://github.com/tectonic/the-force/pull/8162)) [@fmarquesto](https://github.com/fmarquesto)
- MyEntriesLocalId and MyGrantReportsEntryId should handle GrantReport forms ([#8145](https://github.com/tectonic/the-force/pull/8145)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Restrict intended redirection ([#8166](https://github.com/tectonic/the-force/pull/8166)) [@kontoulis](https://github.com/kontoulis)
- Render `eligibilityContentBlock` with `v-output` to assert we don’t display raw HTML there ([#8160](https://github.com/tectonic/the-force/pull/8160)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Enable inline images and remove the media embed toolbar item from CollaboratorsInviteModal. ([#8157](https://github.com/tectonic/the-force/pull/8157)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Fix focus and blur event listeners to CKEditor ([#8153](https://github.com/tectonic/the-force/pull/8153)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Decode HTML entities in Output::stripTags ([#8156](https://github.com/tectonic/the-force/pull/8156)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Fix backend and frontend `WordCounter` to handle heading tags `<h1>` to `<h6>` ([#8155](https://github.com/tectonic/the-force/pull/8155)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- File uploads | Use mime type returned from Plupload, while keeping the previous logic as fallback ([#8130](https://github.com/tectonic/the-force/pull/8130)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Cart doesn’t load due to a large number of field based price variants part 2 ([#8159](https://github.com/tectonic/the-force/pull/8159)) [@pktharindu](https://github.com/pktharindu)
- Improved error handling for Invalid Exif values ([#8085](https://github.com/tectonic/the-force/pull/8085)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Exports timing out with errors ([#8149](https://github.com/tectonic/the-force/pull/8149)) [@fmarquesto](https://github.com/fmarquesto)
- Skip locked file performing find duplicates ([#7414](https://github.com/tectonic/the-force/pull/7414)) [@nikocucuzza](https://github.com/nikocucuzza)
- Total video viewing time in falcon not working ([#8154](https://github.com/tectonic/the-force/pull/8154)) [@fmarquesto](https://github.com/fmarquesto)
- Adjust `WordCounter` for handling `<` and `>` signs ([#8152](https://github.com/tectonic/the-force/pull/8152)) [@fabriciozeferino](https://github.com/fabriciozeferino)

### ✍️ Other Changes

- Logo upload failing as an organisation owner ([#8089](https://github.com/tectonic/the-force/pull/8089)) [@fmarquesto](https://github.com/fmarquesto)

## v4.0.10 - 2025-08-09

### ✨ Features

- Enable inline images and full toolbar in hint texts ([#8143](https://github.com/tectonic/the-force/pull/8143)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- CKEditor - Phase 2 - MAIN (read-only) ([#6238](https://github.com/tectonic/the-force/pull/6238)) [@Ravaelles](https://github.com/Ravaelles)
- CKEditor: bump platform and translations ([#8136](https://github.com/tectonic/the-force/pull/8136)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Remove explicit use of AWS credentials ([#7810](https://github.com/tectonic/the-force/pull/7810)) [@acarpio89](https://github.com/acarpio89)

### 🛠️ Improvements

- bumb jquery to 3.7.1 ([#8091](https://github.com/tectonic/the-force/pull/8091)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- CKeditor: remove video embed from packing slip and add image embed ([#8124](https://github.com/tectonic/the-force/pull/8124)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- CKeditor:Implement CKEditor in Field Settings and Review Flow ([#8123](https://github.com/tectonic/the-force/pull/8123)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- CKEditor: Add `RESOURCE_ORGANISATIONS` to FieldResource ([#8122](https://github.com/tectonic/the-force/pull/8122)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Add referee fields to judging PDF when they are configured for a score set ([#8093](https://github.com/tectonic/the-force/pull/8093)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- CKEditor: add insert image via url tool to required locations ([#8118](https://github.com/tectonic/the-force/pull/8118)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- CKEditor: Add dynamic toolbar filtering to content blocks ([#8115](https://github.com/tectonic/the-force/pull/8115)) [@fabriciozeferino](https://github.com/fabriciozeferino)

### 🐛 Bug Fixes

- Fix when checking whether the countdown is visible in the entry view ([#8146](https://github.com/tectonic/the-force/pull/8146)) [@nikocucuzza](https://github.com/nikocucuzza)
- Authorise AWSRequest (transcoding, transcribing) ([#8103](https://github.com/tectonic/the-force/pull/8103)) [@acarpio89](https://github.com/acarpio89)
- Current password is required field when updating the profile page ([#8139](https://github.com/tectonic/the-force/pull/8139)) [@nikocucuzza](https://github.com/nikocucuzza)
- Refactoring tests ([#8111](https://github.com/tectonic/the-force/pull/8111)) [@thanosalexandris](https://github.com/thanosalexandris)
- add context for required resubmission notification ([#8137](https://github.com/tectonic/the-force/pull/8137)) [@fmarquesto](https://github.com/fmarquesto)
- Cart doesn’t load due to a large number of field based price variants ([#8140](https://github.com/tectonic/the-force/pull/8140)) [@pktharindu](https://github.com/pktharindu)
- Fix issue with report form fields having category options enabled ([#8076](https://github.com/tectonic/the-force/pull/8076)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Fix allocation payment endpoint issues ([#8082](https://github.com/tectonic/the-force/pull/8082)) [@thanosalexandris](https://github.com/thanosalexandris)
- Fix delete undelete prevention on roles ([#8119](https://github.com/tectonic/the-force/pull/8119)) [@thanosalexandris](https://github.com/thanosalexandris)
- Fixes eligibility tab saving error ([#8135](https://github.com/tectonic/the-force/pull/8135)) [@thanosalexandris](https://github.com/thanosalexandris)
- Bugfix/keyboard navigation not working correctly ([#8102](https://github.com/tectonic/the-force/pull/8102)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Fixed the saving issue if the form is not collaborative but the api update setting is enabled ([#8059](https://github.com/tectonic/the-force/pull/8059)) [@kevinawardforce](https://github.com/kevinawardforce)
- Add `packingSlipInstructions` to `htmlFields` in Category model ([#8142](https://github.com/tectonic/the-force/pull/8142)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Setting field label preview not updating in real time ([#8138](https://github.com/tectonic/the-force/pull/8138)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Fix intermittent failing test: tests/Tests/Library/Search/Actions/SimpleLinkTest.php ([#8063](https://github.com/tectonic/the-force/pull/8063)) [@kevinawardforce](https://github.com/kevinawardforce)
- CKEditor: word count in safari is different to chrome ([#8129](https://github.com/tectonic/the-force/pull/8129)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Reorder option in Recommendation judging when labels are numeric ([#8117](https://github.com/tectonic/the-force/pull/8117)) [@pktharindu](https://github.com/pktharindu)
- Fix XSS via panel updated pusher notification ([#8125](https://github.com/tectonic/the-force/pull/8125)) [@acarpio89](https://github.com/acarpio89)
- Invalid invisible characters copied into field options ([#8047](https://github.com/tectonic/the-force/pull/8047)) [@Ravaelles](https://github.com/Ravaelles)
- Cart count appears for registered users if there is a required user field ([#7946](https://github.com/tectonic/the-force/pull/7946)) [@pktharindu](https://github.com/pktharindu)
- Entry Collaborators should be able to access the entrant pdf through "My applications" list ([#8088](https://github.com/tectonic/the-force/pull/8088)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Fix "Send Request" button for newly created Referees (non-collaborative forms) ([#8108](https://github.com/tectonic/the-force/pull/8108)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- CKEditor: formatting is not being copied when copying the content from a sent broadcast ([#8109](https://github.com/tectonic/the-force/pull/8109)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- CKEditor: copying slugs from slack copies span element ([#8107](https://github.com/tectonic/the-force/pull/8107)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Fix intermittent test: `EntryContextTest::testHandleSuccessfullyFillsPromptContextWithEntryData` ([#8106](https://github.com/tectonic/the-force/pull/8106)) [@pktharindu](https://github.com/pktharindu)
- Stop API not found exceptions from being reported ([#8080](https://github.com/tectonic/the-force/pull/8080)) [@NizarBerjawi](https://github.com/NizarBerjawi)

### 📦 Security and Dependency Updates

- bumb jquery to 3.7.1 ([#8091](https://github.com/tectonic/the-force/pull/8091)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Bump pbkdf2 from 3.1.2 to 3.1.3 ([#8002](https://github.com/tectonic/the-force/pull/8002)) @[dependabot[bot]](https://github.com/apps/dependabot)
- CKEditor: iframe injection ([#8114](https://github.com/tectonic/the-force/pull/8114)) [@fabriciozeferino](https://github.com/fabriciozeferino)

### ✍️ Other Changes

- CKEditor: Fix error when Vue binds empty maximum values ([#8131](https://github.com/tectonic/the-force/pull/8131)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- [devOps] af4 to the-force renaming in GH actions ([#8112](https://github.com/tectonic/the-force/pull/8112)) [@sameeraksc](https://github.com/sameeraksc)

## v4.0.9 - 2025-07-26

### ✨ Features

- Formation organisation resource ([#7482](https://github.com/tectonic/the-force/pull/7482)) [@kontoulis](https://github.com/kontoulis)
- Organisation members - add to organisation  ([#7787](https://github.com/tectonic/the-force/pull/7787)) [@kontoulis](https://github.com/kontoulis)
- Org resource members temp for merge ([#7814](https://github.com/tectonic/the-force/pull/7814)) [@kontoulis](https://github.com/kontoulis)
- Organisation member view ([#7835](https://github.com/tectonic/the-force/pull/7835)) [@kontoulis](https://github.com/kontoulis)
- Feature/form access by role ([#7844](https://github.com/tectonic/the-force/pull/7844)) [@fmarquesto](https://github.com/fmarquesto)
- AI Agent: context handling and command ([#7852](https://github.com/tectonic/the-force/pull/7852)) [@pktharindu](https://github.com/pktharindu)
- Snowflake id support ([#7990](https://github.com/tectonic/the-force/pull/7990)) [@kontoulis](https://github.com/kontoulis)
- Include collaborators in notifications ([#8008](https://github.com/tectonic/the-force/pull/8008)) [@fmarquesto](https://github.com/fmarquesto)
- Carts list view ([#7811](https://github.com/tectonic/the-force/pull/7811)) [@acarpio89](https://github.com/acarpio89)
- Bugfix/pdf viewer lib ([#8023](https://github.com/tectonic/the-force/pull/8023)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- AI Agents Library - Prism Bedrock wrapper ([#7697](https://github.com/tectonic/the-force/pull/7697)) [@pktharindu](https://github.com/pktharindu)
- Account copy kafka ([#8069](https://github.com/tectonic/the-force/pull/8069)) [@kontoulis](https://github.com/kontoulis)

### 🛠️ Improvements

- Form id loading improvements ([#8030](https://github.com/tectonic/the-force/pull/8030)) [@kontoulis](https://github.com/kontoulis)
- Manage entries columnator general improvements ([#8042](https://github.com/tectonic/the-force/pull/8042)) [@kontoulis](https://github.com/kontoulis)
- Improvement/tab config tidy up ([#7972](https://github.com/tectonic/the-force/pull/7972)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- new arrayToObject helper to be used in front-end. ([#8078](https://github.com/tectonic/the-force/pull/8078)) [@fmarquesto](https://github.com/fmarquesto)
- Invalidate user session after social token revoked ([#8005](https://github.com/tectonic/the-force/pull/8005)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Upgrade Chargebee package to v4 ([#7988](https://github.com/tectonic/the-force/pull/7988)) [@pktharindu](https://github.com/pktharindu)
- Improvement/role selection on field config ([#8061](https://github.com/tectonic/the-force/pull/8061)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)

### 🐛 Bug Fixes

- Cookie notice missing padding ([#8105](https://github.com/tectonic/the-force/pull/8105)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Fix missing tooltip for conditional fields by allowing `HtmlString` return type ([#8098](https://github.com/tectonic/the-force/pull/8098)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Bugfix/fix default form role visibility ([#8097](https://github.com/tectonic/the-force/pull/8097)) [@fmarquesto](https://github.com/fmarquesto)
- Bugfix organisation feature based menu item ([#8100](https://github.com/tectonic/the-force/pull/8100)) [@kontoulis](https://github.com/kontoulis)
- No Rate Limit on OTP Verification Leading to Account Takeover ([#8094](https://github.com/tectonic/the-force/pull/8094)) [@Ravaelles](https://github.com/Ravaelles)
- Profile MFA toggle | Replaced old blade component with MfaSwitch ([#8081](https://github.com/tectonic/the-force/pull/8081)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Copy account bug category chapter relationship missing ([#8043](https://github.com/tectonic/the-force/pull/8043)) [@fmarquesto](https://github.com/fmarquesto)
- Fix issues with login redirects ([#8062](https://github.com/tectonic/the-force/pull/8062)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Fixes disabled save next button when swapping categories ([#8065](https://github.com/tectonic/the-force/pull/8065)) [@thanosalexandris](https://github.com/thanosalexandris)
- add id for test users to match expectation ([#8077](https://github.com/tectonic/the-force/pull/8077)) [@fmarquesto](https://github.com/fmarquesto)
- fix repository method usage with new settings column ([#8075](https://github.com/tectonic/the-force/pull/8075)) [@fmarquesto](https://github.com/fmarquesto)
- Bugfix/pdf viewer lib ([#8023](https://github.com/tectonic/the-force/pull/8023)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Custom export ignoring the action status filter ([#8044](https://github.com/tectonic/the-force/pull/8044)) [@Ravaelles](https://github.com/Ravaelles)
- Entries copy feature should include currency and referee fields ([#7971](https://github.com/tectonic/the-force/pull/7971)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Fixed the collaboration date field formatting issue ([#8068](https://github.com/tectonic/the-force/pull/8068)) [@kevinawardforce](https://github.com/kevinawardforce)

### ✍️ Other Changes

- bump missing translations ([#8087](https://github.com/tectonic/the-force/pull/8087)) [@fmarquesto](https://github.com/fmarquesto)
- [DevOps] added php kafka ext to behat test job ([#8072](https://github.com/tectonic/the-force/pull/8072)) [@sameeraksc](https://github.com/sameeraksc)

## v4.0.8 - 2025-07-12

### ✨ Features

- Add 7 new languages ([#8046](https://github.com/tectonic/the-force/pull/8046)) [@pktharindu](https://github.com/pktharindu)
- Move general entry settings to form - Merge issues ([#8028](https://github.com/tectonic/the-force/pull/8028)) [@nikocucuzza](https://github.com/nikocucuzza)
- Move general entry settings to form ([#7553](https://github.com/tectonic/the-force/pull/7553)) [@nikocucuzza](https://github.com/nikocucuzza)
- [Dashboards OKR] Support multi-select Season and Form filters ([#7996](https://github.com/tectonic/the-force/pull/7996)) [@jc-creativeforce](https://github.com/jc-creativeforce)

### 🛠️ Improvements

- Enable configuring internal comments visibility on score sets ([#8033](https://github.com/tectonic/the-force/pull/8033)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Refactoring avatar to profile photo ([#7699](https://github.com/tectonic/the-force/pull/7699)) [@thanosalexandris](https://github.com/thanosalexandris)
- Update payment options ([#8022](https://github.com/tectonic/the-force/pull/8022)) [@thanosalexandris](https://github.com/thanosalexandris)
- Added collection and object for products and plans ([#8041](https://github.com/tectonic/the-force/pull/8041)) [@thanosalexandris](https://github.com/thanosalexandris)
- Improvement: local id shortcode format ([#7895](https://github.com/tectonic/the-force/pull/7895)) [@kontoulis](https://github.com/kontoulis)
- Additional "Invite applicant..." locations ([#7982](https://github.com/tectonic/the-force/pull/7982)) [@Ravaelles](https://github.com/Ravaelles)
- Added plan name on guides and tours with upgrade functionality ([#8015](https://github.com/tectonic/the-force/pull/8015)) [@thanosalexandris](https://github.com/thanosalexandris)
- Encrypt payment provider settings and mask sensitive fields ([#7920](https://github.com/tectonic/the-force/pull/7920)) [@pktharindu](https://github.com/pktharindu)
- feat: No line breaks between class properties ([#7149](https://github.com/tectonic/the-force/pull/7149)) [@pktharindu](https://github.com/pktharindu)

### 🐛 Bug Fixes

- Word count is reporting incorrectly ([#8004](https://github.com/tectonic/the-force/pull/8004)) [@Ravaelles](https://github.com/Ravaelles)
- Remove form setting check from certificate pdf ([#8060](https://github.com/tectonic/the-force/pull/8060)) [@kontoulis](https://github.com/kontoulis)
- Fixes vueconsumer issues ([#8057](https://github.com/tectonic/the-force/pull/8057)) [@thanosalexandris](https://github.com/thanosalexandris)
- Open external links in new tab in textarea output ([#8058](https://github.com/tectonic/the-force/pull/8058)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- Format API values in real-time entry endpoints ([#8014](https://github.com/tectonic/the-force/pull/8014)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- Fix issue with categories being overwritten when using filters with multicheckbox component ([#8056](https://github.com/tectonic/the-force/pull/8056)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Make feedback on entries in inactive chapters visible ([#8035](https://github.com/tectonic/the-force/pull/8035)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Bump translations repository ([#8051](https://github.com/tectonic/the-force/pull/8051)) [@thanosalexandris](https://github.com/thanosalexandris)
- Fixes seed column command ([#8049](https://github.com/tectonic/the-force/pull/8049)) [@thanosalexandris](https://github.com/thanosalexandris)
- Fix favicon ico fetch exception ([#8034](https://github.com/tectonic/the-force/pull/8034)) [@thanosalexandris](https://github.com/thanosalexandris)
- Role registration form link is not working for users who are logged in ([#8038](https://github.com/tectonic/the-force/pull/8038)) [@fmarquesto](https://github.com/fmarquesto)
- Fix plan names issue ([#8036](https://github.com/tectonic/the-force/pull/8036)) [@thanosalexandris](https://github.com/thanosalexandris)
- Fix stored form settings values ([#8037](https://github.com/tectonic/the-force/pull/8037)) [@nikocucuzza](https://github.com/nikocucuzza)
- Fix incorrect handling of "invitation only" and "browsable" form settings in the app ([#8039](https://github.com/tectonic/the-force/pull/8039)) [@nikocucuzza](https://github.com/nikocucuzza)
- entry should be eligible when all tabs are eligible ([#8010](https://github.com/tectonic/the-force/pull/8010)) [@fmarquesto](https://github.com/fmarquesto)
- Display id setting when downloading grant reports ([#8032](https://github.com/tectonic/the-force/pull/8032)) [@nikocucuzza](https://github.com/nikocucuzza)
- Authenticator infinite redirection loop ([#8006](https://github.com/tectonic/the-force/pull/8006)) [@kontoulis](https://github.com/kontoulis)
- Fix complete profile issue ([#8019](https://github.com/tectonic/the-force/pull/8019)) [@thanosalexandris](https://github.com/thanosalexandris)
- Fix all "NTA" named order items in the database ([#7983](https://github.com/tectonic/the-force/pull/7983)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- thumbnail in manage workspace incorrect but correct in judge workspace ([#8020](https://github.com/tectonic/the-force/pull/8020)) [@fmarquesto](https://github.com/fmarquesto)

### ✍️ Other Changes

- [DevOps] Adding ckeditor license key ([#8053](https://github.com/tectonic/the-force/pull/8053)) [@paolaromerod](https://github.com/paolaromerod)

## v4.0.7 - 2025-06-28

### ✨ Features

- Adds support for uploading images before creating score sets ([#7961](https://github.com/tectonic/the-force/pull/7961)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- Adds more debounce logs and shortens expiry time - platform bump ([#8013](https://github.com/tectonic/the-force/pull/8013)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- Added interface texts ([#7987](https://github.com/tectonic/the-force/pull/7987)) [@thanosalexandris](https://github.com/thanosalexandris)
- [Dashboards OKR] Chapter Manager Dashboards ([#7800](https://github.com/tectonic/the-force/pull/7800)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- Broadcast to collaborators ([#7876](https://github.com/tectonic/the-force/pull/7876)) [@pktharindu](https://github.com/pktharindu)

### 🛠️ Improvements

- Show a warning message when panel has a judge role that results in public access to entries ([#7969](https://github.com/tectonic/the-force/pull/7969)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Allow `del` tag for strikethrough text ([#7981](https://github.com/tectonic/the-force/pull/7981)) [@fabriciozeferino](https://github.com/fabriciozeferino)

### 🐛 Bug Fixes

- Conditionally validate Json translations when score control is "recommendation" ([#8012](https://github.com/tectonic/the-force/pull/8012)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Fixed short fields displayed when required fileds are not completed ([#8003](https://github.com/tectonic/the-force/pull/8003)) [@thanosalexandris](https://github.com/thanosalexandris)
- Badge query filter loads badges for irrelevant entries ([#7998](https://github.com/tectonic/the-force/pull/7998)) [@kontoulis](https://github.com/kontoulis)
- Add retry strategy when importing users ([#8007](https://github.com/tectonic/the-force/pull/8007)) [@nikocucuzza](https://github.com/nikocucuzza)
- Collaboration: Broadcast submittable eligibility status to other collaborators ([#7900](https://github.com/tectonic/the-force/pull/7900)) [@kevinawardforce](https://github.com/kevinawardforce)
- Fix issue when "default Form" cannot be obtained ([#7960](https://github.com/tectonic/the-force/pull/7960)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- Add internal comments to leaderboard exports ([#7995](https://github.com/tectonic/the-force/pull/7995)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Print save as pdf button not working 2 ([#7985](https://github.com/tectonic/the-force/pull/7985)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Fix the collaboration referee that email and name weren't saving ([#7928](https://github.com/tectonic/the-force/pull/7928)) [@kevinawardforce](https://github.com/kevinawardforce)
- sort result before asserting ([#7974](https://github.com/tectonic/the-force/pull/7974)) [@fmarquesto](https://github.com/fmarquesto)
- XSS - Handle <af-field> content as pre-parsed HTML in Output::html ([#7992](https://github.com/tectonic/the-force/pull/7992)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Restore top and bottom margin on `<p>` tags ([#7993](https://github.com/tectonic/the-force/pull/7993)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- save file metadata to database ([#7711](https://github.com/tectonic/the-force/pull/7711)) [@fmarquesto](https://github.com/fmarquesto)
- clicking submit pulls up eligible entry content block ([#7986](https://github.com/tectonic/the-force/pull/7986)) [@fmarquesto](https://github.com/fmarquesto)
- Failing command on profile controller ([#7989](https://github.com/tectonic/the-force/pull/7989)) [@thanosalexandris](https://github.com/thanosalexandris)
- Prevents module loading on error pages ([#7817](https://github.com/tectonic/the-force/pull/7817)) [@thanosalexandris](https://github.com/thanosalexandris)
- Fixes long labels on form widgets buttons ([#7979](https://github.com/tectonic/the-force/pull/7979)) [@thanosalexandris](https://github.com/thanosalexandris)
- Fixes "User deleted" issue on user names. ([#7962](https://github.com/tectonic/the-force/pull/7962)) [@thanosalexandris](https://github.com/thanosalexandris)
- Disallow saving scoring criteria with empty recommendation labels ([#7944](https://github.com/tectonic/the-force/pull/7944)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Hides schedule grant report buttons when season is archived ([#7939](https://github.com/tectonic/the-force/pull/7939)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- XSS 3.0 - Drop Markdown inline and refactor code to work without it ([#7948](https://github.com/tectonic/the-force/pull/7948)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Broadcast page not loading on prod ([#7976](https://github.com/tectonic/the-force/pull/7976)) [@pktharindu](https://github.com/pktharindu)
- Fix intermittent Tests\Http\Controllers\Api\V2\ScoreSetControllerTest::testItCanListScoreSets ([#7952](https://github.com/tectonic/the-force/pull/7952)) [@jc-creativeforce](https://github.com/jc-creativeforce)

## v4.0.6 - 2025-06-14

### ✨ Features

- Remove credit card option for annual plans ([#7938](https://github.com/tectonic/the-force/pull/7938)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- Field value updated webhook ([#6927](https://github.com/tectonic/the-force/pull/6927)) [@pktharindu](https://github.com/pktharindu)
- Judging internal comments ([#7702](https://github.com/tectonic/the-force/pull/7702)) [@kevinawardforce](https://github.com/kevinawardforce)

### 🛠️ Improvements

- bump translations ([#7964](https://github.com/tectonic/the-force/pull/7964)) [@fmarquesto](https://github.com/fmarquesto)
- Record the moderation status change in the audit log ([#7954](https://github.com/tectonic/the-force/pull/7954)) [@Ravaelles](https://github.com/Ravaelles)
- Display save and submit deactivated message in red text while file is uploading ([#7958](https://github.com/tectonic/the-force/pull/7958)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- add new notification trigger resubmission required ([#7918](https://github.com/tectonic/the-force/pull/7918)) [@fmarquesto](https://github.com/fmarquesto)
- Upgrade to PHP 8.3 ([#7703](https://github.com/tectonic/the-force/pull/7703)) [@pktharindu](https://github.com/pktharindu)
- Display scoring average column by default regardless of selected filters ([#7885](https://github.com/tectonic/the-force/pull/7885)) [@NizarBerjawi](https://github.com/NizarBerjawi)

### 🐛 Bug Fixes

- add created time to muffin ([#7973](https://github.com/tectonic/the-force/pull/7973)) [@fmarquesto](https://github.com/fmarquesto)
- Formula field doesn't work when it references read-only fields ([#7926](https://github.com/tectonic/the-force/pull/7926)) [@Ravaelles](https://github.com/Ravaelles)
- 500 error when creating pdf ([#7936](https://github.com/tectonic/the-force/pull/7936)) [@fmarquesto](https://github.com/fmarquesto)
- change expectation to match html entities encode ([#7933](https://github.com/tectonic/the-force/pull/7933)) [@fmarquesto](https://github.com/fmarquesto)
- Encode the name correctly before checking against the response. ([#7929](https://github.com/tectonic/the-force/pull/7929)) [@kontoulis](https://github.com/kontoulis)
- manage duplicates list view shows entries from deleted forms ([#7959](https://github.com/tectonic/the-force/pull/7959)) [@fmarquesto](https://github.com/fmarquesto)
- Test: Change the expected relative time to 1 minute ([#7932](https://github.com/tectonic/the-force/pull/7932)) [@kontoulis](https://github.com/kontoulis)
- Field based autotag should consider category ([#7957](https://github.com/tectonic/the-force/pull/7957)) [@kontoulis](https://github.com/kontoulis)
- Interface text strings visible on custom export page ([#7956](https://github.com/tectonic/the-force/pull/7956)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Add explicit filename and mime type when creating the file ([#7934](https://github.com/tectonic/the-force/pull/7934)) [@nikocucuzza](https://github.com/nikocucuzza)
- Have account switcher use default language as fallback for account names ([#7922](https://github.com/tectonic/the-force/pull/7922)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- Collaboration: Text field sending single field update endpoint twice ([#7813](https://github.com/tectonic/the-force/pull/7813)) [@kevinawardforce](https://github.com/kevinawardforce)
- Remove hardcoded ids from Notification tests ([#7953](https://github.com/tectonic/the-force/pull/7953)) [@acarpio89](https://github.com/acarpio89)
- User role count incorrect ([#7943](https://github.com/tectonic/the-force/pull/7943)) [@fmarquesto](https://github.com/fmarquesto)
- Update read/write field access values of entrant to boolean in GET field API response ([#7919](https://github.com/tectonic/the-force/pull/7919)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Show form selectors for entry.eligible, entry.ineligible, and entry.invited ([#7937](https://github.com/tectonic/the-force/pull/7937)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Preview button in entry form going to wrong tab for managers ([#7947](https://github.com/tectonic/the-force/pull/7947)) [@Ravaelles](https://github.com/Ravaelles)
- Allow managers to update an entry after the entry deadline has passed ([#7899](https://github.com/tectonic/the-force/pull/7899)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Tests\Modules\Api\V2\Endpoints\EntryTest::testFilterByEntryTitle ([#7949](https://github.com/tectonic/the-force/pull/7949)) [@nikocucuzza](https://github.com/nikocucuzza)
- Strip <edit-interface-text> tags while preserving basic HTML in LinkS… ([#7940](https://github.com/tectonic/the-force/pull/7940)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- XSS 3.0 - fix markdown inline newlines ([#7917](https://github.com/tectonic/the-force/pull/7917)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Target=blank visible on guides and tours page ([#7905](https://github.com/tectonic/the-force/pull/7905)) [@Ravaelles](https://github.com/Ravaelles)
- [Dashboards OKR] Fix issue where dropdowns display no options if user languages is not supported in account ([#7898](https://github.com/tectonic/the-force/pull/7898)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- add account as domaing prefix to avoid duplicates ([#7941](https://github.com/tectonic/the-force/pull/7941)) [@fmarquesto](https://github.com/fmarquesto)
- update vue-bootstrap hash ([#7925](https://github.com/tectonic/the-force/pull/7925)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Fix Action dropdown in payment schedule table ([#7916](https://github.com/tectonic/the-force/pull/7916)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Hide referee from preview when review task is deleted ([#7910](https://github.com/tectonic/the-force/pull/7910)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Fix NTA appearing in Orders ([#7927](https://github.com/tectonic/the-force/pull/7927)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- EntryItem test + API EntryTest ([#7935](https://github.com/tectonic/the-force/pull/7935)) [@nikocucuzza](https://github.com/nikocucuzza)
- XSS - Allow `start` attribute in HTML so users can make numbered lists starting from any number ([#7909](https://github.com/tectonic/the-force/pull/7909)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Add caret icon to LinkSelector component dropdown label ([#7904](https://github.com/tectonic/the-force/pull/7904)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- verify if scoreset is empty ([#7915](https://github.com/tectonic/the-force/pull/7915)) [@fmarquesto](https://github.com/fmarquesto)
- Log tag event test fix ([#7931](https://github.com/tectonic/the-force/pull/7931)) [@kontoulis](https://github.com/kontoulis)
- Referee fields should be validated inside Review flow ([#7841](https://github.com/tectonic/the-force/pull/7841)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Fix intermittent issue with filtered by star test ([#7930](https://github.com/tectonic/the-force/pull/7930)) [@pktharindu](https://github.com/pktharindu)
- Resubscribe button redirection ([#7923](https://github.com/tectonic/the-force/pull/7923)) [@kontoulis](https://github.com/kontoulis)
- Autoscoring field columns visible on user entries tab ([#7924](https://github.com/tectonic/the-force/pull/7924)) [@kontoulis](https://github.com/kontoulis)
- Shows payment button if previous order exists, but has been deleted ([#7908](https://github.com/tectonic/the-force/pull/7908)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- User confirmation six digit code wrong error message ([#7911](https://github.com/tectonic/the-force/pull/7911)) [@kontoulis](https://github.com/kontoulis)
- Fix search field id property in hidden field ([#7912](https://github.com/tectonic/the-force/pull/7912)) [@kontoulis](https://github.com/kontoulis)
- Bugfix video duration validation ([#7901](https://github.com/tectonic/the-force/pull/7901)) [@kontoulis](https://github.com/kontoulis)
- Set the internal param default value to false ([#7907](https://github.com/tectonic/the-force/pull/7907)) [@kevinawardforce](https://github.com/kevinawardforce)

### 📁 Has Migrations

- Field value updated webhook ([#6927](https://github.com/tectonic/the-force/pull/6927)) [@pktharindu](https://github.com/pktharindu)
- Judging internal comments ([#7702](https://github.com/tectonic/the-force/pull/7702)) [@kevinawardforce](https://github.com/kevinawardforce)

### ✍️ Other Changes

- [DevOps] Adding dev dependencies in UAT ([#7914](https://github.com/tectonic/the-force/pull/7914)) [@paolaromerod](https://github.com/paolaromerod)

## v4.0.5 - 2025-05-31

### ✨ Features

- Fix judging fast start not saving labels for all languages ([#7868](https://github.com/tectonic/the-force/pull/7868)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- XSS 3.0 - frontend sanitiser LINT ([#7619](https://github.com/tectonic/the-force/pull/7619)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Add remembrance to video resolution ([#7853](https://github.com/tectonic/the-force/pull/7853)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- 🌟XSS 3.0🌟 [TheForce] - Main branch ([#7590](https://github.com/tectonic/the-force/pull/7590)) [@Ravaelles](https://github.com/Ravaelles)
- XSS 3.0 - frontend sanitiser ([#7622](https://github.com/tectonic/the-force/pull/7622)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Remove CacheCartRepository ([#7829](https://github.com/tectonic/the-force/pull/7829)) [@acarpio89](https://github.com/acarpio89)

### 🛠️ Improvements

- Add auto score column for individual field ([#7867](https://github.com/tectonic/the-force/pull/7867)) [@thanosalexandris](https://github.com/thanosalexandris)
- Add title filter to List entries endpooint ([#7874](https://github.com/tectonic/the-force/pull/7874)) [@nikocucuzza](https://github.com/nikocucuzza)
- Bump translations ([#7870](https://github.com/tectonic/the-force/pull/7870)) [@thanosalexandris](https://github.com/thanosalexandris)
- XSS 3.0 - comment bubble icon doesn't work on managers view of an entry ([#7839](https://github.com/tectonic/the-force/pull/7839)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Add product dropdown to Holocron Feature Intro ([#7793](https://github.com/tectonic/the-force/pull/7793)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)

### 🐛 Bug Fixes

- Render criterion label in judging PDF ([#7906](https://github.com/tectonic/the-force/pull/7906)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Add alignment column when variants present ([#7893](https://github.com/tectonic/the-force/pull/7893)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Fix issues with optional registration fields and registration fields for default role in homepage ([#7882](https://github.com/tectonic/the-force/pull/7882)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- Exclude unused areas from export ([#7896](https://github.com/tectonic/the-force/pull/7896)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Entry Form Edit - ConfigurationModeSwitch should be checked by default ([#7848](https://github.com/tectonic/the-force/pull/7848)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Use v-output to correctly render category and chapter entities when changed on the term page ([#7897](https://github.com/tectonic/the-force/pull/7897)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Fix eligibility was not ignoring fields from other categories ([#7886](https://github.com/tectonic/the-force/pull/7886)) [@kontoulis](https://github.com/kontoulis)
- Move error pages to their own layout - fixed after revert ([#7862](https://github.com/tectonic/the-force/pull/7862)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- Skipped sanitising by using HtmlString to safely render falcon tracker code ([#7890](https://github.com/tectonic/the-force/pull/7890)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Added null safe operator on ListAction.vue component ([#7888](https://github.com/tectonic/the-force/pull/7888)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Skipped sanitising by using HtmlString to safely render tracker code ([#7889](https://github.com/tectonic/the-force/pull/7889)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- XSS 3.0 - Add HTML sanitisation to term retrieval process ([#7878](https://github.com/tectonic/the-force/pull/7878)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- The User Emulation action should not be triggered repeatedly ([#7872](https://github.com/tectonic/the-force/pull/7872)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Fixes url attachments displaying as full size in cards ([#7883](https://github.com/tectonic/the-force/pull/7883)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- Disable max input length for HTML sanitiser configuration ([#7880](https://github.com/tectonic/the-force/pull/7880)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- added score set settings to account stub and cast to value object ([#7859](https://github.com/tectonic/the-force/pull/7859)) [@fmarquesto](https://github.com/fmarquesto)
- Bugfix/cant create chapter in config mode ([#7857](https://github.com/tectonic/the-force/pull/7857)) [@fmarquesto](https://github.com/fmarquesto)
- Assignment created merge field directs to the dashboard instead ([#7863](https://github.com/tectonic/the-force/pull/7863)) [@pktharindu](https://github.com/pktharindu)
- Saving entry loads referees with deleted review tasks ([#7865](https://github.com/tectonic/the-force/pull/7865)) [@kontoulis](https://github.com/kontoulis)
- Debounceable webhook error key was null ([#7854](https://github.com/tectonic/the-force/pull/7854)) [@kontoulis](https://github.com/kontoulis)
- Remove debug leftovers ([#7864](https://github.com/tectonic/the-force/pull/7864)) [@think-01](https://github.com/think-01)
- Holocron Tours videos should be visible on Guides and Tours page ([#7847](https://github.com/tectonic/the-force/pull/7847)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Simplify Amount multiplication ([#7828](https://github.com/tectonic/the-force/pull/7828)) [@acarpio89](https://github.com/acarpio89)
- Send notification when Entry is tagged because of an autotag field ([#7838](https://github.com/tectonic/the-force/pull/7838)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Make Leaderboard link visible to Auditors ([#7850](https://github.com/tectonic/the-force/pull/7850)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- Referee fields should be displayed on manager/entrant pdfs ([#7760](https://github.com/tectonic/the-force/pull/7760)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Fix unsubscribe link in broadcasts ([#7849](https://github.com/tectonic/the-force/pull/7849)) [@acarpio89](https://github.com/acarpio89)
- Referees dont show in judging unless there are also associated referee fields with values ([#7370](https://github.com/tectonic/the-force/pull/7370)) [@fmarquesto](https://github.com/fmarquesto)
- Tag models slowness fix ([#7834](https://github.com/tectonic/the-force/pull/7834)) [@kontoulis](https://github.com/kontoulis)
- Fix 500 error when provisioning ([#7846](https://github.com/tectonic/the-force/pull/7846)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- Fix issue with Judge login redirect when multiple Score Sets are available ([#7785](https://github.com/tectonic/the-force/pull/7785)) [@NizarBerjawi](https://github.com/NizarBerjawi)

### 📦 Security and Dependency Updates

- Bump vue-bootstrap ([#7866](https://github.com/tectonic/the-force/pull/7866)) [@fabriciozeferino](https://github.com/fabriciozeferino)

### ✍️ Other Changes

- Remove AWS Elastic Transcoder code ([#7812](https://github.com/tectonic/the-force/pull/7812)) [@NizarBerjawi](https://github.com/NizarBerjawi)

## v4.0.4 - 2025-05-17

### ✨ Features

- [Dashboard OKR] Default PM Dashboard ([#7525](https://github.com/tectonic/the-force/pull/7525)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- Persistent cart ([#7522](https://github.com/tectonic/the-force/pull/7522)) [@acarpio89](https://github.com/acarpio89)
- [Dashboard OKR] Manage Dashboards from Falcon ([#7662](https://github.com/tectonic/the-force/pull/7662)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- A simple value object caster ([#7738](https://github.com/tectonic/the-force/pull/7738)) [@kontoulis](https://github.com/kontoulis)
- [Dashboards OKR] Separate route for default and custom dashboard ([#7786](https://github.com/tectonic/the-force/pull/7786)) [@jc-creativeforce](https://github.com/jc-creativeforce)

### 🛠️ Improvements

- Improve accessibility of dropdown toggles by adding role and tabindex ([#7821](https://github.com/tectonic/the-force/pull/7821)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Lint: Improve accessibility of dropdown toggles by adding role and tabindex ([#7822](https://github.com/tectonic/the-force/pull/7822)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Ensure button is properly marked as disabled for screen readers ([#7819](https://github.com/tectonic/the-force/pull/7819)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- When-creating-a-new-field-and-pressing-the-dropdown-menu-it-moves ([#7820](https://github.com/tectonic/the-force/pull/7820)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)

### 🐛 Bug Fixes

- Disable "undeleting" fields conditional on a deleted field ([#7802](https://github.com/tectonic/the-force/pull/7802)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Bump vue-bootstrap ([#7836](https://github.com/tectonic/the-force/pull/7836)) [@thanosalexandris](https://github.com/thanosalexandris)
- Fixed assignments form wcag ([#7818](https://github.com/tectonic/the-force/pull/7818)) [@thanosalexandris](https://github.com/thanosalexandris)
- Tag models slowness fix base PR ([#7832](https://github.com/tectonic/the-force/pull/7832)) [@kontoulis](https://github.com/kontoulis)
- Error detected during "Manage grant reports" export ([#7798](https://github.com/tectonic/the-force/pull/7798)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Allow EntryItems with NullPrice ([#7831](https://github.com/tectonic/the-force/pull/7831)) [@acarpio89](https://github.com/acarpio89)
- add forced-colors active media for tabs button.active styling ([#7825](https://github.com/tectonic/the-force/pull/7825)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Fix persistent cart amount rounding ([#7826](https://github.com/tectonic/the-force/pull/7826)) [@acarpio89](https://github.com/acarpio89)
- When-creating-a-new-field-and-pressing-the-dropdown-menu-it-moves ([#7820](https://github.com/tectonic/the-force/pull/7820)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Fix cart failing tests ([#7823](https://github.com/tectonic/the-force/pull/7823)) [@acarpio89](https://github.com/acarpio89)
- Entries Export should not throw unhandled exception in case an entry is associated with an non-existent user ([#7776](https://github.com/tectonic/the-force/pull/7776)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Bump: Added terms.descriptions.referee translation  ([#7803](https://github.com/tectonic/the-force/pull/7803)) [@kevinawardforce](https://github.com/kevinawardforce)
- Autosave in ca ([#7424](https://github.com/tectonic/the-force/pull/7424)) [@think-01](https://github.com/think-01)
- Fix failing test  ([#7799](https://github.com/tectonic/the-force/pull/7799)) [@kontoulis](https://github.com/kontoulis)

### 📦 Security and Dependency Updates

- Bump vue-bootstrap ([#7788](https://github.com/tectonic/the-force/pull/7788)) [@thanosalexandris](https://github.com/thanosalexandris)

### ✍️ Other Changes

- Stop local Firebase errors from happening when environment variables are not configured ([#7824](https://github.com/tectonic/the-force/pull/7824)) [@NizarBerjawi](https://github.com/NizarBerjawi)

## v4.0.3 - 2025-05-03

### ✨ Features

- route alias for frontend ([#7750](https://github.com/tectonic/the-force/pull/7750)) [@think-01](https://github.com/think-01)
- Warning message for Good Grants accounts when creating a new season ([#7695](https://github.com/tectonic/the-force/pull/7695)) [@NizarBerjawi](https://github.com/NizarBerjawi)

### 🛠️ Improvements

- Allow tagging from all Judging views ([#7757](https://github.com/tectonic/the-force/pull/7757)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- remove duplicate pageview gtag event ([#7782](https://github.com/tectonic/the-force/pull/7782)) [@fmarquesto](https://github.com/fmarquesto)
- Remove option to reset/reassign review task for referee stages ([#7735](https://github.com/tectonic/the-force/pull/7735)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Cleanup git ignore files ([#7722](https://github.com/tectonic/the-force/pull/7722)) [@kirkbushell](https://github.com/kirkbushell)
- Add entrant email and entrant global id to get entry response ([#7734](https://github.com/tectonic/the-force/pull/7734)) [@NizarBerjawi](https://github.com/NizarBerjawi)

### 🐛 Bug Fixes

- Bugfix: It's difficult to type directly into the date/time field ([#7796](https://github.com/tectonic/the-force/pull/7796)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Revert "Move error pages to their own layout, don't use application layout" ([#7794](https://github.com/tectonic/the-force/pull/7794)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- Bugfix/export exceeding 32k character limit for excel cell ([#7773](https://github.com/tectonic/the-force/pull/7773)) [@fmarquesto](https://github.com/fmarquesto)
- Move error pages to their own layout, don't use application layout ([#7643](https://github.com/tectonic/the-force/pull/7643)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- Update Attachments column and create v2.3 Contributors column with new structure ([#7775](https://github.com/tectonic/the-force/pull/7775)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Block autosaves after sobmit ([#7724](https://github.com/tectonic/the-force/pull/7724)) [@think-01](https://github.com/think-01)
- Make custom columns (except aggregates) in allocation payment columnator sortable ([#7759](https://github.com/tectonic/the-force/pull/7759)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- bugfix/Radio buttons in PDF squashed for excessive text ([#7768](https://github.com/tectonic/the-force/pull/7768)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- bugfix/Primary and secondary button colours not working in Top pick when set to default colour ([#7765](https://github.com/tectonic/the-force/pull/7765)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Bugfix/safari browser issue printing feedback does not work ([#7779](https://github.com/tectonic/the-force/pull/7779)) [@adamorlowski-cf](https://github.com/adamorlowski-cf)
- Referee tab - Resend Request Action not working ([#7752](https://github.com/tectonic/the-force/pull/7752)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Show proper message for review flows with start date in the future ([#7777](https://github.com/tectonic/the-force/pull/7777)) [@nikocucuzza](https://github.com/nikocucuzza)
- Chapter is appearing in invoices for accounts that don't use chapters ([#7771](https://github.com/tectonic/the-force/pull/7771)) [@nikocucuzza](https://github.com/nikocucuzza)
- Bugfix/category export is throwing an error ([#7767](https://github.com/tectonic/the-force/pull/7767)) [@fmarquesto](https://github.com/fmarquesto)
- Bugfix/table row locking non collaboration form 2 ([#7766](https://github.com/tectonic/the-force/pull/7766)) [@think-01](https://github.com/think-01)
- Bugfix/plug transaction guard for mutables ([#7442](https://github.com/tectonic/the-force/pull/7442)) [@think-01](https://github.com/think-01)
- use pathname + search params to build the url to match routes ([#7762](https://github.com/tectonic/the-force/pull/7762)) [@fmarquesto](https://github.com/fmarquesto)
- Chapter managers should be able to change the chapter on an entry even if it is not a chapter they manage ([#7661](https://github.com/tectonic/the-force/pull/7661)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Add entry id and submitted date to Manage Grants and Grant Reports pages ([#7736](https://github.com/tectonic/the-force/pull/7736)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Fix issue with Update Contributor API throwing error when there are conditional fields ([#7747](https://github.com/tectonic/the-force/pull/7747)) [@jc-creativeforce](https://github.com/jc-creativeforce)

### 📦 Security and Dependency Updates

- Security: Fix Dependabot alerts (Axios) ([#7673](https://github.com/tectonic/the-force/pull/7673)) [@fabriciozeferino](https://github.com/fabriciozeferino)

### ✍️ Other Changes

- Collaboration: reverting the value when locked ([#7659](https://github.com/tectonic/the-force/pull/7659)) [@kevinawardforce](https://github.com/kevinawardforce)

## v4.0.2 - 2025-04-19

### ✨ Features

- new pro partner plan ([#7701](https://github.com/tectonic/the-force/pull/7701)) [@fmarquesto](https://github.com/fmarquesto)
- Add grant status to filters in allocation payments ([#7714](https://github.com/tectonic/the-force/pull/7714)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Adds api endpoint for judging leaderboard ([#7604](https://github.com/tectonic/the-force/pull/7604)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- Show cart entry count on cart icon ([#7660](https://github.com/tectonic/the-force/pull/7660)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- Integrate repository `primary()` builder method and replace `find()` ([#7718](https://github.com/tectonic/the-force/pull/7718)) [@kontoulis](https://github.com/kontoulis)

### 🛠️ Improvements

- Updated the Provisioning page background image for Good Grants ([#7737](https://github.com/tectonic/the-force/pull/7737)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Updated the Provisioning page background image for Good Grants (Base) ([#7748](https://github.com/tectonic/the-force/pull/7748)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Words counter should treat hyphen-separated words and URLs as single words ([#7725](https://github.com/tectonic/the-force/pull/7725)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)

### 🐛 Bug Fixes

- Adds check to see if criteria exists before trying to access values ([#7746](https://github.com/tectonic/the-force/pull/7746)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- Revert code for keyboard navigation for accessibility columnator ([#7742](https://github.com/tectonic/the-force/pull/7742)) [@thanosalexandris](https://github.com/thanosalexandris)
- fix: keyboard accessibility access to elements ([#7682](https://github.com/tectonic/the-force/pull/7682)) [@thanosalexandris](https://github.com/thanosalexandris)
- Fix issue where other contributors get deleted when updating a single contributor ([#7705](https://github.com/tectonic/the-force/pull/7705)) [@jc-creativeforce](https://github.com/jc-creativeforce)
- Fixes judging table/scorematrix so status and total score work when filtered by score set ([#7730](https://github.com/tectonic/the-force/pull/7730)) [@CDBuckmaster](https://github.com/CDBuckmaster)
- Rename CVV in NABTransact requests ([#7729](https://github.com/tectonic/the-force/pull/7729)) [@acarpio89](https://github.com/acarpio89)
- reverted #7694 that was reverting #7648 ([#7698](https://github.com/tectonic/the-force/pull/7698)) [@fmarquesto](https://github.com/fmarquesto)
- Eligibility status not being added for one entry ([#7567](https://github.com/tectonic/the-force/pull/7567)) [@nikocucuzza](https://github.com/nikocucuzza)
- Downloads Columnnator should display "User Deleted", unlinked, when requestor is not available ([#7715](https://github.com/tectonic/the-force/pull/7715)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- ApiKeyController's authorization should be related to 'ApiKeys' and not 'Settings' resource. ([#7676](https://github.com/tectonic/the-force/pull/7676)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- normalize string used on multiselect for filtering purpose ([#7691](https://github.com/tectonic/the-force/pull/7691)) [@fmarquesto](https://github.com/fmarquesto)
- Include CVV in NABTransact requests ([#7713](https://github.com/tectonic/the-force/pull/7713)) [@acarpio89](https://github.com/acarpio89)
- fix: add optional to judging label ([#7685](https://github.com/tectonic/the-force/pull/7685)) [@NizarBerjawi](https://github.com/NizarBerjawi)

### ✍️ Other Changes

- Fixes validation highlighting on other tab's fields ([#7551](https://github.com/tectonic/the-force/pull/7551)) [@thanosalexandris](https://github.com/thanosalexandris)
- [DevOps] Update deploy.yml with Fix for eu commit is not changing issue ([#7727](https://github.com/tectonic/the-force/pull/7727)) [@sameeraksc](https://github.com/sameeraksc)
- Added workflow dispatch to update-changelog.yml ([#7719](https://github.com/tectonic/the-force/pull/7719)) [@sameeraksc](https://github.com/sameeraksc)
- Update update-changelog.yml to use token ([#7716](https://github.com/tectonic/the-force/pull/7716)) [@sameeraksc](https://github.com/sameeraksc)

## v4.0.1 - 2025-04-07

### ✨ Features

- PJAX: Refactoring pjax-router.js ([#7245](https://github.com/tectonic/the-force/pull/7245)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- feat: refactoring authenticators module ([#7594](https://github.com/tectonic/the-force/pull/7594)) [@thanosalexandris](https://github.com/thanosalexandris)
- Feature refactor slug builder repository ([#7547](https://github.com/tectonic/the-force/pull/7547)) [@kontoulis](https://github.com/kontoulis)
- PJAX: create adapter for jquery.pjax.js ([#7260](https://github.com/tectonic/the-force/pull/7260)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- PJAX: Fix TabComponent Browser Back/Forward Navigation Issue ([#7541](https://github.com/tectonic/the-force/pull/7541)) [@fabriciozeferino](https://github.com/fabriciozeferino)
- Feature user details grants tab ([#7570](https://github.com/tectonic/the-force/pull/7570)) [@kontoulis](https://github.com/kontoulis)
- Feature: Improve Datepicker Keyboard Accessibility ([#7650](https://github.com/tectonic/the-force/pull/7650)) [@fabriciozeferino](https://github.com/fabriciozeferino)

### 🛠️ Improvements

- Generate developer release notes and changelog ([#6117](https://github.com/tectonic/the-force/pull/6117)) [@pktharindu](https://github.com/pktharindu)

### 🐛 Bug Fixes

- Deleting grant report due notification results in a error when updating due date for existing grant reports ([#7633](https://github.com/tectonic/the-force/pull/7633)) [@pktharindu](https://github.com/pktharindu)
- fix: Entries without categories for Invited users should be printed normally ([#7687](https://github.com/tectonic/the-force/pull/7687)) [@vangelis-giannakosian](https://github.com/vangelis-giannakosian)
- Grant status for 'scheduled' grant reports exports showing as 'in progress' ([#7609](https://github.com/tectonic/the-force/pull/7609)) [@pktharindu](https://github.com/pktharindu)
- Release notes sprint cycle and adjust styling ([#7696](https://github.com/tectonic/the-force/pull/7696)) [@pktharindu](https://github.com/pktharindu)
- fix: replace label with title for field in Auto-score table ([#7680](https://github.com/tectonic/the-force/pull/7680)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- fix: add missing pagination information to the seasons list page ([#7679](https://github.com/tectonic/the-force/pull/7679)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Bugfix/allow zero in allocations ([#7671](https://github.com/tectonic/the-force/pull/7671)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- Make join_link use short URLs ([#7663](https://github.com/tectonic/the-force/pull/7663)) [@NizarBerjawi](https://github.com/NizarBerjawi)
- fix: buttons container alignment on attachements tab ([#7674](https://github.com/tectonic/the-force/pull/7674)) [@thanosalexandris](https://github.com/thanosalexandris)
- Bugfix/contributor export includes all forms and incorrect entries ([#7670](https://github.com/tectonic/the-force/pull/7670)) [@fmarquesto](https://github.com/fmarquesto)
- Bugfix/edit form button disappears on single form accounts ([#7677](https://github.com/tectonic/the-force/pull/7677)) [@fmarquesto](https://github.com/fmarquesto)
- fix ts errors ([#7664](https://github.com/tectonic/the-force/pull/7664)) [@think-01](https://github.com/think-01)

### ✍️ Other Changes

- [DevOps] Change main region to EU for slack command ([#7689](https://github.com/tectonic/the-force/pull/7689)) [@paolaromerod](https://github.com/paolaromerod)
