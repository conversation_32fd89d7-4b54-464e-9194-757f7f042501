module.exports = {
  extends: [
    'standard',
    'plugin:prettier/recommended',
    'plugin:vue/recommended',
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    "plugin:chai-friendly/recommended"
  ],
  parser: 'vue-eslint-parser',
  parserOptions: {
    parser: {
      ts: '@typescript-eslint/parser',
      js: '@typescript-eslint/parser',
      '<template>': 'espree',
    },
  },
  plugins: ['@typescript-eslint', 'import', 'chai-friendly'],
  root: true,
  rules: {
		'no-mixed-spaces-and-tabs': 'off',
    'arrow-body-style': ['error', 'as-needed'],
    'max-len': ['error', { code: 160, ignoreTemplateLiterals: true }],

    'no-unused-vars': ['error', { vars: 'all', args: 'after-used', ignoreRestSiblings: true }],
    'prettier/prettier': ['error', {
      tabWidth: 1,
      useTabs: true,
      singleQuote: true,
      printWidth: 120,
      arrowParens: 'always'
    }],
    'no-useless-constructor': 'off',
    'vue/multi-word-component-names': 'off',
    'vue/html-self-closing': 'off',
    'vue/max-attributes-per-line': 'off',
    'vue/no-template-shadow': 'error',
    'vue/no-unused-components': 'off',
    'vue/no-use-v-if-with-v-for': 'error',
    'vue/no-v-html': 'error',
    'vue/singleline-html-element-content-newline': 'off',
    'vue/no-mutating-props': 'error',
    'vue/html-closing-bracket-newline': 'error',
    'vue/html-indent': 'off',
    'no-case-declarations': 'error',
    'vue/no-lone-template': 'off',
    'vue/no-parsing-error': 'error',
    'vue/no-useless-template-attributes': 'error',
    'vue/valid-v-slot': 'error',
    'vue/order-in-components': 'off',

    'no-new': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    '@typescript-eslint/no-var-requires': 'off',

    'no-prototype-builtins': 'error',
    '@typescript-eslint/no-this-alias': 'error',
    'no-empty-function': 'off',
    '@typescript-eslint/no-empty-function': ['error', { allow: ['arrowFunctions'] }],
    '@typescript-eslint/naming-convention': [
      'error',
      { selector: 'variableLike', format: ['camelCase'] },
      { selector: 'property', format: ['camelCase'] },
      { selector: 'typeLike', format: ['PascalCase'] },
      { selector: 'typeAlias', format: ['PascalCase'], custom: { regex: '^[TI][A-Z]', match: false } },
      { selector: 'typeAlias', format: ['PascalCase'], custom: { regex: '^.*(Class|Type|Interface)$', match: false } },
      { selector: 'interface', format: ['PascalCase'], custom: { regex: '^[TI][A-Z]', match: false } },
      { selector: 'interface', format: ['PascalCase'], custom: { regex: '^.*(Class|Type|Interface)$', match: false } },
      { selector: 'class', format: ['PascalCase'], custom: { regex: '^.*(Class|Type|Interface)$', match: false } },
      { selector: 'enumMember', format: ['StrictPascalCase', 'UPPER_CASE'] },
      { selector: 'objectLiteralProperty', format: null },
    ],
    'brace-style': 'off',
    '@typescript-eslint/brace-style': ['error', '1tbs', { allowSingleLine: true }],
    'keyword-spacing': 'off',
    '@typescript-eslint/keyword-spacing': 'error',
    '@typescript-eslint/type-annotation-spacing': 'error',
    quotes: 'off',
    '@typescript-eslint/quotes': ['error', 'single', { avoidEscape: true }],
    semi: 'off',
    '@typescript-eslint/semi': ['error', 'always', { omitLastInOneLineBlock: true }],
    '@typescript-eslint/array-type': 'error',
    'padding-line-between-statements': 'off',
    '@typescript-eslint/padding-line-between-statements': [
      'error',
      { blankLine: 'always', prev: 'block-like', next: '*' },
      { blankLine: 'any', prev: 'import', next: 'import' },
    ],
    'import/imports-first': 'error',
    'import/exports-last': 'error',
    'import/group-exports': 'error',
    'sort-imports': [
      'error',
      {
        allowSeparatedGroups: true,
        ignoreCase: true,
        ignoreMemberSort: false,
        memberSyntaxSortOrder: ['none', 'all', 'single', 'multiple'],
      },
    ],
    'no-console': ['error', { allow: ['error', 'warn'] }],
    'no-restricted-imports': [
      'error',
      {
        patterns: ['./../../', '../../../', './../../../', '../../../../', './../../../../']
      },
    ],
    'space-before-function-paren': [
      'error',
      {
        anonymous: 'always',
        named: 'never',
        asyncArrow: 'always'
      }
    ],
  },
  overrides: [
    {
      files: ['*.vue'],
      rules: {
        'import/first': 'off',
        'import/imports-first': 'off',
        'import/exports-last': 'off',
        'import/group-exports': 'off',
        '@typescript-eslint/naming-convention': ['error', { selector: 'objectLiteralProperty', format: null }],
        'sort-imports': 'off',
        'max-len': ['error', { code: 200, ignoreTemplateLiterals: true, ignoreUrls: true }],
      },
    },
    {
        files: 'resources/assets/js/tests/**/*.js',
        rules: {
            'no-unused-expressions': 'off',
            'chai-friendly/no-unused-expressions': 'off',
        },
    }
  ],
  globals: {
    Route: 'readonly',
    App: 'readonly',
    $: 'readonly',
    currentBreadcrumb: 'readonly',
    entryCompletionChartData: true,
    entryVolumeChartData: true,
    uploaderOptions: true
  },
	ignorePatterns: [
    'resources/assets/js/src/vendor/**',
    'resources/assets/js/src/modules/test/**',
    '**/*.json'
  ],
	env: {
		mocha: true,
	},
};
